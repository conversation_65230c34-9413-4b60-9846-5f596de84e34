-- DBReeder Database Schema
-- PostgreSQL implementation for the application database

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for better type safety
CREATE TYPE user_role AS ENUM ('special_admin', 'admin', 'dba', 'sql_approver', 'sql_user');
CREATE TYPE approval_status AS ENUM ('pending', 'approved', 'rejected', 'expired');
CREATE TYPE sql_command_type AS ENUM ('DQL', 'DML', 'DDL', 'DCL', 'TCL');
CREATE TYPE audit_event_type AS ENUM ('login', 'logout', 'sql_execute', 'sql_approve', 'sql_reject', 'user_create', 'user_update', 'team_create', 'permission_change');

-- Teams table - Multi-tenant isolation
CREATE TABLE teams (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    password_changed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Security constraints
    CONSTRAINT username_length CHECK (LENGTH(username) >= 3),
    CONSTRAINT email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- User-Team relationships with roles
CREATE TABLE user_team_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
    role user_role NOT NULL,
    granted_by UUID REFERENCES users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    
    -- Ensure unique user-team combination
    UNIQUE(user_id, team_id)
);

-- Database connections managed by teams
CREATE TABLE database_connections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    team_id UUID NOT NULL REFERENCES teams(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    database_type VARCHAR(50) NOT NULL, -- postgresql, mysql, sqlite
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    database_name VARCHAR(255) NOT NULL,
    username VARCHAR(255) NOT NULL,
    password_encrypted TEXT NOT NULL, -- Encrypted connection password
    ssl_enabled BOOLEAN DEFAULT true,
    connection_params JSONB, -- Additional connection parameters
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure unique connection name per team
    UNIQUE(team_id, name)
);

-- SQL approval requests
CREATE TABLE sql_approval_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    requester_id UUID NOT NULL REFERENCES users(id),
    team_id UUID NOT NULL REFERENCES teams(id),
    database_connection_id UUID NOT NULL REFERENCES database_connections(id),
    sql_command TEXT NOT NULL,
    command_type sql_command_type NOT NULL,
    justification TEXT,
    status approval_status DEFAULT 'pending',
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT valid_approval CHECK (
        (status = 'approved' AND approved_by IS NOT NULL AND approved_at IS NOT NULL) OR
        (status != 'approved')
    )
);

-- Comprehensive audit log
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    team_id UUID REFERENCES teams(id),
    event_type audit_event_type NOT NULL,
    resource_type VARCHAR(100), -- table, user, team, etc.
    resource_id VARCHAR(255), -- ID of the affected resource
    sql_command TEXT, -- For SQL-related events
    command_type sql_command_type, -- For SQL commands
    database_connection_id UUID REFERENCES database_connections(id),
    execution_time_ms INTEGER, -- Query execution time
    rows_affected INTEGER, -- Number of rows affected
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    additional_data JSONB, -- Flexible additional information
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Index for performance
    INDEX idx_audit_logs_user_id (user_id),
    INDEX idx_audit_logs_team_id (team_id),
    INDEX idx_audit_logs_event_type (event_type),
    INDEX idx_audit_logs_created_at (created_at),
    INDEX idx_audit_logs_sql_command (sql_command) WHERE sql_command IS NOT NULL
);

-- User sessions for JWT token management
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL UNIQUE, -- Hashed JWT token
    refresh_token_hash VARCHAR(255) UNIQUE,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- System configuration
CREATE TABLE system_config (
    key VARCHAR(255) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_user_team_roles_user_id ON user_team_roles(user_id);
CREATE INDEX idx_user_team_roles_team_id ON user_team_roles(team_id);
CREATE INDEX idx_user_team_roles_role ON user_team_roles(role);
CREATE INDEX idx_database_connections_team_id ON database_connections(team_id);
CREATE INDEX idx_sql_approval_requests_requester_id ON sql_approval_requests(requester_id);
CREATE INDEX idx_sql_approval_requests_status ON sql_approval_requests(status);
CREATE INDEX idx_sql_approval_requests_team_id ON sql_approval_requests(team_id);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_teams_updated_at BEFORE UPDATE ON teams
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_database_connections_updated_at BEFORE UPDATE ON database_connections
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sql_approval_requests_updated_at BEFORE UPDATE ON sql_approval_requests
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system configuration
INSERT INTO system_config (key, value, description) VALUES
('max_approval_time_hours', '24', 'Maximum time in hours for SQL approval requests'),
('max_sql_length', '10000', 'Maximum length of SQL commands'),
('session_timeout_hours', '24', 'User session timeout in hours'),
('rate_limit_per_minute', '100', 'API rate limit per minute per user'),
('audit_retention_days', '365', 'Number of days to retain audit logs');

-- Create a view for active user permissions
CREATE VIEW user_permissions AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    t.id as team_id,
    t.name as team_name,
    utr.role,
    utr.granted_at,
    utr.granted_by
FROM users u
JOIN user_team_roles utr ON u.id = utr.user_id
JOIN teams t ON utr.team_id = t.id
WHERE u.is_active = true 
  AND utr.is_active = true 
  AND t.is_active = true;
