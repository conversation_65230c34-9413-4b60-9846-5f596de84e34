# DBReeder System Architecture

## Overview

DBReeder is a secure database management system that acts as a proxy between developers and database servers, providing role-based access control, SQL command auditing, and approval workflows.

## Core Principles

1. **Zero Trust Security**: No direct database access
2. **Comprehensive Auditing**: Every SQL command is logged
3. **Role-Based Access**: Strict permission hierarchy
4. **Team Isolation**: Multi-tenant architecture
5. **Approval Workflows**: Critical operations require approval

## System Architecture

### High-Level Architecture

```
┌─────────────────┐
│   Web Client    │ (React/Vue Frontend)
└─────────┬───────┘
          │ HTTPS/WSS
┌─────────▼───────┐
│   API Gateway   │ (Express.js + Security Middleware)
│   - Rate Limiting
│   - Authentication
│   - Request Validation
└─────────┬───────┘
          │
┌─────────▼───────┐
│  Core Services  │
│ ┌─────────────┐ │
│ │Auth Service │ │ (JWT + RBAC)
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │SQL Service  │ │ (Parser + Executor)
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │Audit Service│ │ (Logging + Analytics)
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │Team Service │ │ (Multi-tenancy)
│ └─────────────┘ │
└─────────┬───────┘
          │
┌─────────▼───────┐
│  Data Layer     │
│ ┌─────────────┐ │
│ │App Database │ │ (PostgreSQL - Users, Teams, Audit)
│ └─────────────┘ │
│ ┌─────────────┐ │
│ │Target DBs   │ │ (PostgreSQL, MySQL, SQLite)
│ └─────────────┘ │
└─────────────────┘
```

### Component Details

#### 1. API Gateway Layer
- **Express.js** with TypeScript
- **Security Middleware**: Helmet, CORS, Rate Limiting
- **Authentication**: JWT-based with refresh tokens
- **Request Validation**: Joi schemas
- **Error Handling**: Centralized error management

#### 2. Core Services

##### Authentication Service
- User registration and login
- JWT token management
- Password hashing (bcrypt)
- Session management

##### Authorization Service (RBAC)
- Role-based permission checking
- Team-based access control
- Permission inheritance
- Dynamic permission evaluation

##### SQL Processing Service
- SQL parsing and validation
- Command classification (DQL/DML/DDL)
- Security validation
- Query execution management

##### Audit Service
- Real-time logging
- Performance metrics
- Security event tracking
- Compliance reporting

##### Team Management Service
- Multi-tenant isolation
- Team-based database access
- User-team relationships
- Team-specific configurations

#### 3. Data Layer

##### Application Database (PostgreSQL)
- User management
- Team and role definitions
- Audit logs
- Approval workflows
- System configuration

##### Target Databases
- PostgreSQL, MySQL, SQLite support
- Connection pooling
- Transaction management
- Query result caching

## Security Architecture

### Authentication Flow
1. User login with credentials
2. JWT token generation with role claims
3. Token validation on each request
4. Automatic token refresh

### Authorization Flow
1. Extract user context from JWT
2. Validate team membership
3. Check role permissions
4. Evaluate resource access rights

### SQL Execution Flow
1. Parse and validate SQL command
2. Classify command type (DQL/DML/DDL)
3. Check user permissions
4. Apply approval workflow if required
5. Execute command with audit logging
6. Return results with metadata

## Data Flow Diagrams

### SQL Command Execution Flow
```
User Request → Authentication → Authorization → SQL Parsing → 
Permission Check → Approval Workflow → Execution → Audit Log → Response
```

### Approval Workflow
```
DML/DDL Request → Queue for Approval → Notify Approvers → 
Approval Decision → Execute/Reject → Audit Log → Notify User
```

## Scalability Considerations

1. **Horizontal Scaling**: Stateless services with load balancing
2. **Database Sharding**: Team-based data partitioning
3. **Caching**: Redis for session and query result caching
4. **Connection Pooling**: Efficient database connection management
5. **Async Processing**: Queue-based approval workflows

## Security Measures

1. **Input Validation**: All inputs validated and sanitized
2. **SQL Injection Prevention**: Parameterized queries only
3. **Rate Limiting**: API endpoint protection
4. **Audit Logging**: Immutable audit trail
5. **Encryption**: Data at rest and in transit
6. **Secret Management**: Environment-based configuration

## Monitoring & Observability

1. **Application Metrics**: Performance and usage statistics
2. **Security Monitoring**: Failed authentication attempts
3. **Audit Analytics**: SQL command patterns and trends
4. **Health Checks**: Service availability monitoring
5. **Error Tracking**: Centralized error logging
