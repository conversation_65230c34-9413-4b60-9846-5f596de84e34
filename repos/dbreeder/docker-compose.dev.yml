version: '3.8'

services:
  # Development Database (PostgreSQL)
  postgres-dev:
    image: postgres:15-alpine
    container_name: dbreeder-postgres-dev
    environment:
      POSTGRES_DB: dbreeder_dev
      POSTGRES_USER: dbreeder_dev
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5434:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./docs/database-schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
    networks:
      - dbreeder-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dbreeder_dev -d dbreeder_dev"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for development
  redis-dev:
    image: redis:7-alpine
    container_name: dbreeder-redis-dev
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - dbreeder-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Development Target Databases
  mysql-target-dev:
    image: mysql:8.0
    container_name: dbreeder-mysql-target-dev
    environment:
      MYSQL_ROOT_PASSWORD: mysql_dev_password
      MYSQL_DATABASE: target_dev_db
      MYSQL_USER: target_dev_user
      MYSQL_PASSWORD: target_dev_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./scripts/mysql-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - dbreeder-dev-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres-target-dev:
    image: postgres:15-alpine
    container_name: dbreeder-postgres-target-dev
    environment:
      POSTGRES_DB: target_dev_db
      POSTGRES_USER: target_dev_user
      POSTGRES_PASSWORD: target_dev_password
    ports:
      - "5435:5432"
    volumes:
      - postgres_target_dev_data:/var/lib/postgresql/data
      - ./scripts/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - dbreeder-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U target_dev_user -d target_dev_db"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  mysql_dev_data:
    driver: local
  postgres_target_dev_data:
    driver: local

networks:
  dbreeder-dev-network:
    driver: bridge
