!function(t,r){if("object"==typeof exports&&"object"==typeof module)module.exports=r();else if("function"==typeof define&&define.amd)define([],r);else{var e=r();for(var n in e)("object"==typeof exports?exports:t)[n]=e[n]}}(this,(function(){return function(t){var r={};function e(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=t,e.c=r,e.d=function(t,r,n){e.o(t,r)||Object.defineProperty(t,r,{enumerable:!0,get:n})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,r){if(1&r&&(t=e(t)),8&r)return t;if(4&r&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&r&&"string"!=typeof t)for(var o in t)e.d(n,o,function(r){return t[r]}.bind(null,o));return n},e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,"a",r),r},e.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)},e.p="",e(e.s=25)}([function(t,r,e){"use strict";e.r(r),e.d(r,"arrayStructTypeToSQL",(function(){return C})),e.d(r,"autoIncrementToSQL",(function(){return R})),e.d(r,"columnOrderListToSQL",(function(){return I})),e.d(r,"commonKeywordArgsToSQL",(function(){return g})),e.d(r,"commonOptionConnector",(function(){return i})),e.d(r,"connector",(function(){return s})),e.d(r,"commonTypeValue",(function(){return y})),e.d(r,"commentToSQL",(function(){return T})),e.d(r,"createBinaryExpr",(function(){return l})),e.d(r,"createValueExpr",(function(){return c})),e.d(r,"dataTypeToSQL",(function(){return L})),e.d(r,"DEFAULT_OPT",(function(){return a})),e.d(r,"escape",(function(){return f})),e.d(r,"literalToSQL",(function(){return E})),e.d(r,"columnIdentifierToSql",(function(){return h})),e.d(r,"getParserOpt",(function(){return p})),e.d(r,"identifierToSql",(function(){return d})),e.d(r,"onPartitionsToSQL",(function(){return w})),e.d(r,"replaceParams",(function(){return j})),e.d(r,"returningToSQL",(function(){return A})),e.d(r,"hasVal",(function(){return m})),e.d(r,"setParserOpt",(function(){return b})),e.d(r,"toUpper",(function(){return O})),e.d(r,"topToSQL",(function(){return v})),e.d(r,"triggerEventToSQL",(function(){return S}));var n=e(3);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var a={database:"flinksql",type:"table",parseOptions:{}},u=a;function i(t,r,e){if(e)return t?"".concat(t.toUpperCase()," ").concat(r(e)):r(e)}function s(t,r){if(r)return"".concat(t.toUpperCase()," ").concat(r)}function c(t){var r=o(t);if(Array.isArray(t))return{type:"expr_list",value:t.map(c)};if(null===t)return{type:"null",value:null};switch(r){case"boolean":return{type:"bool",value:t};case"string":return{type:"string",value:t};case"number":return{type:"number",value:t};default:throw new Error('Cannot convert value "'.concat(r,'" to SQL'))}}function l(t,r,e){var n={operator:t,type:"binary_expr"};return n.left=r.type?r:c(r),"BETWEEN"===t||"NOT BETWEEN"===t?(n.right={type:"expr_list",value:[c(e[0]),c(e[1])]},n):(n.right=e.type?e:c(e),n)}function f(t){return t}function p(){return u}function b(t){u=t}function v(t){if(t){var r=t.value,e=t.percent,n=t.parentheses?"(".concat(r,")"):r,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function h(t){var r=p().database;if(t)switch(r&&r.toLowerCase()){case"postgresql":case"db2":case"snowflake":case"noql":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(t,"`")}}function d(t,r){var e=p().database;if(!0===r)return"'".concat(t,"'");if(t){if("*"===t)return t;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":case"sqlite":return"`".concat(t,"`");case"postgresql":case"snowflake":case"noql":return'"'.concat(t,'"');case"transactsql":return"[".concat(t,"]");case"bigquery":case"db2":return t;default:return"`".concat(t,"`")}}}function y(t){var r=[];if(!t)return r;var e=t.type,n=t.symbol,o=t.value;return r.push(e.toUpperCase()),n&&r.push(n),r.push(o.toUpperCase()),r}function O(t){if(t)return t.toUpperCase()}function m(t){return t}function E(t){if(t){var r=t.prefix,e=t.type,n=t.parentheses,a=t.suffix,u=t.value,i="string"==typeof t?t:u;switch(e){case"backticks_quote_string":i="`".concat(u,"`");break;case"string":i="'".concat(u,"'");break;case"regex_string":i='r"'.concat(u,'"');break;case"hex_string":i="X'".concat(u,"'");break;case"full_hex_string":i="0x".concat(u);break;case"natural_string":i="N'".concat(u,"'");break;case"bit_string":i="b'".concat(u,"'");break;case"double_quote_string":i='"'.concat(u,'"');break;case"single_quote_string":i="'".concat(u,"'");break;case"boolean":case"bool":i=u?"TRUE":"FALSE";break;case"null":i="NULL";break;case"star":i="*";break;case"param":i="".concat(r||":").concat(u),r=null;break;case"origin":i=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":i="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":i="N'".concat(u,"'")}var s=[];return r&&s.push(O(r)),s.push(i),a&&s.push("object"===o(a)&&a.collate?y(a.collate).join(" "):O(a)),i=s.join(" "),n?"(".concat(i,")"):i}}function j(t,r){return function t(r,e){return Object.keys(r).filter((function(t){var e=r[t];return Array.isArray(e)||"object"===o(e)&&null!==e})).forEach((function(n){var a=r[n];if("object"!==o(a)||"param"!==a.type)return t(a,e);if(void 0===e[a.value])throw new Error("no value for parameter :".concat(a.value," found"));return r[n]=c(e[a.value]),null})),r}(JSON.parse(JSON.stringify(t)),r)}function w(t){var r=t.type,e=t.partitions;return[O(r),"(".concat(e.map((function(t){if("range"!==t.type)return E(t);var r=t.start,e=t.end,n=t.symbol;return"".concat(E(r)," ").concat(O(n)," ").concat(E(e))})).join(", "),")")].join(" ")}function L(t){var r=t.dataType,e=t.length,n=t.parentheses,o=t.scale,a=t.suffix,u="";return null!=e&&(u=o?"".concat(e,", ").concat(o):e),n&&(u="(".concat(u,")")),a&&a.length&&(u+=" ".concat(a.join(" "))),"".concat(r).concat(u)}function C(t){if(t){var r=t.dataType,e=t.definition,n=t.anglebracket,o=O(r);if("ARRAY"!==o&&"STRUCT"!==o)return o;var a=e&&e.map((function(t){return[t.field_name,C(t.field_type)].filter(m).join(" ")})).join(", ");return n?"".concat(o,"<").concat(a,">"):"".concat(o," ").concat(a)}}function T(t){if(t){var r=[],e=t.keyword,n=t.symbol,o=t.value;return r.push(e.toUpperCase()),n&&r.push(n),r.push(E(o)),r.join(" ")}}function S(t){return t.map((function(t){var r=t.keyword,e=t.args,o=[O(r)];if(e){var a=e.keyword,u=e.columns;o.push(O(a),u.map(n.d).join(", "))}return o.join(" ")})).join(" OR ")}function A(t){return t?["RETURNING",t.columns.map(n.f).filter(m).join(", ")].join(" "):""}function g(t){return t?[O(t.keyword),O(t.args)]:[]}function R(t){if(t){if("string"==typeof t){var r=p().database;switch(r&&r.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=t.keyword,n=t.seed,o=t.increment,a=t.parentheses,u=O(e);return a&&(u+="(".concat(E(n),", ").concat(E(o),")")),u}}function I(t){if(t)return t.map(n.c).filter(m).join(", ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return w})),e.d(r,"b",(function(){return L})),e.d(r,"d",(function(){return j})),e.d(r,"c",(function(){return C}));var n=e(0),o=e(8),a=e(12);var u=e(21),i=e(19);var s=e(3),c=e(6),l=e(20);var f=e(10),p=e(22);function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t){var r=t.expr_list,e=t.type;switch(Object(n.toUpper)(e)){case"STRUCT":return"(".concat(Object(s.g)(r),")");case"ARRAY":return function(t){var r=t.array_path,e=t.brackets,o=t.expr_list,a=t.parentheses;if(!o)return"[".concat(Object(s.g)(r),"]");var u=Array.isArray(o)?o.map((function(t){return"(".concat(Object(s.g)(t),")")})).filter(n.hasVal).join(", "):w(o);return e?"[".concat(u,"]"):a?"(".concat(u,")"):u}(t);default:return""}}function h(t){var r=t.definition,e=t.keyword,o=[Object(n.toUpper)(e)];return r&&"object"===b(r)&&(o.length=0,o.push(Object(n.arrayStructTypeToSQL)(r))),o.push(v(t)),o.filter(n.hasVal).join("")}var d=e(2),y=e(5),O=e(17);function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var E={alter:o.b,aggr_func:function(t){var r=t.args,e=t.filter,o=t.over,u=t.within_group_orderby,i=w(r.expr),s=t.name,c=Object(a.a)(o);r.distinct&&(i=["DISTINCT",i].join(" ")),r.orderby&&(i="".concat(i," ").concat(C(r.orderby,"order by"))),r.separator&&(i=[i,Object(n.toUpper)(r.separator.keyword),Object(n.literalToSQL)(r.separator.value)].filter(n.hasVal).join(" "));var l=u?"WITHIN GROUP (".concat(C(u,"order by"),")"):"",f=e?"FILTER (WHERE ".concat(w(e.where),")"):"";return["".concat(s,"(").concat(i,")"),l,c,f].filter(n.hasVal).join(" ")},any_value:c.a,window_func:O.c,array:h,assign:u.a,binary_expr:i.a,case:function(t){var r=["CASE"],e=t.args,n=t.expr,o=t.parentheses;n&&r.push(w(n));for(var a=0,u=e.length;a<u;++a)r.push(e[a].type.toUpperCase()),e[a].cond&&(r.push(w(e[a].cond)),r.push("THEN")),r.push(w(e[a].result));return r.push("END"),o?"(".concat(r.join(" "),")"):r.join(" ")},cast:c.b,column_ref:s.d,column_definition:s.b,datatype:n.dataTypeToSQL,extract:c.c,flatten:c.d,fulltext_search:s.h,function:c.e,insert:y.b,interval:l.a,json:function(t){var r=t.keyword,e=t.expr_list;return[Object(n.toUpper)(r),e.map((function(t){return w(t)})).join(", ")].join(" ")},show:p.a,struct:h,tablefunc:c.f,tables:d.c,unnest:d.d,window:O.b};function j(t){var r=t.prefix,e=void 0===r?"@":r,o=t.name,a=t.members,u=t.keyword,i=t.quoted,s=t.suffix,c=[];u&&c.push(u);var l=a&&a.length>0?"".concat(o,".").concat(a.join(".")):o,f="".concat(e||"").concat(l);return s&&(f+=s),c.push(f),[i,c.join(" "),i].filter(n.hasVal).join("")}function w(t){if(t){var r=t;if(t.ast){var e=r.ast;Reflect.deleteProperty(r,e);for(var o=0,a=Object.keys(e);o<a.length;o++){var u=a[o];r[u]=e[u]}}return E[r.type]?E[r.type](r):Object(n.literalToSQL)(r)}}function L(t){return t?t.map(w):[]}function C(t,r){if(!Array.isArray(t))return"";var e=[],o=Object(n.toUpper)(r);switch(o){case"ORDER BY":e=t.map((function(t){return[w(t.expr),t.type||"ASC",Object(n.toUpper)(t.nulls)].filter(n.hasVal).join(" ")}));break;case"PARTITION BY":default:e=t.map((function(t){return w(t.expr)}))}return Object(n.connector)(o,e.join(", "))}E.var=j,E.expr_list=function(t){var r=L(t.value);return t.parentheses?"(".concat(r.join(", "),")"):r},E.select=function(t){var r="object"===m(t._next)?Object(y.b)(t):Object(f.a)(t);return t.parentheses?"(".concat(r,")"):r},E.unary_expr=function(t){var r=t.operator,e=t.parentheses,n=t.expr,o="-"===r||"+"===r||"~"===r||"!"===r?"":" ",a="".concat(r).concat(o).concat(w(n));return e?"(".concat(a,")"):a}},function(t,r,e){"use strict";e.d(r,"c",(function(){return b})),e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return p})),e.d(r,"d",(function(){return c}));var n=e(19),o=e(3),a=e(1),u=e(18),i=e(20),s=e(0);function c(t){var r=t.type,e=t.as,n=t.expr,o=t.with_offset;return["".concat(Object(s.toUpper)(r),"(").concat(n&&Object(a.a)(n)||"",")"),Object(s.commonOptionConnector)("AS",s.identifierToSql,e),Object(s.commonOptionConnector)(Object(s.toUpper)(o&&o.keyword),s.identifierToSql,o&&o.as)].filter(s.hasVal).join(" ")}function l(t){if(t)switch(t.type){case"pivot":case"unpivot":return function(t){var r=t.as,e=t.column,u=t.expr,i=t.in_expr,c=t.type,l=[Object(a.a)(u),"FOR",Object(o.d)(e),Object(n.a)(i)],f=["".concat(Object(s.toUpper)(c),"(").concat(l.join(" "),")")];return r&&f.push("AS",Object(s.identifierToSql)(r)),f.join(" ")}(t);default:return""}}function f(t){if(t){var r=t.keyword,e=t.expr,n=t.index,o=t.index_columns,u=t.parentheses,i=t.prefix,c=[];switch(r.toLowerCase()){case"forceseek":c.push(Object(s.toUpper)(r),"(".concat(Object(s.identifierToSql)(n)),"(".concat(o.map(a.a).filter(s.hasVal).join(", "),"))"));break;case"spatial_window_max_cells":c.push(Object(s.toUpper)(r),"=",Object(a.a)(e));break;case"index":c.push(Object(s.toUpper)(i),Object(s.toUpper)(r),u?"(".concat(e.map(s.identifierToSql).join(", "),")"):"= ".concat(Object(s.identifierToSql)(e)));break;default:c.push(Object(a.a)(e))}return c.filter(s.hasVal).join(" ")}}function p(t){if("UNNEST"===Object(s.toUpper)(t.type))return c(t);var r=t.table,e=t.db,n=t.as,p=t.expr,b=t.operator,v=t.prefix,h=t.schema,d=t.server,y=t.tablesample,O=t.table_hint,m=Object(s.identifierToSql)(d),E=Object(s.identifierToSql)(e),j=Object(s.identifierToSql)(h),w=r&&Object(s.identifierToSql)(r);if(p)switch(p.type){case"values":var L=p.parentheses,C=p.values,T=p.prefix,S=[L&&"(","",L&&")"],A=Object(u.b)(C);T&&(A=A.split("(").slice(1).map((function(t){return"".concat(Object(s.toUpper)(T),"(").concat(t)})).join("")),S[1]="VALUES ".concat(A),w=S.filter(s.hasVal).join("");break;case"tumble":w=function(t){if(!t)return"";var r=t.data,e=t.timecol,n=t.size;return["TABLE(TUMBLE(TABLE",[Object(s.identifierToSql)(r.db),Object(s.identifierToSql)(r.table)].filter(s.hasVal).join("."),"DESCRIPTOR(".concat(Object(o.d)(e),")"),"".concat(Object(i.a)(n),"))")].filter(s.hasVal).join(" ")}(p);break;default:w=Object(a.a)(p)}var g=[m,E,j,w=[Object(s.toUpper)(v),w].filter(s.hasVal).join(" ")].filter(s.hasVal).join(".");t.parentheses&&(g="(".concat(g,")"));var R=[g];if(y){var I=["TABLESAMPLE",Object(a.a)(y.expr),Object(s.literalToSQL)(y.repeatable)].filter(s.hasVal).join(" ");R.push(I)}return R.push(Object(s.commonOptionConnector)("AS",s.identifierToSql,n),l(b)),O&&R.push(Object(s.toUpper)(O.keyword),"(".concat(O.expr.map(f).filter(s.hasVal).join(", "),")")),R.filter(s.hasVal).join(" ")}function b(t){if(!t)return"";if(!Array.isArray(t)){var r=t.expr,e=t.parentheses,n=b(r);return e?"(".concat(n,")"):n}var o=t[0],u=[];if("dual"===o.type)return"DUAL";u.push(p(o));for(var i=1;i<t.length;++i){var c=t[i],l=c.on,f=c.using,v=c.join,h=[];h.push(v?" ".concat(Object(s.toUpper)(v)):","),h.push(p(c)),h.push(Object(s.commonOptionConnector)("ON",a.a,l)),f&&h.push("USING (".concat(f.map(s.identifierToSql).join(", "),")")),u.push(h.filter(s.hasVal).join(" "))}return u.filter(s.hasVal).join("")}function v(t){var r=t.keyword,e=t.symbol,n=t.value,o=[r.toUpperCase()];e&&o.push(e);var u=n;switch(r){case"partition by":case"default collate":u=Object(a.a)(n);break;case"options":u="(".concat(n.map((function(t){return[t.keyword,t.symbol,Object(a.a)(t.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(a.a).join(", ")}return o.push(u),o.join(" ")}},function(t,r,e){"use strict";e.d(r,"b",(function(){return v})),e.d(r,"d",(function(){return l})),e.d(r,"f",(function(){return y})),e.d(r,"g",(function(){return O})),e.d(r,"a",(function(){return f})),e.d(r,"c",(function(){return b})),e.d(r,"e",(function(){return p})),e.d(r,"h",(function(){return d}));var n=e(16),o=e(1),a=e(6),u=e(2),i=e(0);function s(t){return function(t){if(Array.isArray(t))return c(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(!t)return;if("string"==typeof t)return c(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(t);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return c(t,r)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function l(t){var r=t.array_index,e=t.arrows,n=void 0===e?[]:e,a=t.as,u=t.collate,c=t.column,l=t.db,f=t.isDual,p=t.schema,b=t.table,v=t.parentheses,h=t.properties,d=t.suffix,y=t.order_by,O=t.subFields,m=void 0===O?[]:O,E="*"===c?"*":function(t,r){if("string"==typeof t)return Object(i.identifierToSql)(t,r);var e=t.expr,n=t.offset,a=t.suffix,u=n&&n.map((function(t){return["[",t.name,"".concat(t.name?"(":""),Object(i.literalToSQL)(t.value),"".concat(t.name?")":""),"]"].filter(i.hasVal).join("")})).join("");return[Object(o.a)(e),u,a].filter(i.hasVal).join("")}(c,f),j=[p,l,b].filter(i.hasVal).map((function(t){return"".concat(Object(i.identifierToSql)(t))})).join(".");j&&(E="".concat(j,".").concat(E)),r&&(E="".concat(E,"[").concat(Object(i.literalToSQL)(r.index),"]"),r.property&&(E="".concat(E,".").concat(Object(i.literalToSQL)(r.property))));var w=[E=[E].concat(s(m)).join("."),Object(i.commonOptionConnector)("AS",o.a,a),n.map((function(t,r){return Object(i.commonOptionConnector)(t,i.literalToSQL,h[r])})).join(" ")];u&&w.push(Object(i.commonTypeValue)(u).join(" ")),w.push(Object(i.toUpper)(d)),w.push(Object(i.toUpper)(y));var L=w.filter(i.hasVal).join(" ");return v?"(".concat(L,")"):L}function f(t){var r=t||{},e=r.dataType,n=r.length,a=r.suffix,u=r.scale,i=r.expr,s=e;return null!=n&&(s+="(".concat([n,u].filter((function(t){return null!=t})).join(", "),")")),a&&a.length&&(s+=" ".concat(a.join(" "))),i&&(s+=Object(o.a)(i)),s}function p(t){var r=[];if(!t)return r;var e=t.definition,n=t.keyword,a=t.match,s=t.table,c=t.on_action;return r.push(Object(i.toUpper)(n)),r.push(Object(u.c)(s)),r.push(e&&"(".concat(e.map((function(t){return Object(o.a)(t)})).join(", "),")")),r.push(Object(i.toUpper)(a)),c.map((function(t){return r.push(Object(i.toUpper)(t.type),Object(o.a)(t.value))})),r.filter(i.hasVal)}function b(t){var r=t.column,e=t.collate,n=t.nulls,a=t.opclass,u=t.order_by;return[Object(o.a)("string"==typeof r?{type:"column_ref",table:t.table,column:r}:t),Object(i.commonOptionConnector)(e&&e.type,i.identifierToSql,e&&e.value),a,Object(i.toUpper)(u),Object(i.toUpper)(n)].filter(i.hasVal).join(" ")}function v(t){var r=[],e=l(t.column),a=f(t.definition);r.push(e),r.push(a);var u=function(t){var r=[],e=t.nullable,a=t.character_set,u=t.check,c=t.comment,l=t.collate,f=t.storage,b=t.default_val,v=t.auto_increment,h=t.unique,d=t.primary_key,y=t.column_format,O=t.reference_definition;if(r.push(Object(i.toUpper)(e&&e.value)),b){var m=b.type,E=b.value;r.push(m.toUpperCase(),Object(o.a)(E))}var j=Object(i.getParserOpt)().database;return r.push(Object(n.a)(u)),r.push(Object(i.autoIncrementToSQL)(v),Object(i.toUpper)(d),Object(i.toUpper)(h),Object(i.commentToSQL)(c)),r.push.apply(r,s(Object(i.commonTypeValue)(a))),"sqlite"!==j&&r.push.apply(r,s(Object(i.commonTypeValue)(l))),r.push.apply(r,s(Object(i.commonTypeValue)(y))),r.push.apply(r,s(Object(i.commonTypeValue)(f))),r.push.apply(r,s(p(O))),r.filter(i.hasVal).join(" ")}(t);r.push(u);var c=function(t){if(t)return[Object(i.toUpper)(t.value),"(".concat(Object(o.a)(t.expr),")"),Object(i.toUpper)(t.storage_type)].filter(i.hasVal).join(" ")}(t.generated);return r.push(c),r.filter(i.hasVal).join(" ")}function h(t){return t?["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(t)?Object(i.identifierToSql)(t):Object(i.columnIdentifierToSql)(t)].join(" "):""}function d(t){var r=t.against,e=t.as,n=t.columns,a=t.match,u=t.mode;return[[Object(i.toUpper)(a),"(".concat(n.map((function(t){return l(t)})).join(", "),")")].join(" "),[Object(i.toUpper)(r),["(",Object(o.a)(t.expr),u&&" ".concat(Object(i.literalToSQL)(u)),")"].filter(i.hasVal).join("")].join(" "),h(e)].filter(i.hasVal).join(" ")}function y(t,r){var e=t.expr,n=t.type;if("cast"===n)return Object(a.b)(t);r&&(e.isDual=r);var u=Object(o.a)(e),s=t.expr_list;if(s){var c=[u],l=s.map((function(t){return y(t,r)})).join(", ");return c.push([Object(i.toUpper)(n),n&&"(",l,n&&")"].filter(i.hasVal).join("")),c.filter(i.hasVal).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&(u="(".concat(u,")")),e.array_index&&"column_ref"!==e.type&&(u="".concat(u,"[").concat(Object(i.literalToSQL)(e.array_index.index),"]")),[u,h(t.as)].filter(i.hasVal).join(" ")}function O(t,r){if(!t||"*"===t)return t;var e=function(t){var r=Array.isArray(t)&&t[0];return!(!r||"dual"!==r.type)}(r);return t.map((function(t){return y(t,e)})).join(", ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return h})),e.d(r,"c",(function(){return C})),e.d(r,"d",(function(){return T})),e.d(r,"e",(function(){return d})),e.d(r,"f",(function(){return y})),e.d(r,"g",(function(){return O})),e.d(r,"h",(function(){return g})),e.d(r,"i",(function(){return A})),e.d(r,"j",(function(){return S})),e.d(r,"l",(function(){return m})),e.d(r,"m",(function(){return E})),e.d(r,"o",(function(){return j})),e.d(r,"n",(function(){return w})),e.d(r,"k",(function(){return L}));var n=e(3),o=e(14),a=e(0),u=e(1),i=e(2),s=e(15),c=e(5);function l(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=p(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(i)throw a}}}}function f(t){return function(t){if(Array.isArray(t))return b(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||p(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,r){if(t){if("string"==typeof t)return b(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?b(t,r):void 0}}function b(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function v(t){var r=Object(u.a)(t.expr);return"".concat("CALL"," ").concat(r)}function h(t){var r=t.type,e=t.keyword,o=t.name,s=t.prefix,c=[Object(a.toUpper)(r),Object(a.toUpper)(e),Object(a.toUpper)(s)];switch(e){case"table":c.push(Object(i.c)(o));break;case"trigger":c.push([o[0].schema?"".concat(Object(a.identifierToSql)(o[0].schema),"."):"",Object(a.identifierToSql)(o[0].trigger)].filter(a.hasVal).join(""));break;case"database":case"schema":case"procedure":c.push(Object(a.identifierToSql)(o));break;case"view":c.push(Object(i.c)(o),t.options&&t.options.map(u.a).filter(a.hasVal).join(" "));break;case"index":c.push.apply(c,[Object(n.d)(o)].concat(f(t.table?["ON",Object(i.b)(t.table)]:[]),[t.options&&t.options.map(u.a).filter(a.hasVal).join(" ")]))}return c.filter(a.hasVal).join(" ")}function d(t){var r=t.type,e=t.table,n=Object(a.toUpper)(r);return"".concat(n," ").concat(Object(a.identifierToSql)(e))}function y(t){var r=t.type,e=t.name,n=t.args,o=[Object(a.toUpper)(r)],i=[e];return n&&i.push("(".concat(Object(u.a)(n).join(", "),")")),o.push(i.join("")),o.filter(a.hasVal).join(" ")}function O(t){var r=t.type,e=t.label,n=t.target,o=t.query,u=t.stmts;return[e,Object(a.toUpper)(r),n,"IN",Object(c.a)([o]),"LOOP",Object(c.a)(u),"END LOOP",e].filter(a.hasVal).join(" ")}function m(t){var r=t.type,e=t.level,n=t.raise,o=t.using,i=[Object(a.toUpper)(r),Object(a.toUpper)(e)];return n&&i.push([Object(a.literalToSQL)(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(a.hasVal).join(""),n.expr.map((function(t){return Object(u.a)(t)})).join(", ")),o&&i.push(Object(a.toUpper)(o.type),Object(a.toUpper)(o.option),o.symbol,o.expr.map((function(t){return Object(u.a)(t)})).join(", ")),i.filter(a.hasVal).join(" ")}function E(t){var r=t.type,e=t.table,n=[],o="".concat(r&&r.toUpperCase()," TABLE");if(e){var a,u=l(e);try{for(u.s();!(a=u.n()).done;){var s=a.value.map(i.b);n.push(s.join(" TO "))}}catch(t){u.e(t)}finally{u.f()}}return"".concat(o," ").concat(n.join(", "))}function j(t){var r=t.type,e=t.db,n=Object(a.toUpper)(r),o=Object(a.identifierToSql)(e);return"".concat(n," ").concat(o)}function w(t){var r=t.expr,e=Object(u.a)(r);return"".concat("SET"," ").concat(e)}function L(t){var r=t.type,e=t.keyword,n=t.tables,o=[r.toUpperCase(),Object(a.toUpper)(e)];if("UNLOCK"===r.toUpperCase())return o.join(" ");var u,s=[],c=l(n);try{var p=function(){var t=u.value,r=t.table,e=t.lock_type,n=[Object(i.b)(r)];if(e){n.push(["prefix","type","suffix"].map((function(t){return Object(a.toUpper)(e[t])})).filter(a.hasVal).join(" "))}s.push(n.join(" "))};for(c.s();!(u=c.n()).done;)p()}catch(t){c.e(t)}finally{c.f()}return o.push.apply(o,[s.join(", ")].concat(f(function(t){var r=t.lock_mode,e=t.nowait,n=[];if(r){var o=r.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(t)))),o.filter(a.hasVal).join(" ")}function C(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(a.toUpper)(r),Object(a.toUpper)(e),Object(u.a)(n)].filter(a.hasVal).join(" ")}function T(t){var r=t.type,e=t.declare,i=t.symbol,s=[Object(a.toUpper)(r)],c=e.map((function(t){var r=t.at,e=t.name,i=t.as,s=t.constant,c=t.datatype,l=t.not_null,p=t.prefix,b=t.definition,v=t.keyword,h=[[r,e].filter(a.hasVal).join(""),Object(a.toUpper)(i),Object(a.toUpper)(s)];switch(v){case"variable":h.push.apply(h,[Object(n.a)(c)].concat(f(Object(a.commonTypeValue)(t.collate)),[Object(a.toUpper)(l)])),b&&h.push(Object(a.toUpper)(b.keyword),Object(u.a)(b.value));break;case"cursor":h.push(Object(a.toUpper)(p));break;case"table":h.push(Object(a.toUpper)(p),"(".concat(b.map(o.a).join(", "),")"))}return h.filter(a.hasVal).join(" ")})).join("".concat(i," "));return s.push(c),s.join(" ")}function S(t){var r=t.boolean_expr,e=t.else_expr,n=t.elseif_expr,o=t.if_expr,i=t.prefix,c=t.go,l=t.semicolons,f=t.suffix,p=t.type,b=[Object(a.toUpper)(p),Object(u.a)(r),Object(a.literalToSQL)(i),"".concat(Object(s.a)(o.ast||o)).concat(l[0]),Object(a.toUpper)(c)];return n&&b.push(n.map((function(t){return[Object(a.toUpper)(t.type),Object(u.a)(t.boolean_expr),"THEN",Object(s.a)(t.then.ast||t.then),t.semicolon].filter(a.hasVal).join(" ")})).join(" ")),e&&b.push("ELSE","".concat(Object(s.a)(e.ast||e)).concat(l[1])),b.push(Object(a.literalToSQL)(f)),b.filter(a.hasVal).join(" ")}function A(t){var r=t.name,e=t.host,n=[Object(a.literalToSQL)(r)];return e&&n.push("@",Object(a.literalToSQL)(e)),n.join("")}function g(t){var r=t.type,e=t.grant_option_for,o=t.keyword,i=t.objects,s=t.on,c=t.to_from,l=t.user_or_roles,f=t.with,p=[Object(a.toUpper)(r),Object(a.literalToSQL)(e)],b=i.map((function(t){var r=t.priv,e=t.columns,o=[Object(u.a)(r)];return e&&o.push("(".concat(e.map(n.d).join(", "),")")),o.join(" ")})).join(", ");if(p.push(b),s)switch(p.push("ON"),o){case"priv":p.push(Object(a.literalToSQL)(s.object_type),s.priv_level.map((function(t){return[Object(a.identifierToSql)(t.prefix),Object(a.identifierToSql)(t.name)].filter(a.hasVal).join(".")})).join(", "));break;case"proxy":p.push(A(s))}return p.push(Object(a.toUpper)(c),l.map(A).join(", ")),p.push(Object(a.literalToSQL)(f)),p.filter(a.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"b",(function(){return m})),e.d(r,"a",(function(){return E}));var n=e(8),o=e(1),a=e(2),u=e(0);var i=e(14),s=e(10),c=e(3),l=e(7),f=e(13);var p=e(11),b=e(18),v=e(4);function h(t){var r=t.name,e=t.value;return["@".concat(r),"=",Object(o.a)(e)].filter(u.hasVal).join(" ")}var d=e(21);var y=e(22),O={alter:n.c,analyze:function(t){var r=t.type,e=t.table;return[Object(u.toUpper)(r),Object(a.b)(e)].join(" ")},attach:function(t){var r=t.type,e=t.database,n=t.expr,a=t.as,i=t.schema;return[Object(u.toUpper)(r),Object(u.toUpper)(e),Object(o.a)(n),Object(u.toUpper)(a),Object(u.identifierToSql)(i)].filter(u.hasVal).join(" ")},create:i.b,select:s.a,deallocate:v.c,delete:function(t){var r=t.columns,e=t.from,n=t.table,i=t.where,s=t.orderby,p=t.with,b=t.limit,v=[Object(f.a)(p),"DELETE"],h=Object(c.g)(r,e);return v.push(h),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||v.push(Object(a.c)(n))),v.push(Object(u.commonOptionConnector)("FROM",a.c,e)),v.push(Object(u.commonOptionConnector)("WHERE",o.a,i)),v.push(Object(o.c)(s,"order by")),v.push(Object(l.a)(b)),v.filter(u.hasVal).join(" ")},exec:function(t){var r=t.keyword,e=t.module,n=t.parameters;return[Object(u.toUpper)(r),Object(a.b)(e),(n||[]).map(h).filter(u.hasVal).join(", ")].filter(u.hasVal).join(" ")},execute:v.f,for:v.g,update:p.b,if:v.j,insert:b.a,drop:v.b,truncate:v.b,replace:b.a,declare:v.d,use:v.o,rename:v.m,call:v.a,desc:v.e,set:v.n,lock:v.k,unlock:v.k,show:y.a,grant:v.h,revoke:v.h,proc:function(t){var r=t.stmt;switch(r.type){case"assign":return Object(d.a)(r);case"return":return function(t){var r=t.type,e=t.expr;return[Object(u.toUpper)(r),Object(o.a)(e)].join(" ")}(r)}},raise:v.l,transaction:function(t){var r=t.expr;return Object(o.a)(r)}};function m(t){if(!t)return"";for(var r=O[t.type],e=t,n=e._parentheses,a=e._orderby,i=e._limit,s=[n&&"(",r(t)];t._next;){var c=O[t._next.type],f=Object(u.toUpper)(t.set_op);s.push(f,c(t._next)),t=t._next}return s.push(n&&")",Object(o.c)(a,"order by"),Object(l.a)(i)),s.filter(u.hasVal).join(" ")}function E(t){for(var r=[],e=0,n=t.length;e<n;++e){var o=t[e]&&t[e].ast?t[e].ast:t[e],a=m(o);e===n-1&&"transaction"===o.type&&(a="".concat(a," ;")),r.push(a)}return r.join(" ; ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u})),e.d(r,"b",(function(){return i})),e.d(r,"c",(function(){return s})),e.d(r,"d",(function(){return c})),e.d(r,"e",(function(){return l})),e.d(r,"f",(function(){return f}));var n=e(1),o=e(0),a=e(12);function u(t){var r=t.args,e=t.type,u=t.over,i=r.expr,s=r.having,c="".concat(Object(o.toUpper)(e),"(").concat(Object(n.a)(i));return s&&(c="".concat(c," HAVING ").concat(Object(o.toUpper)(s.prefix)," ").concat(Object(n.a)(s.expr))),[c="".concat(c,")"),Object(a.a)(u)].filter(o.hasVal).join(" ")}function i(t){var r=t.arrows,e=void 0===r?[]:r,a=t.collate,u=t.target,i=t.expr,s=t.keyword,c=t.symbol,l=t.as,f=t.properties,p=void 0===f?[]:f,b=u.length,v=u.dataType,h=u.parentheses,d=u.quoted,y=u.scale,O=u.suffix,m="";null!=b&&(m=y?"".concat(b,", ").concat(y):b),h&&(m="(".concat(m,")")),O&&O.length&&(m+=" ".concat(O.join(" ")));var E=Object(n.a)(i),j="::",w="";return"as"===c&&(E="".concat(Object(o.toUpper)(s),"(").concat(E),w=")",j=" ".concat(c.toUpperCase()," ")),w+=e.map((function(t,r){return Object(o.commonOptionConnector)(t,o.literalToSQL,p[r])})).join(" "),l&&(w+=" AS ".concat(Object(o.identifierToSql)(l))),a&&(w+=" ".concat(Object(o.commonTypeValue)(a).join(" "))),[E,j,d,v,d,function(t){if(!t||!t.array)return"";switch(t.array){case"one":return"[]";case"two":return"[][]"}}(u),m,w].filter(o.hasVal).join("")}function s(t){var r=t.args,e=t.type,a=r.field,u=r.cast_type,i=r.source,s=["".concat(Object(o.toUpper)(e),"(").concat(Object(o.toUpper)(a)),"FROM",Object(o.toUpper)(u),Object(n.a)(i)];return"".concat(s.filter(o.hasVal).join(" "),")")}function c(t){var r=t.args,e=t.type,a=["input","path","outer","recursive","mode"].map((function(t){return function(t){if(!t)return"";var r=t.type,e=t.symbol,a=t.value;return[Object(o.toUpper)(r),e,Object(n.a)(a)].filter(o.hasVal).join(" ")}(r[t])})).filter(o.hasVal).join(", ");return"".concat(Object(o.toUpper)(e),"(").concat(a,")")}function l(t){var r=t.args,e=t.name,u=t.args_parentheses,i=t.parentheses,s=t.over,c=t.collate,l=t.suffix,f=Object(o.commonTypeValue)(c).join(" "),p=Object(a.a)(s),b=Object(n.a)(l);if(!r)return[e,p].filter(o.hasVal).join(" ");var v=t.separator||", ";"TRIM"===Object(o.toUpper)(e)&&(v=" ");var h=[e];return h.push(!1===u?" ":"("),h.push(Object(n.a)(r).join(v)),!1!==u&&h.push(")"),h=[h.join(""),b].filter(o.hasVal).join(" "),[i?"(".concat(h,")"):h,f,p].filter(o.hasVal).join(" ")}function f(t){var r=t.as,e=t.name,o=t.args;return["".concat(e,"(").concat(Object(n.a)(o).join(", "),")"),"AS",l(r)].join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(0),o=e(1);function a(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(!t)return;if("string"==typeof t)return u(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(t);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return u(t,r)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function i(t){return t?[Object(n.toUpper)(t.prefix),Object(o.a)(t.value),Object(n.toUpper)(t.suffix)]:[]}function s(t){return t?t.fetch?(e=(r=t).fetch,u=r.offset,[].concat(a(i(u)),a(i(e))).filter(n.hasVal).join(" ")):function(t){var r=t.seperator,e=t.value;return 1===e.length&&"offset"===r?Object(n.connector)("OFFSET",Object(o.a)(e[0])):Object(n.connector)("LIMIT",e.map(o.a).join("".concat("offset"===r?" ":"").concat(Object(n.toUpper)(r)," ")))}(t):"";var r,e,u}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f})),e.d(r,"c",(function(){return p})),e.d(r,"b",(function(){return l}));var n=e(3),o=e(14),a=e(9),u=e(2),i=e(1),s=e(10),c=e(0);function l(t){if(!t)return"";var r=t.action,e=t.create_definitions,u=t.first_after,i=t.if_not_exists,s=t.keyword,l=t.old_column,f=t.prefix,p=t.resource,b=t.symbol,v="",h=[];switch(p){case"column":h=[Object(n.b)(t)];break;case"index":h=Object(a.c)(t),v=t[p];break;case"table":case"schema":v=Object(c.identifierToSql)(t[p]);break;case"aggregate":case"function":case"domain":case"type":v=Object(c.identifierToSql)(t[p]);break;case"algorithm":case"lock":case"table-option":v=[b,Object(c.toUpper)(t[p])].filter(c.hasVal).join(" ");break;case"constraint":v=Object(c.identifierToSql)(t[p]),h=[Object(o.a)(e)];break;case"key":v=Object(c.identifierToSql)(t[p]);break;default:v=[b,t[p]].filter((function(t){return null!==t})).join(" ")}return[Object(c.toUpper)(r),Object(c.toUpper)(s),Object(c.toUpper)(i),l&&Object(n.d)(l),Object(c.toUpper)(f),v&&v.trim(),h.filter(c.hasVal).join(" "),u&&"".concat(Object(c.toUpper)(u.keyword)," ").concat(Object(n.d)(u.column))].filter(c.hasVal).join(" ")}function f(t){var r=t.default&&[Object(c.toUpper)(t.default.keyword),Object(i.a)(t.default.value)].join(" ");return[Object(c.toUpper)(t.mode),t.name,Object(c.dataTypeToSQL)(t.type),r].filter(c.hasVal).join(" ")}function p(t){var r=t.keyword;switch(void 0===r?"table":r){case"aggregate":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,a=t.type,u=r.expr,i=r.orderby;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),"(".concat(u.map(f).join(", ")).concat(i?[" ORDER","BY",i.map(f).join(", ")].join(" "):"",")")].filter(c.hasVal).join(""),l(e)].filter(c.hasVal).join(" ")}(t);case"table":return function(t){var r=t.type,e=t.table,n=t.expr,o=void 0===n?[]:n;return[Object(c.toUpper)(r),"TABLE",Object(u.c)(e),o.map(i.a).join(", ")].filter(c.hasVal).join(" ")}(t);case"schema":return function(t){var r=t.expr,e=t.keyword,n=t.schema,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),Object(c.identifierToSql)(n),l(r)].filter(c.hasVal).join(" ")}(t);case"domain":case"type":return function(t){var r=t.expr,e=t.keyword,n=t.name,o=t.type;return[Object(c.toUpper)(o),Object(c.toUpper)(e),[Object(c.identifierToSql)(n.schema),Object(c.identifierToSql)(n.name)].filter(c.hasVal).join("."),l(r)].filter(c.hasVal).join(" ")}(t);case"function":return function(t){var r=t.args,e=t.expr,n=t.keyword,o=t.name,a=t.type;return[Object(c.toUpper)(a),Object(c.toUpper)(n),[[Object(c.identifierToSql)(o.schema),Object(c.identifierToSql)(o.name)].filter(c.hasVal).join("."),r&&"(".concat(r.expr?r.expr.map(f).join(", "):"",")")].filter(c.hasVal).join(""),l(e)].filter(c.hasVal).join(" ")}(t);case"view":return function(t){var r=t.type,e=t.columns,o=t.attributes,a=t.select,i=t.view,l=t.with,f=[Object(c.toUpper)(r),"VIEW",Object(u.b)(i)];return e&&f.push("(".concat(e.map(n.d).join(", "),")")),o&&f.push("WITH ".concat(o.map(c.toUpper).join(", "))),f.push("AS",Object(s.a)(a)),l&&f.push(Object(c.toUpper)(l)),f.filter(c.hasVal).join(" ")}(t)}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return f})),e.d(r,"d",(function(){return i})),e.d(r,"b",(function(){return c})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1);function a(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(!t)return;if("string"==typeof t)return u(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(t);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return u(t,r)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function i(t){if(!t)return[];var r=t.keyword,e=t.type;return[r.toUpperCase(),Object(n.toUpper)(e)]}function s(t){if(t){var r=t.type,e=t.expr,o=t.symbol,u=r.toUpperCase(),s=[];switch(s.push(u),u){case"KEY_BLOCK_SIZE":o&&s.push(o),s.push(Object(n.literalToSQL)(e));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,a(i(t)));break;case"WITH PARSER":s.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(Object(n.commentToSQL)(t));break;case"DATA_COMPRESSION":s.push(o,Object(n.toUpper)(e.value),Object(n.onPartitionsToSQL)(e.on));break;default:s.push(o,Object(n.literalToSQL)(e))}return s.filter(n.hasVal).join(" ")}}function c(t){return t?t.map(s):[]}function l(t){var r=t.constraint_type,e=t.index_type,u=t.index_options,s=void 0===u?[]:u,l=t.definition,f=t.on,p=t.with,b=[];if(b.push.apply(b,a(i(e))),l&&l.length){var v="CHECK"===Object(n.toUpper)(r)?"(".concat(Object(o.a)(l[0]),")"):"(".concat(l.map((function(t){return Object(o.a)(t)})).join(", "),")");b.push(v)}return b.push(c(s).join(" ")),p&&b.push("WITH (".concat(c(p).join(", "),")")),f&&b.push("ON [".concat(f,"]")),b}function f(t){var r=[],e=t.keyword,o=t.index;return r.push(Object(n.toUpper)(e)),r.push(o),r.push.apply(r,a(l(t))),r.filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return l}));var n=e(1),o=e(3),a=e(7),u=e(13),i=e(2),s=e(0);function c(t){if(t&&t.position){var r=t.keyword,e=t.expr,o=[],a=Object(s.toUpper)(r);switch(a){case"VAR":o.push(e.map(n.d).join(", "));break;default:o.push(a,"string"==typeof e?Object(s.identifierToSql)(e):Object(n.a)(e))}return o.filter(s.hasVal).join(" ")}}function l(t){var r=t.as_struct_val,e=t.columns,l=t.distinct,f=t.for,p=t.from,b=t.for_sys_time_as_of,v=void 0===b?{}:b,h=t.locking_read,d=t.groupby,y=t.having,O=t.into,m=void 0===O?{}:O,E=t.limit,j=t.options,w=t.orderby,L=t.parentheses_symbol,C=t.qualify,T=t.top,S=t.window,A=t.with,g=t.where,R=[Object(u.a)(A),"SELECT",Object(s.toUpper)(r)];R.push(Object(s.topToSQL)(T)),Array.isArray(j)&&R.push(j.join(" ")),R.push(function(t){if(t){if("string"==typeof t)return t;var r=t.type,e=t.columns,n=[Object(s.toUpper)(r)];return e&&n.push("(".concat(e.map(o.d).join(", "),")")),n.filter(s.hasVal).join(" ")}}(l),Object(o.g)(e,p));var I=m.position,N="";I&&(N=Object(s.commonOptionConnector)("INTO",c,m)),"column"===I&&R.push(N),R.push(Object(s.commonOptionConnector)("FROM",i.c,p)),"from"===I&&R.push(N);var U=v||{},_=U.keyword,x=U.expr;R.push(Object(s.commonOptionConnector)(_,n.a,x)),R.push(Object(s.commonOptionConnector)("WHERE",n.a,g)),R.push(Object(s.connector)("GROUP BY",Object(n.b)(d).join(", "))),R.push(Object(s.commonOptionConnector)("HAVING",n.a,y)),R.push(Object(s.commonOptionConnector)("QUALIFY",n.a,C)),R.push(Object(s.commonOptionConnector)("WINDOW",n.a,S)),R.push(Object(n.c)(w,"order by")),R.push(Object(a.a)(E)),R.push(Object(s.toUpper)(h)),"end"===I&&R.push(N),R.push(function(t){if(t){var r=t.expr,e=t.keyword,o=t.type,a=[Object(s.toUpper)(o),Object(s.toUpper)(e)];return r?"".concat(a.join(" "),"(").concat(Object(n.a)(r),")"):a.join(" ")}}(f));var k=R.filter(s.hasVal).join(" ");return L?"(".concat(k,")"):k}},function(t,r,e){"use strict";e.d(r,"b",(function(){return f})),e.d(r,"a",(function(){return l}));var n=e(2),o=e(1),a=e(7),u=e(0),i=e(13);function s(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(!t)return;if("string"==typeof t)return c(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(t);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return c(t,r)}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(i)throw a}}}}function c(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function l(t){if(!t||0===t.length)return"";var r,e=[],n=s(t);try{for(n.s();!(r=n.n()).done;){var a=r.value,i=a.table,c=a.column,l=a.value,f=[[i,c].filter(u.hasVal).map((function(t){return Object(u.identifierToSql)(t)})).join(".")],p="";l&&(p=Object(o.a)(l),f.push("=",p)),e.push(f.filter(u.hasVal).join(" "))}}catch(t){n.e(t)}finally{n.f()}return e.join(", ")}function f(t){var r=t.from,e=t.table,s=t.set,c=t.where,f=t.orderby,p=t.with,b=t.limit,v=t.returning;return[Object(i.a)(p),"UPDATE",Object(n.c)(e),Object(u.commonOptionConnector)("SET",l,s),Object(u.commonOptionConnector)("FROM",n.c,r),Object(u.commonOptionConnector)("WHERE",o.a,c),Object(o.c)(f,"order by"),Object(a.a)(b),Object(u.returningToSQL)(v)].filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(0),o=e(1),a=e(17);function u(t){if(t){var r=t.as_window_specification,e=t.expr,u=t.keyword,i=t.type,s=t.parentheses,c=Object(n.toUpper)(i);if("WINDOW"===c)return"OVER ".concat(Object(a.a)(r));if("ON UPDATE"===c){var l="".concat(Object(n.toUpper)(i)," ").concat(Object(n.toUpper)(u)),f=Object(o.a)(e)||[];return s&&(l="".concat(l,"(").concat(f.join(", "),")")),l}throw new Error("unknown over type")}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u}));var n=e(3),o=e(1),a=e(0);function u(t){if(t&&0!==t.length){var r=t[0].recursive?"RECURSIVE ":"",e=t.map((function(t){var r=t.name,e=t.stmt,u=t.columns,i=Array.isArray(u)?"(".concat(u.map(n.d).join(", "),")"):"";return"".concat("default"===r.type?Object(a.identifierToSql)(r.value):Object(a.literalToSQL)(r)).concat(i," AS (").concat(Object(o.a)(e),")")})).join(", ");return"WITH ".concat(r).concat(e)}}},function(t,r,e){"use strict";e.d(r,"b",(function(){return L})),e.d(r,"a",(function(){return y}));var n=e(8),o=e(1),a=e(9),u=e(3),i=e(4),s=e(16),c=e(6),l=e(2),f=e(11),p=e(5),b=e(0);function v(t){return function(t){if(Array.isArray(t))return d(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||h(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,r){if(t){if("string"==typeof t)return d(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);return"Object"===e&&t.constructor&&(e=t.constructor.name),"Map"===e||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?d(t,r):void 0}}function d(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function y(t){if(!t)return[];var r=t.resource;switch(r){case"column":return Object(u.b)(t);case"index":return Object(a.a)(t);case"constraint":return Object(s.a)(t);case"sequence":return[Object(b.toUpper)(t.prefix),Object(o.a)(t.value)].filter(b.hasVal).join(" ");default:throw new Error("unknown resource = ".concat(r," type"))}}function O(t){var r=t.as,e=t.domain,n=t.type,a=t.keyword,u=t.target,i=t.create_definitions,c=[Object(b.toUpper)(n),Object(b.toUpper)(a),[Object(b.identifierToSql)(e.schema),Object(b.identifierToSql)(e.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.dataTypeToSQL)(u)];if(i&&i.length>0){var l,f=[],p=function(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=h(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(i)throw a}}}}(i);try{for(p.s();!(l=p.n()).done;){var v=l.value,d=v.type;switch(d){case"collate":f.push(Object(b.commonTypeValue)(v).join(" "));break;case"default":f.push(Object(b.toUpper)(d),Object(o.a)(v.value));break;case"constraint":f.push(Object(s.a)(v))}}}catch(t){p.e(t)}finally{p.f()}c.push(f.filter(b.hasVal).join(" "))}return c.filter(b.hasVal).join(" ")}function m(t){return t.dataType?Object(b.dataTypeToSQL)(t):[Object(b.identifierToSql)(t.db),Object(b.identifierToSql)(t.schema),Object(b.identifierToSql)(t.table)].filter(b.hasVal).join(".")}function E(t){var r=t.type;switch(r){case"as":return[Object(b.toUpper)(r),t.symbol,Object(p.b)(t.declare),Object(b.toUpper)(t.begin),Object(p.a)(t.expr),Object(b.toUpper)(t.end),t.symbol].filter(b.hasVal).join(" ");case"set":return[Object(b.toUpper)(r),t.parameter,Object(b.toUpper)(t.value&&t.value.prefix),t.value&&t.value.expr.map(o.a).join(", ")].filter(b.hasVal).join(" ");default:return Object(o.a)(t)}}function j(t){var r=t.type,e=t.replace,o=t.keyword,a=t.name,i=t.args,s=t.returns,c=t.options,l=t.last,f=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],p=[Object(b.identifierToSql)(a.schema),a.name].filter(b.hasVal).join("."),v=i.map(n.a).filter(b.hasVal).join(", ");return f.push("".concat(p,"(").concat(v,")"),function(t){var r=t.type,e=t.keyword,n=t.expr;return[Object(b.toUpper)(r),Object(b.toUpper)(e),Array.isArray(n)?"(".concat(n.map(u.b).join(", "),")"):m(n)].filter(b.hasVal).join(" ")}(s),c.map(E).join(" "),l),f.filter(b.hasVal).join(" ")}function w(t){var r=t.type,e=t.symbol,n=t.value,a=[Object(b.toUpper)(r),e];switch(Object(b.toUpper)(r)){case"SFUNC":a.push([Object(b.identifierToSql)(n.schema),n.name].filter(b.hasVal).join("."));break;case"STYPE":case"MSTYPE":a.push(Object(b.dataTypeToSQL)(n));break;default:a.push(Object(o.a)(n))}return a.filter(b.hasVal).join(" ")}function L(t){var r=t.keyword,e="";switch(r.toLowerCase()){case"aggregate":e=function(t){var r=t.type,e=t.replace,o=t.keyword,a=t.name,u=t.args,i=t.options,s=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o)],c=[Object(b.identifierToSql)(a.schema),a.name].filter(b.hasVal).join("."),l="".concat(u.expr.map(n.a).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(n.a).join(", ")].join(" "):"");return s.push("".concat(c,"(").concat(l,")"),"(".concat(i.map(w).join(", "),")")),s.filter(b.hasVal).join(" ")}(t);break;case"table":e=function(t){var r=t.type,e=t.keyword,n=t.table,o=t.like,a=t.as,u=t.temporary,i=t.if_not_exists,s=t.create_definitions,c=t.table_options,f=t.ignore_replace,v=t.or_replace,h=t.query_expr,d=[Object(b.toUpper)(r),Object(b.toUpper)(v),Object(b.toUpper)(u),Object(b.toUpper)(e),Object(b.toUpper)(i),Object(l.c)(n)];if(o){var O=o.type,m=o.table,E=Object(l.c)(m);return d.push(Object(b.toUpper)(O),E),d.filter(b.hasVal).join(" ")}return s&&d.push("(".concat(s.map(y).join(", "),")")),c&&d.push(c.map(l.a).join(" ")),d.push(Object(b.toUpper)(f),Object(b.toUpper)(a)),h&&d.push(Object(p.b)(h)),d.filter(b.hasVal).join(" ")}(t);break;case"trigger":e="constraint"===t.resource?function(t){var r=t.constraint,e=t.constraint_kw,n=t.deferrable,a=t.events,u=t.execute,i=t.for_each,s=t.from,f=t.location,p=t.keyword,h=t.or,d=t.type,y=t.table,O=t.when,m=[Object(b.toUpper)(d),Object(b.toUpper)(h),Object(b.toUpper)(e),Object(b.toUpper)(p),Object(b.identifierToSql)(r),Object(b.toUpper)(f)],E=Object(b.triggerEventToSQL)(a);return m.push(E,"ON",Object(l.b)(y)),s&&m.push("FROM",Object(l.b)(s)),m.push.apply(m,v(Object(b.commonKeywordArgsToSQL)(n)).concat(v(Object(b.commonKeywordArgsToSQL)(i)))),O&&m.push(Object(b.toUpper)(O.type),Object(o.a)(O.cond)),m.push(Object(b.toUpper)(u.keyword),Object(c.e)(u.expr)),m.filter(b.hasVal).join(" ")}(t):function(t){var r=t.definer,e=t.for_each,n=t.keyword,a=t.execute,i=t.type,s=t.table,c=t.if_not_exists,v=t.temporary,h=t.trigger,d=t.events,y=t.order,O=t.time,m=t.when,E=[Object(b.toUpper)(i),Object(b.toUpper)(v),r,Object(b.toUpper)(n),Object(b.toUpper)(c),Object(l.b)(h),Object(b.toUpper)(O),d.map((function(t){var r=[Object(b.toUpper)(t.keyword)],e=t.args;return e&&r.push(Object(b.toUpper)(e.keyword),e.columns.map(u.d).join(", ")),r.join(" ")})),"ON",Object(l.b)(s),Object(b.toUpper)(e&&e.keyword),Object(b.toUpper)(e&&e.args),y&&"".concat(Object(b.toUpper)(y.keyword)," ").concat(Object(b.identifierToSql)(y.trigger)),Object(b.commonOptionConnector)("WHEN",o.a,m),Object(b.toUpper)(a.prefix)];switch(a.type){case"set":E.push(Object(b.commonOptionConnector)("SET",f.a,a.expr));break;case"multiple":E.push(Object(p.a)(a.expr.ast))}return E.push(Object(b.toUpper)(a.suffix)),E.filter(b.hasVal).join(" ")}(t);break;case"extension":e=function(t){var r=t.extension,e=t.from,n=t.if_not_exists,o=t.keyword,a=t.schema,u=t.type,i=t.with,s=t.version;return[Object(b.toUpper)(u),Object(b.toUpper)(o),Object(b.toUpper)(n),Object(b.literalToSQL)(r),Object(b.toUpper)(i),Object(b.commonOptionConnector)("SCHEMA",b.literalToSQL,a),Object(b.commonOptionConnector)("VERSION",b.literalToSQL,s),Object(b.commonOptionConnector)("FROM",b.literalToSQL,e)].filter(b.hasVal).join(" ")}(t);break;case"function":e=j(t);break;case"index":e=function(t){var r=t.concurrently,e=t.filestream_on,u=t.keyword,i=t.include,s=t.index_columns,c=t.index_type,f=t.index_using,p=t.index,h=t.on,d=t.index_options,y=t.algorithm_option,O=t.lock_option,m=t.on_kw,E=t.table,j=t.tablespace,w=t.type,L=t.where,C=t.with,T=t.with_before_where,S=C&&"WITH (".concat(Object(a.b)(C).join(", "),")"),A=i&&"".concat(Object(b.toUpper)(i.keyword)," (").concat(i.columns.map((function(t){return Object(b.identifierToSql)(t)})).join(", "),")"),g=[Object(b.toUpper)(w),Object(b.toUpper)(c),Object(b.toUpper)(u),Object(b.toUpper)(r),Object(b.identifierToSql)(p),Object(b.toUpper)(m),Object(l.b)(E)].concat(v(Object(a.d)(f)),["(".concat(Object(b.columnOrderListToSQL)(s),")"),A,Object(a.b)(d).join(" "),Object(n.b)(y),Object(n.b)(O),Object(b.commonOptionConnector)("TABLESPACE",b.literalToSQL,j)]);return T?g.push(S,Object(b.commonOptionConnector)("WHERE",o.a,L)):g.push(Object(b.commonOptionConnector)("WHERE",o.a,L),S),g.push(Object(b.commonOptionConnector)("ON",o.a,h),Object(b.commonOptionConnector)("FILESTREAM_ON",b.literalToSQL,e)),g.filter(b.hasVal).join(" ")}(t);break;case"sequence":e=function(t){var r=t.type,e=t.keyword,n=t.sequence,o=t.temporary,a=t.if_not_exists,u=t.create_definitions,i=[Object(b.toUpper)(r),Object(b.toUpper)(o),Object(b.toUpper)(e),Object(b.toUpper)(a),Object(l.c)(n)];return u&&i.push(u.map(y).join(" ")),i.filter(b.hasVal).join(" ")}(t);break;case"database":e=function(t){var r=t.type,e=t.keyword,n=t.database,o=t.if_not_exists,a=t.create_definitions,u=[Object(b.toUpper)(r),Object(b.toUpper)(e),Object(b.toUpper)(o),Object(b.columnIdentifierToSql)(n)];return a&&u.push(a.map(l.a).join(" ")),u.filter(b.hasVal).join(" ")}(t);break;case"view":e=function(t){var r=t.algorithm,e=t.columns,n=t.definer,o=t.if_not_exists,a=t.keyword,u=t.recursive,i=t.replace,s=t.select,c=t.sql_security,l=t.temporary,f=t.type,v=t.view,h=t.with,d=t.with_options,y=v.db,O=v.view,m=[Object(b.identifierToSql)(y),Object(b.identifierToSql)(O)].filter(b.hasVal).join(".");return[Object(b.toUpper)(f),Object(b.toUpper)(i),Object(b.toUpper)(l),Object(b.toUpper)(u),r&&"ALGORITHM = ".concat(Object(b.toUpper)(r)),n,c&&"SQL SECURITY ".concat(Object(b.toUpper)(c)),Object(b.toUpper)(a),Object(b.toUpper)(o),m,e&&"(".concat(e.map(b.columnIdentifierToSql).join(", "),")"),d&&["WITH","(".concat(d.map((function(t){return Object(b.commonTypeValue)(t).join(" ")})).join(", "),")")].join(" "),"AS",Object(p.b)(s),Object(b.toUpper)(h)].filter(b.hasVal).join(" ")}(t);break;case"domain":e=O(t);break;case"type":e=function(t){var r=t.as,e=t.create_definitions,n=t.keyword,a=t.name,u=t.resource,i=t.type,s=[Object(b.toUpper)(i),Object(b.toUpper)(n),[Object(b.identifierToSql)(a.schema),Object(b.identifierToSql)(a.name)].filter(b.hasVal).join("."),Object(b.toUpper)(r),Object(b.toUpper)(u)];if(e){var c=[];switch(u){case"enum":c.push(Object(o.a)(e))}s.push(c.filter(b.hasVal).join(" "))}return s.filter(b.hasVal).join(" ")}(t);break;case"user":e=function(t){var r=t.attribute,e=t.comment,n=t.default_role,a=t.if_not_exists,u=t.keyword,s=t.lock_option,c=t.password_options,l=t.require,f=t.resource_options,p=t.type,v=t.user.map((function(t){var r=t.user,e=t.auth_option,n=[Object(i.i)(r)];return e&&n.push(Object(b.toUpper)(e.keyword),e.auth_plugin,Object(b.literalToSQL)(e.value)),n.filter(b.hasVal).join(" ")})).join(", "),h=[Object(b.toUpper)(p),Object(b.toUpper)(u),Object(b.toUpper)(a),v];return n&&h.push(Object(b.toUpper)(n.keyword),n.value.map(i.i).join(", ")),h.push(Object(b.commonOptionConnector)(l&&l.keyword,o.a,l&&l.value)),f&&h.push(Object(b.toUpper)(f.keyword),f.value.map((function(t){return Object(o.a)(t)})).join(" ")),c&&c.forEach((function(t){return h.push(Object(b.commonOptionConnector)(t.keyword,o.a,t.value))})),h.push(Object(b.literalToSQL)(s),Object(b.commentToSQL)(e),Object(b.literalToSQL)(r)),h.filter(b.hasVal).join(" ")}(t);break;default:throw new Error("unknown create resource ".concat(r))}return e}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(5),o=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction"];function a(t){var r=t&&t.ast?t.ast:t;if(!o.includes(r.type))throw new Error("".concat(r.type," statements not supported at the moment"))}function u(t){return Array.isArray(t)?(t.forEach(a),Object(n.a)(t)):(a(t),Object(n.b)(t))}function i(t){return"go"===t.go?function t(r){if(!r||0===r.length)return"";var e=[u(r.ast)];return r.go_next&&e.push(r.go.toUpperCase(),t(r.go_next)),e.filter((function(t){return t})).join(" ")}(t):u(t)}},function(t,r,e){"use strict";e.d(r,"a",(function(){return s}));var n=e(0),o=e(9),a=e(3);function u(t){return function(t){if(Array.isArray(t))return i(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,r){if(!t)return;if("string"==typeof t)return i(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(t);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return i(t,r)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function s(t){if(t){var r=t.constraint,e=t.constraint_type,i=t.enforced,s=t.index,c=t.keyword,l=t.reference_definition,f=[],p=Object(n.getParserOpt)().database;f.push(Object(n.toUpper)(c)),f.push(Object(n.identifierToSql)(r));var b=Object(n.toUpper)(e);return"sqlite"===p&&"UNIQUE KEY"===b&&(b="UNIQUE"),f.push(b),f.push("sqlite"!==p&&Object(n.identifierToSql)(s)),f.push.apply(f,u(Object(o.c)(t))),f.push.apply(f,u(Object(a.e)(l))),f.push(Object(n.toUpper)(i)),f.filter(n.hasVal).join(" ")}}},function(t,r,e){"use strict";e.d(r,"a",(function(){return u})),e.d(r,"b",(function(){return s})),e.d(r,"c",(function(){return l}));var n=e(0),o=e(1),a=e(12);function u(t){if("string"==typeof t)return t;var r=t.window_specification;return"(".concat(function(t){var r=t.name,e=t.partitionby,a=t.orderby,u=t.window_frame_clause;return[r,Object(o.c)(e,"partition by"),Object(o.c)(a,"order by"),Object(n.toUpper)(u)].filter(n.hasVal).join(" ")}(r),")")}function i(t){var r=t.name,e=t.as_window_specification;return"".concat(r," AS ").concat(u(e))}function s(t){return t.expr.map(i).join(", ")}function c(t){var r=t.args,e=t.name,a=t.consider_nulls,u=void 0===a?"":a,i=r?Object(o.a)(r).join(", "):"",s=function(t){switch(Object(n.toUpper)(t)){case"NTH_VALUE":case"LEAD":case"LAG":return!1;default:return!0}}(e);return[e,"(",i,!s&&")",u&&" ",u,s&&")"].filter(n.hasVal).join("")}function l(t){var r=t.over;return[c(t),Object(a.a)(r)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return v})),e.d(r,"b",(function(){return c}));var n=e(2),o=e(1),a=e(3),u=e(0),i=e(10),s=e(11);function c(t){if("select"===t.type)return Object(i.a)(t);var r=t.map(o.a);return"(".concat(r.join("), ("),")")}function l(t){if(!t)return"";var r=["PARTITION","("];if(Array.isArray(t))r.push(t.map(u.identifierToSql).join(", "));else{var e=t.value;r.push(e.map(o.a).join(", "))}return r.push(")"),r.filter(u.hasVal).join("")}function f(t){if(!t)return"";switch(t.type){case"column":return"(".concat(t.expr.map(a.d).join(", "),")")}}function p(t){var r=t.expr,e=t.keyword,n=r.type,a=[Object(u.toUpper)(e)];switch(n){case"origin":a.push(Object(u.literalToSQL)(r));break;case"update":a.push("UPDATE",Object(u.commonOptionConnector)("SET",s.a,r.set),Object(u.commonOptionConnector)("WHERE",o.a,r.where))}return a.filter(u.hasVal).join(" ")}function b(t){if(!t)return"";var r=t.action;return[f(t.target),p(r)].filter(u.hasVal).join(" ")}function v(t){var r=t.table,e=t.type,a=t.prefix,i=void 0===a?"into":a,f=t.columns,p=t.conflict,v=t.values,h=t.where,d=t.on_duplicate_update,y=t.partition,O=t.returning,m=t.set,E=d||{},j=E.keyword,w=E.set,L=[Object(u.toUpper)(e),Object(u.toUpper)(i),Object(n.c)(r),l(y)];return Array.isArray(f)&&L.push("(".concat(f.map(u.identifierToSql).join(", "),")")),L.push(Object(u.commonOptionConnector)(Array.isArray(v)?"VALUES":"",c,v)),L.push(Object(u.commonOptionConnector)("ON CONFLICT",b,p)),L.push(Object(u.commonOptionConnector)("SET",s.a,m)),L.push(Object(u.commonOptionConnector)("WHERE",o.a,h)),L.push(Object(u.returningToSQL)(O)),L.push(Object(u.commonOptionConnector)(j,s.a,w)),L.filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(1),o=e(0);function a(t){var r=t.operator||t.op,e=Object(n.a)(t.right),a=!1;if(Array.isArray(e)){switch(r){case"=":r="IN";break;case"!=":r="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":a=!0,e="".concat(e[0]," AND ").concat(e[1])}a||(e="(".concat(e.join(", "),")"))}var u=t.right.escape||{},i=[Object(n.a)(t.left),r,e,Object(o.toUpper)(u.type),Object(n.a)(u.value)].filter(o.hasVal).join(" ");return t.parentheses?"(".concat(i,")"):i}},function(t,r,e){"use strict";e.d(r,"a",(function(){return a}));var n=e(0),o=e(1);function a(t){var r=t.expr,e=t.unit;return["INTERVAL",Object(o.a)(r),Object(n.toUpper)(e)].filter(n.hasVal).join(" ")}},function(t,r,e){"use strict";e.d(r,"a",(function(){return o}));var n=e(1);function o(t){var r=t.left,e=t.right,o=t.symbol,a=t.keyword;r.keyword=a;var u=Object(n.a)(r),i=Object(n.a)(e);return"".concat(u," ").concat(o," ").concat(i)}},function(t,r,e){"use strict";e.d(r,"a",(function(){return i}));var n=e(1),o=e(7),a=e(2),u=e(0);function i(t){var r,e,i,s,c=t.keyword,l=t.suffix,f="";switch(Object(u.toUpper)(c)){case"BINLOG":e=(r=t).in,i=r.from,s=r.limit,f=[Object(u.commonOptionConnector)("IN",u.literalToSQL,e&&e.right),Object(u.commonOptionConnector)("FROM",a.c,i),Object(o.a)(s)].filter(u.hasVal).join(" ");break;case"CHARACTER":case"COLLATION":f=function(t){var r=t.expr;if(r){var e=r.op;return"LIKE"===Object(u.toUpper)(e)?Object(u.commonOptionConnector)("LIKE",u.literalToSQL,r.right):Object(u.commonOptionConnector)("WHERE",n.a,r)}}(t);break;case"COLUMNS":case"INDEXES":f=Object(u.commonOptionConnector)("FROM",a.c,t.from);break;case"GRANTS":f=function(t){var r=t.for;if(r){var e=r.user,n=r.host,o=r.role_list,a="'".concat(e,"'");return n&&(a+="@'".concat(n,"'")),["FOR",a,o&&"USING",o&&o.map((function(t){return"'".concat(t,"'")})).join(", ")].filter(u.hasVal).join(" ")}}(t);break;case"CREATE":f=Object(u.commonOptionConnector)("",a.b,t[l]);break;case"VAR":f=Object(n.d)(t.var),c=""}return["SHOW",Object(u.toUpper)(c),Object(u.toUpper)(l),f].filter(u.hasVal).join(" ")}},function(t,r,e){"use strict";var n=e(1),o=e(24);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u,i,s,c,l=(u={},i="flinksql",s=o.parse,c=function(t,r){if("object"!=a(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(i,"string"),(i="symbol"==a(c)?c:String(c))in u?Object.defineProperty(u,i,{value:s,enumerable:!0,configurable:!0,writable:!0}):u[i]=s,u),f=e(15),p=e(0);function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function v(t,r){var e="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!e){if(Array.isArray(t)||(e=function(t,r){if(!t)return;if("string"==typeof t)return h(t,r);var e=Object.prototype.toString.call(t).slice(8,-1);"Object"===e&&t.constructor&&(e=t.constructor.name);if("Map"===e||"Set"===e)return Array.from(t);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return h(t,r)}(t))||r&&t&&"number"==typeof t.length){e&&(t=e);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,u=!0,i=!1;return{s:function(){e=e.call(t)},n:function(){var t=e.next();return u=t.done,t},e:function(t){i=!0,a=t},f:function(){try{u||null==e.return||e.return()}finally{if(i)throw a}}}}function h(t,r){(null==r||r>t.length)&&(r=t.length);for(var e=0,n=new Array(r);e<r;e++)n[e]=t[e];return n}function d(t,r){for(var e=0;e<r.length;e++){var n=r[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}function y(t){var r=function(t,r){if("object"!=b(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0!==e){var n=e.call(t,r||"default");if("object"!=b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}(t,"string");return"symbol"==b(r)?r:String(r)}var O=function(){function t(){!function(t,r){if(!(t instanceof r))throw new TypeError("Cannot call a class as a function")}(this,t)}var r,e,o;return r=t,(e=[{key:"astify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=this.parse(t,r);return e&&e.ast}},{key:"sqlify",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(f.a)(t,r)}},{key:"exprToSQL",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT;return Object(p.setParserOpt)(r),Object(n.a)(t)}},{key:"parse",value:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:p.DEFAULT_OPT,e=r.database,n=void 0===e?"flinksql":e;Object(p.setParserOpt)(r);var o=n.toLowerCase();if(l[o])return l[o](t.trim(),r.parseOptions||p.DEFAULT_OPT.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(t,r){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p.DEFAULT_OPT;if(r&&0!==r.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var a,u=this["".concat(o,"List")].bind(this),i=u(t,e),s=!0,c="",l=v(i);try{for(l.s();!(a=l.n()).done;){var f,b=a.value,h=!1,d=v(r);try{for(d.s();!(f=d.n()).done;){var y=f.value,O=new RegExp(y,"i");if(O.test(b)){h=!0;break}}}catch(t){d.e(t)}finally{d.f()}if(!h){c=b,s=!1;break}}}catch(t){l.e(t)}finally{l.f()}if(!s)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(t,"'"))}}},{key:"tableList",value:function(t,r){var e=this.parse(t,r);return e&&e.tableList}},{key:"columnList",value:function(t,r){var e=this.parse(t,r);return e&&e.columnList}}])&&d(r.prototype,e),o&&d(r,o),Object.defineProperty(r,"prototype",{writable:!1}),t}();r.a=O},function(t,r,e){"use strict";var n=e(28);function o(t,r,e,n){this.message=t,this.expected=r,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(t,r){function e(){this.constructor=t}e.prototype=r.prototype,t.prototype=new e}(o,Error),o.buildMessage=function(t,r){var e={literal:function(t){return'"'+o(t.text)+'"'},class:function(t){var r,e="";for(r=0;r<t.parts.length;r++)e+=t.parts[r]instanceof Array?a(t.parts[r][0])+"-"+a(t.parts[r][1]):a(t.parts[r]);return"["+(t.inverted?"^":"")+e+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function n(t){return t.charCodeAt(0).toString(16).toUpperCase()}function o(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}function a(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(t){return"\\x0"+n(t)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(t){return"\\x"+n(t)}))}return"Expected "+function(t){var r,n,o,a=new Array(t.length);for(r=0;r<t.length;r++)a[r]=(o=t[r],e[o.type](o));if(a.sort(),a.length>0){for(r=1,n=1;r<a.length;r++)a[r-1]!==a[r]&&(a[n]=a[r],n++);a.length=n}switch(a.length){case 1:return a[0];case 2:return a[0]+" or "+a[1];default:return a.slice(0,-1).join(", ")+", or "+a[a.length-1]}}(t)+" but "+function(t){return t?'"'+o(t)+'"':"end of input"}(r)+" found."},t.exports={SyntaxError:o,parse:function(t,r){r=void 0!==r?r:{};var e,a={},u={start:ru},i=ru,s=Xa("IF",!0),c=Xa("EXTENSION",!0),l=Xa("SCHEMA",!0),f=Xa("VERSION",!0),p=function(t,r){return nl(t,r,1)},b=Xa("NULLS",!0),v=Xa("FIRST",!0),h=Xa("LAST",!0),d=Xa("AUTO_INCREMENT",!0),y=Xa("UNIQUE",!0),O=Xa("KEY",!0),m=Xa("PRIMARY",!0),E=Xa("COLUMN_FORMAT",!0),j=Xa("FIXED",!0),w=Xa("DYNAMIC",!0),L=Xa("DEFAULT",!0),C=Xa("STORAGE",!0),T=Xa("DISK",!0),S=Xa("MEMORY",!0),A=Xa("ALGORITHM",!0),g=Xa("INSTANT",!0),R=Xa("INPLACE",!0),I=Xa("COPY",!0),N=Xa("LOCK",!0),U=Xa("NONE",!0),_=Xa("SHARED",!0),x=Xa("EXCLUSIVE",!0),k=Xa("PRIMARY KEY",!0),M=Xa("FOREIGN KEY",!0),V=Xa("MATCH FULL",!0),P=Xa("MATCH PARTIAL",!0),D=Xa("MATCH SIMPLE",!0),q=Xa("RESTRICT",!0),B=Xa("CASCADE",!0),G=Xa("SET NULL",!0),F=Xa("NO ACTION",!0),H=Xa("SET DEFAULT",!0),Y=Xa("TRIGGER",!0),Q=Xa("BEFORE",!0),$=Xa("AFTER",!0),W=Xa("INSTEAD OF",!0),X=Xa("ON",!0),Z=Xa("EXECUTE",!0),K=Xa("PROCEDURE",!0),J=Xa("FUNCTION",!0),z=Xa("OF",!0),tt=Xa("NOT",!0),rt=Xa("DEFERRABLE",!0),et=Xa("INITIALLY IMMEDIATE",!0),nt=Xa("INITIALLY DEFERRED",!0),ot=Xa("FOR",!0),at=Xa("EACH",!0),ut=Xa("ROW",!0),it=Xa("STATEMENT",!0),st=Xa("CHARACTER",!0),ct=Xa("SET",!0),lt=Xa("CHARSET",!0),ft=Xa("COLLATE",!0),pt=Xa("AVG_ROW_LENGTH",!0),bt=Xa("KEY_BLOCK_SIZE",!0),vt=Xa("MAX_ROWS",!0),ht=Xa("MIN_ROWS",!0),dt=Xa("STATS_SAMPLE_PAGES",!0),yt=Xa("CONNECTION",!0),Ot=Xa("COMPRESSION",!0),mt=Xa("'",!1),Et=Xa("ZLIB",!0),jt=Xa("LZ4",!0),wt=Xa("ENGINE",!0),Lt=Xa("IN",!0),Ct=Xa("ACCESS SHARE",!0),Tt=Xa("ROW SHARE",!0),St=Xa("ROW EXCLUSIVE",!0),At=Xa("SHARE UPDATE EXCLUSIVE",!0),gt=Xa("SHARE ROW EXCLUSIVE",!0),Rt=Xa("ACCESS EXCLUSIVE",!0),It=Xa("SHARE",!0),Nt=Xa("MODE",!0),Ut=Xa("NOWAIT",!0),_t=Xa("(",!1),xt=Xa(")",!1),kt=Xa("BTREE",!0),Mt=Xa("HASH",!0),Vt=Xa("GIST",!0),Pt=Xa("GIN",!0),Dt=Xa("WITH",!0),qt=Xa("PARSER",!0),Bt=Xa("VISIBLE",!0),Gt=Xa("INVISIBLE",!0),Ft=function(t,r){return r.unshift(t),r.forEach(t=>{const{table:r,as:e}=t;fl[r]=r,e&&(fl[e]=r),function(t){const r=ul(t);t.clear(),r.forEach(r=>t.add(r))}(ll)}),r},Ht=Xa("DESCRIPTOR",!0),Yt=Xa("=",!1),Qt=function(t,r){return ol(t,r)},$t=Xa("!",!1),Wt=Xa(">=",!1),Xt=Xa(">",!1),Zt=Xa("<=",!1),Kt=Xa("<>",!1),Jt=Xa("<",!1),zt=Xa("!=",!1),tr=Xa("ESCAPE",!0),rr=Xa("@>",!1),er=Xa("<@",!1),nr=Xa("?",!1),or=Xa("?|",!1),ar=Xa("?&",!1),ur=Xa("#-",!1),ir=Xa("+",!1),sr=Xa("-",!1),cr=Xa("*",!1),lr=Xa("/",!1),fr=Xa("%",!1),pr=Xa("$",!1),br=function(t){return!0===zc[t.toUpperCase()]},vr=Xa('"',!1),hr=/^[^"]/,dr=Za(['"'],!0,!1),yr=function(t){return t.join("")},Or=/^[^']/,mr=Za(["'"],!0,!1),Er=Xa("`",!1),jr=/^[^`]/,wr=Za(["`"],!0,!1),Lr=/^[A-Za-z_]/,Cr=Za([["A","Z"],["a","z"],"_"],!1,!1),Tr=/^[A-Za-z0-9_\-]/,Sr=Za([["A","Z"],["a","z"],["0","9"],"_","-"],!1,!1),Ar=/^[A-Za-z0-9_]/,gr=Za([["A","Z"],["a","z"],["0","9"],"_"],!1,!1),Rr=Xa(":",!1),Ir=Xa("OVER",!0),Nr=Xa("POSITION",!0),Ur=Xa("BOTH",!0),_r=Xa("LEADING",!0),xr=Xa("TRAILING",!0),kr=Xa("trim",!0),Mr=Xa("placing",!0),Vr=Xa("for",!0),Pr=Xa("overlay",!0),Dr=Xa("SUBSTRING",!0),qr=Xa("CENTURY",!0),Br=Xa("DAY",!0),Gr=Xa("DATE",!0),Fr=Xa("DECADE",!0),Hr=Xa("DOW",!0),Yr=Xa("DOY",!0),Qr=Xa("EPOCH",!0),$r=Xa("HOUR",!0),Wr=Xa("ISODOW",!0),Xr=Xa("ISOYEAR",!0),Zr=Xa("MICROSECONDS",!0),Kr=Xa("MILLENNIUM",!0),Jr=Xa("MILLISECONDS",!0),zr=Xa("MINUTE",!0),te=Xa("MONTH",!0),re=Xa("QUARTER",!0),ee=Xa("SECOND",!0),ne=Xa("TIMEZONE",!0),oe=Xa("TIMEZONE_HOUR",!0),ae=Xa("TIMEZONE_MINUTE",!0),ue=Xa("WEEK",!0),ie=Xa("YEAR",!0),se=/^[^"\\\0-\x1F\x7F]/,ce=Za(['"',"\\",["\0",""],""],!0,!1),le=/^[^'\\]/,fe=Za(["'","\\"],!0,!1),pe=Xa("\\'",!1),be=Xa('\\"',!1),ve=Xa("\\\\",!1),he=Xa("\\/",!1),de=Xa("\\b",!1),ye=Xa("\\f",!1),Oe=Xa("\\n",!1),me=Xa("\\r",!1),Ee=Xa("\\t",!1),je=Xa("\\u",!1),we=Xa("\\",!1),Le=Xa("''",!1),Ce=Xa('""',!1),Te=Xa("``",!1),Se=/^[\n\r]/,Ae=Za(["\n","\r"],!1,!1),ge=Xa(".",!1),Re=/^[0-9]/,Ie=Za([["0","9"]],!1,!1),Ne=/^[0-9a-fA-F]/,Ue=Za([["0","9"],["a","f"],["A","F"]],!1,!1),_e=/^[eE]/,xe=Za(["e","E"],!1,!1),ke=/^[+\-]/,Me=Za(["+","-"],!1,!1),Ve=Xa("NULL",!0),Pe=Xa("NOT NULL",!0),De=Xa("TRUE",!0),qe=Xa("TO",!0),Be=Xa("FALSE",!0),Ge=(Xa("SHOW",!0),Xa("DROP",!0)),Fe=Xa("USE",!0),He=Xa("ALTER",!0),Ye=Xa("SELECT",!0),Qe=Xa("UPDATE",!0),$e=Xa("CREATE",!0),We=Xa("TEMPORARY",!0),Xe=Xa("DELETE",!0),Ze=Xa("INSERT",!0),Ke=Xa("RECURSIVE",!1),Je=Xa("REPLACE",!0),ze=Xa("RETURNING",!0),tn=Xa("RENAME",!0),rn=Xa("IGNORE",!0),en=(Xa("EXPLAIN",!0),Xa("PARTITION",!0)),nn=Xa("INTO",!0),on=Xa("FROM",!0),an=Xa("AS",!0),un=Xa("TABLE",!0),sn=Xa("TABLESPACE",!0),cn=Xa("DATABASE",!0),ln=Xa("SCHEME",!0),fn=Xa("NATURAL",!0),pn=Xa("LEFT",!0),bn=Xa("RIGHT",!0),vn=Xa("FULL",!0),hn=Xa("INNER",!0),dn=Xa("JOIN",!0),yn=Xa("CROSS",!0),On=Xa("APPLY",!0),mn=Xa("OUTER",!0),En=Xa("UNION",!0),jn=Xa("INTERSECT",!0),wn=Xa("EXCEPT",!0),Ln=Xa("VALUES",!0),Cn=Xa("USING",!0),Tn=Xa("WHERE",!0),Sn=Xa("GROUP",!0),An=Xa("BY",!0),gn=Xa("ORDER",!0),Rn=Xa("HAVING",!0),In=Xa("LIMIT",!0),Nn=Xa("OFFSET",!0),Un=Xa("ASC",!0),_n=Xa("DESC",!0),xn=Xa("ALL",!0),kn=Xa("DISTINCT",!0),Mn=Xa("BETWEEN",!0),Vn=Xa("IS",!0),Pn=Xa("LIKE",!0),Dn=Xa("SIMILAR",!0),qn=Xa("EXISTS",!0),Bn=Xa("AND",!0),Gn=Xa("OR",!0),Fn=Xa("COUNT",!0),Hn=Xa("MAX",!0),Yn=Xa("MIN",!0),Qn=Xa("SUM",!0),$n=Xa("AVG",!0),Wn=Xa("COLLECT",!0),Xn=Xa("RANK",!0),Zn=Xa("DENSE_RANK",!0),Kn=Xa("LISTAGG",!0),Jn=Xa("ROW_NUMBER",!0),zn=Xa("TUMBLE",!0),to=(Xa("TUMBLE_START",!0),Xa("TUMBLE_END",!0),Xa("HOP_START",!0),Xa("HOP_END",!0),Xa("SESSION_START",!0),Xa("SESSION_END",!0),Xa("TUMBLE_ROWTIME",!0),Xa("HOP_ROWTIME",!0),Xa("SESSION_ROWTIME",!0),Xa("TUMBLE_PROCTIME",!0),Xa("HOP_PROCTIME",!0),Xa("SESSION_PROCTIME",!0),Xa("EXTRACT",!0)),ro=Xa("CALL",!0),eo=Xa("CASE",!0),no=Xa("WHEN",!0),oo=Xa("THEN",!0),ao=Xa("ELSE",!0),uo=Xa("END",!0),io=Xa("CAST",!0),so=Xa("TRY_CAST",!0),co=Xa("BOOL",!0),lo=Xa("BOOLEAN",!0),fo=Xa("CHAR",!0),po=Xa("VARCHAR",!0),bo=Xa("STRING",!0),vo=Xa("NUMERIC",!0),ho=Xa("DECIMAL",!0),yo=Xa("SIGNED",!0),Oo=Xa("UNSIGNED",!0),mo=Xa("INT",!0),Eo=Xa("ZEROFILL",!0),jo=Xa("INTEGER",!0),wo=Xa("JSON",!0),Lo=Xa("JSONB",!0),Co=Xa("GEOMETRY",!0),To=Xa("SMALLINT",!0),So=Xa("TINYINT",!0),Ao=Xa("TINYTEXT",!0),go=Xa("TEXT",!0),Ro=Xa("MEDIUMTEXT",!0),Io=Xa("LONGTEXT",!0),No=Xa("BIGINT",!0),Uo=Xa("FLOAT",!0),_o=Xa("DOUBLE",!0),xo=Xa("DATETIME",!0),ko=Xa("TIME",!0),Mo=Xa("TIMESTAMP",!0),Vo=Xa("TRUNCATE",!0),Po=Xa("USER",!0),Do=Xa("UUID",!0),qo=Xa("ARRAY",!0),Bo=Xa("MAP",!0),Go=(Xa("MULTISET",!0),Xa("CURRENT_DATE",!0)),Fo=(Xa("ADDDATE",!0),Xa("INTERVAL",!0)),Ho=(Xa("SECONDS",!0),Xa("CURRENT_TIME",!0)),Yo=Xa("CURRENT_TIMESTAMP",!0),Qo=Xa("CURRENT_USER",!0),$o=Xa("SESSION_USER",!0),Wo=Xa("SYSTEM_USER",!0),Xo=Xa("GLOBAL",!0),Zo=Xa("SESSION",!0),Ko=Xa("LOCAL",!0),Jo=Xa("PERSIST",!0),zo=Xa("PERSIST_ONLY",!0),ta=Xa("@",!1),ra=Xa("@@",!1),ea=Xa("return",!0),na=Xa(":=",!1),oa=Xa("::",!1),aa=Xa("DUAL",!0),ua=Xa("ADD",!0),ia=Xa("COLUMN",!0),sa=Xa("INDEX",!0),ca=Xa("FULLTEXT",!0),la=Xa("SPATIAL",!0),fa=Xa("COMMENT",!0),pa=Xa("CONSTRAINT",!0),ba=Xa("CONCURRENTLY",!0),va=Xa("REFERENCES",!0),ha=Xa("SQL_CALC_FOUND_ROWS",!0),da=Xa("SQL_CACHE",!0),ya=Xa("SQL_NO_CACHE",!0),Oa=Xa("SQL_SMALL_RESULT",!0),ma=Xa("SQL_BIG_RESULT",!0),Ea=Xa("SQL_BUFFER_RESULT",!0),ja=Xa(",",!1),wa=Xa("[",!1),La=Xa("]",!1),Ca=Xa(";",!1),Ta=Xa("->",!1),Sa=Xa("->>",!1),Aa=Xa("#>",!1),ga=Xa("#>>",!1),Ra=Xa("||",!1),Ia=Xa("&&",!1),Na=Xa("/*",!1),Ua=Xa("*/",!1),_a=Xa("--",!1),xa=(Xa("#",!1),{type:"any"}),ka=Xa("years",!0),Ma=Xa("months",!0),Va=Xa("days",!0),Pa=Xa("hours",!0),Da=Xa("minutes",!0),qa=Xa("seconds",!0),Ba=/^[ \t\n\r]/,Ga=Za([" ","\t","\n","\r"],!1,!1),Fa=function(t){return{dataType:t}},Ha=0,Ya=[{line:1,column:1}],Qa=0,$a=[],Wa=0;if("startRule"in r){if(!(r.startRule in u))throw new Error("Can't start parsing from rule \""+r.startRule+'".');i=u[r.startRule]}function Xa(t,r){return{type:"literal",text:t,ignoreCase:r}}function Za(t,r,e){return{type:"class",parts:t,inverted:r,ignoreCase:e}}function Ka(r){var e,n=Ya[r];if(n)return n;for(e=r-1;!Ya[e];)e--;for(n={line:(n=Ya[e]).line,column:n.column};e<r;)10===t.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Ya[r]=n,n}function Ja(t,r){var e=Ka(t),n=Ka(r);return{start:{offset:t,line:e.line,column:e.column},end:{offset:r,line:n.line,column:n.column}}}function za(t){Ha<Qa||(Ha>Qa&&(Qa=Ha,$a=[]),$a.push(t))}function tu(t,r,e){return new o(o.buildMessage(t,r),t,r,e)}function ru(){var t,r;return t=Ha,_c()!==a&&(r=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=nu())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=gc())!==a&&(i=_c())!==a&&(s=nu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=gc())!==a&&(i=_c())!==a&&(s=nu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=function(t,r){const e=t&&t.ast||t,n=r&&r.length&&r[0].length>=4?[e]:e;for(let t=0;t<r.length;t++)r[t][3]&&0!==r[t][3].length&&n.push(r[t][3]&&r[t][3].ast||r[t][3]);return{tableList:Array.from(cl),columnList:ul(ll),ast:n}}(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())!==a?(t,t=r):(Ha=t,t=a),t}function eu(){var r;return(r=function(){var t,r,e,n,o,u;t=Ha,(r=as())!==a&&_c()!==a&&(e=ms())!==a&&_c()!==a&&(n=Vu())!==a?(t,i=r,s=e,(c=n)&&c.forEach(t=>cl.add(`${i}::${t.db}::${t.table}`)),r={tableList:Array.from(cl),columnList:ul(ll),ast:{type:i.toLowerCase(),keyword:s.toLowerCase(),name:c}},t=r):(Ha=t,t=a);var i,s,c;t===a&&(t=Ha,(r=as())!==a&&_c()!==a&&(e=dc())!==a&&_c()!==a&&(n=Li())!==a&&_c()!==a&&Es()!==a&&_c()!==a&&(o=Bu())!==a&&_c()!==a?((u=function(){var t,r,e,n,o,u;t=Ha,(r=bu())===a&&(r=vu());if(r!==a){for(e=[],n=Ha,(o=_c())!==a?((u=bu())===a&&(u=vu()),u!==a?n=o=[o,u]:(Ha=n,n=a)):(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a?((u=bu())===a&&(u=vu()),u!==a?n=o=[o,u]:(Ha=n,n=a)):(Ha=n,n=a);e!==a?(t,r=p(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())===a&&(u=null),u!==a&&_c()!==a?(t,r=function(t,r,e,n,o){return{tableList:Array.from(cl),columnList:ul(ll),ast:{type:t.toLowerCase(),keyword:r.toLowerCase(),name:e,table:n,options:o}}}(r,e,n,o,u),t=r):(Ha=t,t=a)):(Ha=t,t=a));return t}())===a&&(r=function(){var r;(r=function(){var t,r,e,n,o,u,i,s,c,l;t=Ha,(r=is())!==a&&_c()!==a?((e=ss())===a&&(e=null),e!==a&&_c()!==a&&ms()!==a&&_c()!==a?((n=uu())===a&&(n=null),n!==a&&_c()!==a&&(o=Vu())!==a&&_c()!==a&&(u=function(){var t,r,e,n,o,u,i,s,c;if(t=Ha,(r=Cc())!==a)if(_c()!==a)if((e=su())!==a){for(n=[],o=Ha,(u=_c())!==a&&(i=wc())!==a&&(s=_c())!==a&&(c=su())!==a?o=u=[u,i,s,c]:(Ha=o,o=a);o!==a;)n.push(o),o=Ha,(u=_c())!==a&&(i=wc())!==a&&(s=_c())!==a&&(c=su())!==a?o=u=[u,i,s,c]:(Ha=o,o=a);n!==a&&(o=_c())!==a&&(u=Tc())!==a?(t,r=nl(e,n),t=r):(Ha=t,t=a)}else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;return t}())!==a&&_c()!==a?((i=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=wu())!==a){for(e=[],n=Ha,(o=_c())!==a?((u=wc())===a&&(u=null),u!==a&&(i=_c())!==a&&(s=wu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a)):(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a?((u=wc())===a&&(u=null),u!==a&&(i=_c())!==a&&(s=wu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a)):(Ha=n,n=a);e!==a?(t,r=nl(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())===a&&(i=null),i!==a&&_c()!==a?((s=bs())===a&&(s=fs()),s===a&&(s=null),s!==a&&_c()!==a?((c=Os())===a&&(c=null),c!==a&&_c()!==a?((l=au())===a&&(l=null),l!==a?(t,f=r,p=e,b=n,h=u,d=i,y=s,O=c,m=l,(v=o)&&v.forEach(t=>cl.add(`create::${t.db}::${t.table}`)),r={tableList:Array.from(cl),columnList:ul(ll),ast:{type:f[0].toLowerCase(),keyword:"table",temporary:p&&p[0].toLowerCase(),if_not_exists:b,table:v,ignore_replace:y&&y[0].toLowerCase(),as:O&&O[0].toLowerCase(),query_expr:m&&m.ast,create_definitions:h,table_options:d}},t=r):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a);var f,p,b,v,h,d,y,O,m;t===a&&(t=Ha,(r=is())!==a&&_c()!==a?((e=ss())===a&&(e=null),e!==a&&_c()!==a&&ms()!==a&&_c()!==a?((n=uu())===a&&(n=null),n!==a&&_c()!==a&&(o=Vu())!==a&&_c()!==a&&(u=function t(){var r,e;(r=function(){var t,r;t=Ha,ks()!==a&&_c()!==a&&(r=Vu())!==a?(t,t={type:"like",table:r}):(Ha=t,t=a);return t}())===a&&(r=Ha,Cc()!==a&&_c()!==a&&(e=t())!==a&&_c()!==a&&Tc()!==a?(r,(n=e).parentheses=!0,r=n):(Ha=r,r=a));var n;return r}())!==a?(t,r=function(t,r,e,n,o){return n&&n.forEach(t=>cl.add(`create::${t.db}::${t.table}`)),{tableList:Array.from(cl),columnList:ul(ll),ast:{type:t[0].toLowerCase(),keyword:"table",temporary:r&&r[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(r,e,n,o,u),t=r):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a));return t}())===a&&(r=function(){var r,e,n,o,u,i,s,c,l,f,p,b,v,h,d,y,O,m,E,j,w;r=Ha,(e=is())!==a&&_c()!==a?(n=Ha,(o=qs())!==a&&(u=_c())!==a&&(i=fs())!==a?n=o=[o,u,i]:(Ha=n,n=a),n===a&&(n=null),n!==a&&(o=_c())!==a?((u=Ec())===a&&(u=null),u!==a&&(i=_c())!==a?("trigger"===t.substr(Ha,7).toLowerCase()?(s=t.substr(Ha,7),Ha+=7):(s=a,0===Wa&&za(Y)),s!==a&&_c()!==a&&(c=Ri())!==a&&_c()!==a?("before"===t.substr(Ha,6).toLowerCase()?(l=t.substr(Ha,6),Ha+=6):(l=a,0===Wa&&za(Q)),l===a&&("after"===t.substr(Ha,5).toLowerCase()?(l=t.substr(Ha,5),Ha+=5):(l=a,0===Wa&&za($)),l===a&&("instead of"===t.substr(Ha,10).toLowerCase()?(l=t.substr(Ha,10),Ha+=10):(l=a,0===Wa&&za(W)))),l!==a&&_c()!==a&&(f=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Eu())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=qs())!==a&&(i=_c())!==a&&(s=Eu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=qs())!==a&&(i=_c())!==a&&(s=Eu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=nl(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())!==a&&_c()!==a?("on"===t.substr(Ha,2).toLowerCase()?(p=t.substr(Ha,2),Ha+=2):(p=a,0===Wa&&za(X)),p!==a&&_c()!==a&&(b=Bu())!==a&&_c()!==a?(v=Ha,(h=ds())!==a&&(d=_c())!==a&&(y=Bu())!==a?v=h=[h,d,y]:(Ha=v,v=a),v===a&&(v=null),v!==a&&(h=_c())!==a?((d=function(){var r,e,n,o,u;r=Ha,e=Ha,"not"===t.substr(Ha,3).toLowerCase()?(n=t.substr(Ha,3),Ha+=3):(n=a,0===Wa&&za(tt));n===a&&(n=null);n!==a&&(o=_c())!==a?("deferrable"===t.substr(Ha,10).toLowerCase()?(u=t.substr(Ha,10),Ha+=10):(u=a,0===Wa&&za(rt)),u!==a?e=n=[n,o,u]:(Ha=e,e=a)):(Ha=e,e=a);e!==a&&(n=_c())!==a?("initially immediate"===t.substr(Ha,19).toLowerCase()?(o=t.substr(Ha,19),Ha+=19):(o=a,0===Wa&&za(et)),o===a&&("initially deferred"===t.substr(Ha,18).toLowerCase()?(o=t.substr(Ha,18),Ha+=18):(o=a,0===Wa&&za(nt))),o!==a?(r,s=o,e={keyword:(i=e)&&i[0]?i[0].toLowerCase()+" deferrable":"deferrable",args:s&&s.toLowerCase()},r=e):(Ha=r,r=a)):(Ha=r,r=a);var i,s;return r}())===a&&(d=null),d!==a&&(y=_c())!==a?((O=function(){var r,e,n,o;r=Ha,"for"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(ot));e!==a&&_c()!==a?("each"===t.substr(Ha,4).toLowerCase()?(n=t.substr(Ha,4),Ha+=4):(n=a,0===Wa&&za(at)),n===a&&(n=null),n!==a&&_c()!==a?("row"===t.substr(Ha,3).toLowerCase()?(o=t.substr(Ha,3),Ha+=3):(o=a,0===Wa&&za(ut)),o===a&&("statement"===t.substr(Ha,9).toLowerCase()?(o=t.substr(Ha,9),Ha+=9):(o=a,0===Wa&&za(it))),o!==a?(r,u=e,s=o,e={keyword:(i=n)?`${u.toLowerCase()} ${i.toLowerCase()}`:u.toLowerCase(),args:s.toLowerCase()},r=e):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a);var u,i,s;return r}())===a&&(O=null),O!==a&&_c()!==a?((m=function(){var t,r;t=Ha,Fs()!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(r=ii())!==a&&_c()!==a&&Tc()!==a?(t,t={type:"when",cond:r,parentheses:!0}):(Ha=t,t=a);return t}())===a&&(m=null),m!==a&&_c()!==a?("execute"===t.substr(Ha,7).toLowerCase()?(E=t.substr(Ha,7),Ha+=7):(E=a,0===Wa&&za(Z)),E!==a&&_c()!==a?("procedure"===t.substr(Ha,9).toLowerCase()?(j=t.substr(Ha,9),Ha+=9):(j=a,0===Wa&&za(K)),j===a&&("function"===t.substr(Ha,8).toLowerCase()?(j=t.substr(Ha,8),Ha+=8):(j=a,0===Wa&&za(J))),j!==a&&_c()!==a&&(w=$c())!==a?(r,L=u,C=s,S=f,A=b,g=v,R=d,I=O,N=m,U=j,_=w,e={type:"create",replace:n&&"or replace",constraint:c,location:(T=l)&&T.toLowerCase(),events:S,table:A,from:g&&g[2],deferrable:R,for_each:I,when:N,execute:{keyword:"execute "+U.toLowerCase(),expr:_},constraint_type:C&&C.toLowerCase(),keyword:C&&C.toLowerCase(),constraint_kw:L&&L.toLowerCase(),resource:"constraint"},r=e):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a);var L,C,T,S,A,g,R,I,N,U,_;return r}())===a&&(r=function(){var r,e,n,o,u,i,s,p,b,v,h,d,y,O;r=Ha,(e=is())!==a&&_c()!==a?("extension"===t.substr(Ha,9).toLowerCase()?(n=t.substr(Ha,9),Ha+=9):(n=a,0===Wa&&za(c)),n!==a&&_c()!==a?((o=uu())===a&&(o=null),o!==a&&_c()!==a?((u=Ri())===a&&(u=Qi()),u!==a&&_c()!==a?((i=Ss())===a&&(i=null),i!==a&&_c()!==a?(s=Ha,"schema"===t.substr(Ha,6).toLowerCase()?(p=t.substr(Ha,6),Ha+=6):(p=a,0===Wa&&za(l)),p!==a&&(b=_c())!==a&&(v=Ri())!==a?s=p=[p,b,v]:(Ha=s,s=a),s===a&&(s=Qi()),s===a&&(s=null),s!==a&&(p=_c())!==a?(b=Ha,"version"===t.substr(Ha,7).toLowerCase()?(v=t.substr(Ha,7),Ha+=7):(v=a,0===Wa&&za(f)),v!==a&&(h=_c())!==a?((d=Ri())===a&&(d=Qi()),d!==a?b=v=[v,h,d]:(Ha=b,b=a)):(Ha=b,b=a),b===a&&(b=null),b!==a&&(v=_c())!==a?(h=Ha,(d=ds())!==a&&(y=_c())!==a?((O=Ri())===a&&(O=Qi()),O!==a?h=d=[d,y,O]:(Ha=h,h=a)):(Ha=h,h=a),h===a&&(h=null),h!==a?(r,m=o,E=u,j=i,w=s,L=b,C=h,e={type:"create",keyword:n.toLowerCase(),if_not_exists:m,extension:il(E),with:j&&j[0].toLowerCase(),schema:il(w&&w[2].toLowerCase()),version:il(L&&L[2]),from:il(C&&C[2])},r=e):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a);var m,E,j,w,L,C;return r}())===a&&(r=function(){var r,e,n,o,u,i,s,c,l,f,p,b,v,h,d,y,O,m;r=Ha,(e=is())!==a&&_c()!==a?((n=Oc())===a&&(n=null),n!==a&&_c()!==a&&(o=dc())!==a&&_c()!==a?((u=function(){var r,e,n,o;r=Ha,"concurrently"===t.substr(Ha,12).toLowerCase()?(e=t.substr(Ha,12),Ha+=12):(e=a,0===Wa&&za(ba));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="CONCURRENTLY"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(u=null),u!==a&&_c()!==a?((i=Ci())===a&&(i=null),i!==a&&_c()!==a&&(s=Es())!==a&&_c()!==a&&(c=Bu())!==a&&_c()!==a?((l=xu())===a&&(l=null),l!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(f=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=iu())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=iu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=iu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=nl(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())!==a&&_c()!==a&&Tc()!==a&&_c()!==a?(p=Ha,(b=Ss())!==a&&(v=_c())!==a&&(h=Cc())!==a&&(d=_c())!==a&&(y=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Mu())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Mu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Mu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=nl(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())!==a&&(O=_c())!==a&&(m=Tc())!==a?p=b=[b,v,h,d,y,O,m]:(Ha=p,p=a),p===a&&(p=null),p!==a&&(b=_c())!==a?(v=Ha,(h=function(){var r,e,n,o;r=Ha,"tablespace"===t.substr(Ha,10).toLowerCase()?(e=t.substr(Ha,10),Ha+=10):(e=a,0===Wa&&za(sn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="TABLESPACE"):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&(d=_c())!==a&&(y=Ri())!==a?v=h=[h,d,y]:(Ha=v,v=a),v===a&&(v=null),v!==a&&(h=_c())!==a?((d=Hu())===a&&(d=null),d!==a&&(y=_c())!==a?(r,E=e,j=n,w=o,L=u,C=i,T=s,S=c,A=l,g=f,R=p,I=v,N=d,e={tableList:Array.from(cl),columnList:ul(ll),ast:{type:E[0].toLowerCase(),index_type:j&&j.toLowerCase(),keyword:w.toLowerCase(),concurrently:L&&L.toLowerCase(),index:C,on_kw:T[0].toLowerCase(),table:S,index_using:A,index_columns:g,with:R&&R[4],with_before_where:!0,tablespace:I&&{type:"origin",value:I[2]},where:N}},r=e):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a);var E,j,w,L,C,T,S,A,g,R,I,N;return r}())===a&&(r=function(){var r,e,n,o,u,i;r=Ha,(e=is())!==a&&_c()!==a?((n=function(){var r,e,n,o;r=Ha,"database"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(cn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="DATABASE"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=Ha,"scheme"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(ln));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SCHEME"):(Ha=r,r=a)):(Ha=r,r=a);return r}()),n!==a&&_c()!==a?((o=uu())===a&&(o=null),o!==a&&_c()!==a&&(u=Ri())!==a&&_c()!==a?((i=function(){var t,r,e,n,o,u;if(t=Ha,(r=ju())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=ju())!==a?n=o=[o,u]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=ju())!==a?n=o=[o,u]:(Ha=n,n=a);e!==a?(t,r=p(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())===a&&(i=null),i!==a?(r,s=e,c=o,l=u,f=i,e={tableList:Array.from(cl),columnList:ul(ll),ast:{type:s[0].toLowerCase(),keyword:"database",if_not_exists:c,database:l,create_definitions:f}},r=e):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a);var s,c,l,f;return r}());return r}())===a&&(r=function(){var t,r,e,n;t=Ha,(r=sc())!==a&&_c()!==a?((e=ms())===a&&(e=null),e!==a&&_c()!==a&&(n=Vu())!==a?(t,o=r,u=e,(i=n)&&i.forEach(t=>cl.add(`${o}::${t.db}::${t.table}`)),r={tableList:Array.from(cl),columnList:ul(ll),ast:{type:o.toLowerCase(),keyword:u&&u.toLowerCase()||"table",name:i}},t=r):(Ha=t,t=a)):(Ha=t,t=a);var o,u,i;return t}())===a&&(r=function(){var t,r,e;t=Ha,(r=ps())!==a&&_c()!==a&&ms()!==a&&_c()!==a&&(e=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=_u())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=_u())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=_u())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=nl(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())!==a?(t,(n=e).forEach(t=>t.forEach(t=>t.table&&cl.add(`rename::${t.db}::${t.table}`))),r={tableList:Array.from(cl),columnList:ul(ll),ast:{type:"rename",table:n}},t=r):(Ha=t,t=a);var n;return t}())===a&&(r=function(){var r,e,n;r=Ha,(e=function(){var r,e,n,o;r=Ha,"call"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(ro));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="CALL"):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&_c()!==a&&(n=$c())!==a?(r,o=n,e={tableList:Array.from(cl),columnList:ul(ll),ast:{type:"call",expr:o}},r=e):(Ha=r,r=a);var o;return r}())===a&&(r=function(){var r,e,n;r=Ha,(e=function(){var r,e,n,o;r=Ha,"use"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Fe));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&_c()!==a&&(n=Ci())!==a?(r,o=n,cl.add(`use::${o}::null`),e={tableList:Array.from(cl),columnList:ul(ll),ast:{type:"use",db:o}},r=e):(Ha=r,r=a);var o;return r}())===a&&(r=function(){var r,e,n,o;r=Ha,(e=function(){var r,e,n,o;r=Ha,"alter"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(He));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&_c()!==a&&ms()!==a&&_c()!==a&&(n=Vu())!==a&&_c()!==a&&(o=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=pu())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=pu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=pu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=nl(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())!==a?(r,i=o,(u=n)&&u.length>0&&u.forEach(t=>cl.add(`alter::${t.db}::${t.table}`)),e={tableList:Array.from(cl),columnList:ul(ll),ast:{type:"alter",table:u,expr:i}},r=e):(Ha=r,r=a);var u,i;return r}())===a&&(r=function(){var r,e,n,o;r=Ha,(e=ys())!==a&&_c()!==a?((n=function(){var r,e,n,o;r=Ha,"global"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Xo));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="GLOBAL"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=Ha,"session"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Zo));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SESSION"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=Ha,"local"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(Ko));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="LOCAL"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=Ha,"persist"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Jo));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="PERSIST"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(n=function(){var r,e,n,o;r=Ha,"persist_only"===t.substr(Ha,12).toLowerCase()?(e=t.substr(Ha,12),Ha+=12):(e=a,0===Wa&&za(zo));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="PERSIST_ONLY"):(Ha=r,r=a)):(Ha=r,r=a);return r}()),n===a&&(n=null),n!==a&&_c()!==a&&(o=Bc())!==a?(r,u=n,(i=o).keyword=u,e={tableList:Array.from(cl),columnList:ul(ll),ast:{type:"set",expr:i}},r=e):(Ha=r,r=a)):(Ha=r,r=a);var u,i;return r}())===a&&(r=function(){var r,e,n,o,u,i;r=Ha,(e=function(){var r,e,n,o;r=Ha,"lock"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(N));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&_c()!==a?((n=ms())===a&&(n=null),n!==a&&_c()!==a&&(o=Vu())!==a&&_c()!==a?((u=function(){var r,e,n,o;r=Ha,"in"===t.substr(Ha,2).toLowerCase()?(e=t.substr(Ha,2),Ha+=2):(e=a,0===Wa&&za(Lt));e!==a&&_c()!==a?("access share"===t.substr(Ha,12).toLowerCase()?(n=t.substr(Ha,12),Ha+=12):(n=a,0===Wa&&za(Ct)),n===a&&("row share"===t.substr(Ha,9).toLowerCase()?(n=t.substr(Ha,9),Ha+=9):(n=a,0===Wa&&za(Tt)),n===a&&("row exclusive"===t.substr(Ha,13).toLowerCase()?(n=t.substr(Ha,13),Ha+=13):(n=a,0===Wa&&za(St)),n===a&&("share update exclusive"===t.substr(Ha,22).toLowerCase()?(n=t.substr(Ha,22),Ha+=22):(n=a,0===Wa&&za(At)),n===a&&("share row exclusive"===t.substr(Ha,19).toLowerCase()?(n=t.substr(Ha,19),Ha+=19):(n=a,0===Wa&&za(gt)),n===a&&("exclusive"===t.substr(Ha,9).toLowerCase()?(n=t.substr(Ha,9),Ha+=9):(n=a,0===Wa&&za(x)),n===a&&("access exclusive"===t.substr(Ha,16).toLowerCase()?(n=t.substr(Ha,16),Ha+=16):(n=a,0===Wa&&za(Rt)),n===a&&("share"===t.substr(Ha,5).toLowerCase()?(n=t.substr(Ha,5),Ha+=5):(n=a,0===Wa&&za(It))))))))),n!==a&&_c()!==a?("mode"===t.substr(Ha,4).toLowerCase()?(o=t.substr(Ha,4),Ha+=4):(o=a,0===Wa&&za(Nt)),o!==a?(r,e={mode:`in ${n.toLowerCase()} mode`},r=e):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(u=null),u!==a&&_c()!==a?("nowait"===t.substr(Ha,6).toLowerCase()?(i=t.substr(Ha,6),Ha+=6):(i=a,0===Wa&&za(Ut)),i===a&&(i=null),i!==a?(r,s=n,l=u,f=i,(c=o)&&c.forEach(t=>cl.add(`lock::${t.db}::${t.table}`)),e={tableList:Array.from(cl),columnList:ul(ll),ast:{type:"lock",keyword:s&&s.toLowerCase(),tables:c.map(t=>({table:t})),lock_mode:l,nowait:f}},r=e):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a);var s,c,l,f;return r}()),r}function nu(){var t;return(t=au())===a&&(t=function(){var t,r,e,n,o,u;t=Ha,(r=us())!==a&&_c()!==a&&(e=Vu())!==a&&_c()!==a&&ys()!==a&&_c()!==a&&(n=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Zu())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Zu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Zu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=nl(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())!==a&&_c()!==a?((o=Hu())===a&&(o=null),o!==a&&_c()!==a?((u=Ku())===a&&(u=null),u!==a?(t,r=function(t,r,e,n){const o={};return t&&t.forEach(t=>{const{db:r,as:e,table:n,join:a}=t,u=a?"select":"update";r&&(o[n]=r),n&&cl.add(`${u}::${r}::${n}`)}),r&&r.forEach(t=>{if(t.table){const r=al(t.table);cl.add(`update::${o[r]||null}::${r}`)}ll.add(`update::${t.table}::${t.column}`)}),{tableList:Array.from(cl),columnList:ul(ll),ast:{type:"update",table:t,set:r,where:e,returning:n}}}(e,n,o,u),t=r):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o,u,i,s;t=Ha,(r=ti())!==a&&_c()!==a?((e=hs())===a&&(e=null),e!==a&&_c()!==a&&(n=Bu())!==a&&_c()!==a?((o=zu())===a&&(o=null),o!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(u=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Ai())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Ai())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Ai())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=nl(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())!==a&&_c()!==a&&Tc()!==a&&_c()!==a&&(i=Ju())!==a&&_c()!==a?((s=Ku())===a&&(s=null),s!==a?(t,r=function(t,r,e,n,o,a){if(r&&(cl.add(`insert::${r.db}::${r.table}`),r.as=null),n){let t=r&&r.table||null;Array.isArray(o)&&o.forEach((t,r)=>{if(t.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(r+1))}),n.forEach(r=>ll.add(`insert::${t}::${r}`))}return{tableList:Array.from(cl),columnList:ul(ll),ast:{type:t,table:[r],columns:n,values:o,partition:e,returning:a}}}(r,n,o,u,i,s),t=r):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o,u,i,s;t=Ha,(r=ti())!==a&&_c()!==a?((e=bs())===a&&(e=null),e!==a&&_c()!==a?((n=hs())===a&&(n=null),n!==a&&_c()!==a&&(o=Bu())!==a&&_c()!==a?((u=zu())===a&&(u=null),u!==a&&_c()!==a&&(i=Ju())!==a&&_c()!==a?((s=Ku())===a&&(s=null),s!==a?(t,r=function(t,r,e,n,o,a,u){n&&(cl.add(`insert::${n.db}::${n.table}`),ll.add(`insert::${n.table}::(.*)`),n.as=null);const i=[r,e].filter(t=>t).map(t=>t[0]&&t[0].toLowerCase()).join(" ");return{tableList:Array.from(cl),columnList:ul(ll),ast:{type:t,table:[n],columns:null,values:a,partition:o,prefix:i,returning:u}}}(r,e,n,o,u,i,s),t=r):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a);return t}())===a&&(t=function(){var t,r,e,n,o;t=Ha,(r=cs())!==a&&_c()!==a?((e=Vu())===a&&(e=null),e!==a&&_c()!==a&&(n=Uu())!==a&&_c()!==a?((o=Hu())===a&&(o=null),o!==a?(t,r=function(t,r,e){if(r&&r.forEach(t=>{const{db:r,as:e,table:n,join:o}=t,a=o?"select":"delete";n&&cl.add(`${a}::${r}::${n}`),o||ll.add(`delete::${n}::(.*)`)}),null===t&&1===r.length){const e=r[0];t=[{db:e.db,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(cl),columnList:ul(ll),ast:{type:"delete",table:t,from:r,where:e}}}(e,n,o),t=r):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a);return t}())===a&&(t=eu())===a&&(t=function(){var t,r;t=[],r=qc();for(;r!==a;)t.push(r),r=qc();return t}()),t}function ou(){var r,e,n,o,u;return r=Ha,(e=function(){var r,e,n,o;r=Ha,"union"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(En));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="UNION"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(e=function(){var r,e,n,o;r=Ha,"intersect"===t.substr(Ha,9).toLowerCase()?(e=t.substr(Ha,9),Ha+=9):(e=a,0===Wa&&za(jn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="INTERSECT"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(e=function(){var r,e,n,o;r=Ha,"except"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(wn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="EXCEPT"):(Ha=r,r=a)):(Ha=r,r=a);return r}()),e!==a&&_c()!==a?((n=Is())===a&&(n=Ns()),n===a&&(n=null),n!==a?(r,o=e,r=e=(u=n)?`${o.toLowerCase()} ${u.toLowerCase()}`:""+o.toLowerCase()):(Ha=r,r=a)):(Ha=r,r=a),r}function au(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Lu())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=ou())!==a&&(i=_c())!==a&&(s=Lu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=ou())!==a&&(i=_c())!==a&&(s=Lu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a&&(n=_c())!==a?((o=Qu())===a&&(o=null),o!==a&&(u=_c())!==a?((i=Xu())===a&&(i=null),i!==a?(t,t=r=function(t,r,e,n){let o=t;for(let t=0;t<r.length;t++)o._next=r[t][3],o.set_op=r[t][1],o=o._next;return e&&(t._orderby=e),n&&(t._limit=n),{tableList:Array.from(cl),columnList:ul(ll),ast:t}}(r,e,o,i)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)}else Ha=t,t=a;return t}function uu(){var r,e;return r=Ha,"if"===t.substr(Ha,2).toLowerCase()?(e=t.substr(Ha,2),Ha+=2):(e=a,0===Wa&&za(s)),e!==a&&_c()!==a&&Ps()!==a&&_c()!==a&&Vs()!==a?(r,r=e="IF NOT EXISTS"):(Ha=r,r=a),r}function iu(){var r,e,n,o,u,i,s,c,l,f,p,d,y,O;return r=Ha,(e=ii())!==a&&_c()!==a?((n=fu())===a&&(n=null),n!==a&&_c()!==a?((o=Ci())===a&&(o=null),o!==a&&_c()!==a?((u=gs())===a&&(u=Rs()),u===a&&(u=null),u!==a&&_c()!==a?(i=Ha,"nulls"===t.substr(Ha,5).toLowerCase()?(s=t.substr(Ha,5),Ha+=5):(s=a,0===Wa&&za(b)),s!==a&&(c=_c())!==a?("first"===t.substr(Ha,5).toLowerCase()?(l=t.substr(Ha,5),Ha+=5):(l=a,0===Wa&&za(v)),l===a&&("last"===t.substr(Ha,4).toLowerCase()?(l=t.substr(Ha,4),Ha+=4):(l=a,0===Wa&&za(h))),l!==a?i=s=[s,c,l]:(Ha=i,i=a)):(Ha=i,i=a),i===a&&(i=null),i!==a?(r,f=e,p=n,d=o,y=u,O=i,r=e={...f,collate:p,opclass:d,order_by:y&&y.toLowerCase(),nulls:O&&`${O[0].toLowerCase()} ${O[2].toLowerCase()}`}):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r}function su(){var r;return(r=lu())===a&&(r=hu())===a&&(r=du())===a&&(r=function(){var r;(r=function(){var r,e,n,o,u,i;r=Ha,(e=yu())===a&&(e=null);e!==a&&_c()!==a?("primary key"===t.substr(Ha,11).toLowerCase()?(n=t.substr(Ha,11),Ha+=11):(n=a,0===Wa&&za(k)),n!==a&&_c()!==a?((o=xu())===a&&(o=null),o!==a&&_c()!==a&&(u=Su())!==a&&_c()!==a?((i=ku())===a&&(i=null),i!==a?(r,c=n,l=o,f=u,p=i,e={constraint:(s=e)&&s.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:s&&s.keyword,index_type:l,resource:"constraint",index_options:p},r=e):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a);var s,c,l,f,p;return r}())===a&&(r=function(){var t,r,e,n,o,u,i,s;t=Ha,(r=yu())===a&&(r=null);r!==a&&_c()!==a&&(e=Oc())!==a&&_c()!==a?((n=dc())===a&&(n=yc()),n===a&&(n=null),n!==a&&_c()!==a?((o=Ai())===a&&(o=null),o!==a&&_c()!==a?((u=xu())===a&&(u=null),u!==a&&_c()!==a&&(i=Su())!==a&&_c()!==a?((s=ku())===a&&(s=null),s!==a?(t,l=e,f=n,p=o,b=u,v=i,h=s,r={constraint:(c=r)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:h},t=r):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a);var c,l,f,p,b,v,h;return t}())===a&&(r=function(){var r,e,n,o,u,i;r=Ha,(e=yu())===a&&(e=null);e!==a&&_c()!==a?("foreign key"===t.substr(Ha,11).toLowerCase()?(n=t.substr(Ha,11),Ha+=11):(n=a,0===Wa&&za(M)),n!==a&&_c()!==a?((o=Ai())===a&&(o=null),o!==a&&_c()!==a&&(u=Su())!==a&&_c()!==a?((i=Ou())===a&&(i=null),i!==a?(r,c=n,l=o,f=u,p=i,e={constraint:(s=e)&&s.constraint,definition:f,constraint_type:c,keyword:s&&s.keyword,index:l,resource:"constraint",reference_definition:p},r=e):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a);var s,c,l,f,p;return r}());return r}()),r}function cu(){var r,e,n,o;return r=Ha,(e=function(){var r,e;r=Ha,(e=function(){var r,e,n,o;r=Ha,"not null"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(Pe));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&(r,e={type:"not null",value:"not null"});return r=e}())===a&&(e=Yi()),e!==a&&(r,(o=e)&&!o.value&&(o.value="null"),e={nullable:o}),(r=e)===a&&(r=Ha,(e=function(){var t,r;t=Ha,ns()!==a&&_c()!==a?((r=Hi())===a&&(r=ii()),r!==a?(t,t={type:"default",value:r}):(Ha=t,t=a)):(Ha=t,t=a);return t}())!==a&&(r,e={default_val:e}),(r=e)===a&&(r=Ha,"auto_increment"===t.substr(Ha,14).toLowerCase()?(e=t.substr(Ha,14),Ha+=14):(e=a,0===Wa&&za(d)),e!==a&&(r,e={auto_increment:e.toLowerCase()}),(r=e)===a&&(r=Ha,"unique"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(y)),e!==a&&_c()!==a?("key"===t.substr(Ha,3).toLowerCase()?(n=t.substr(Ha,3),Ha+=3):(n=a,0===Wa&&za(O)),n===a&&(n=null),n!==a?(r,r=e=function(t){const r=["unique"];return t&&r.push(t),{unique:r.join(" ").toLowerCase("")}}(n)):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,"primary"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(m)),e===a&&(e=null),e!==a&&_c()!==a?("key"===t.substr(Ha,3).toLowerCase()?(n=t.substr(Ha,3),Ha+=3):(n=a,0===Wa&&za(O)),n!==a?(r,r=e=function(t){const r=[];return t&&r.push("primary"),r.push("key"),{primary_key:r.join(" ").toLowerCase("")}}(e)):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,(e=Mc())!==a&&(r,e={comment:e}),(r=e)===a&&(r=Ha,(e=fu())!==a&&(r,e={collate:e}),(r=e)===a&&(r=Ha,(e=function(){var r,e,n;r=Ha,"column_format"===t.substr(Ha,13).toLowerCase()?(e=t.substr(Ha,13),Ha+=13):(e=a,0===Wa&&za(E));e!==a&&_c()!==a?("fixed"===t.substr(Ha,5).toLowerCase()?(n=t.substr(Ha,5),Ha+=5):(n=a,0===Wa&&za(j)),n===a&&("dynamic"===t.substr(Ha,7).toLowerCase()?(n=t.substr(Ha,7),Ha+=7):(n=a,0===Wa&&za(w)),n===a&&("default"===t.substr(Ha,7).toLowerCase()?(n=t.substr(Ha,7),Ha+=7):(n=a,0===Wa&&za(L)))),n!==a?(r,e={type:"column_format",value:n.toLowerCase()},r=e):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&(r,e={column_format:e}),(r=e)===a&&(r=Ha,(e=function(){var r,e,n;r=Ha,"storage"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(C));e!==a&&_c()!==a?("disk"===t.substr(Ha,4).toLowerCase()?(n=t.substr(Ha,4),Ha+=4):(n=a,0===Wa&&za(T)),n===a&&("memory"===t.substr(Ha,6).toLowerCase()?(n=t.substr(Ha,6),Ha+=6):(n=a,0===Wa&&za(S))),n!==a?(r,e={type:"storage",value:n.toLowerCase()},r=e):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&(r,e={storage:e}),(r=e)===a&&(r=Ha,(e=Ou())!==a&&(r,e={reference_definition:e}),r=e))))))))),r}function lu(){var t,r,e,n,o,u,i;return t=Ha,(r=Li())!==a&&_c()!==a&&(e=Kc())!==a&&_c()!==a?((n=function(){var t,r,e,n,o,u;if(t=Ha,(r=cu())!==a)if(_c()!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=cu())!==a?n=o=[o,u]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=cu())!==a?n=o=[o,u]:(Ha=n,n=a);e!==a?(t,t=r=function(t,r){let e=t;for(let t=0;t<r.length;t++)e={...e,...r[t][1]};return e}(r,e)):(Ha=t,t=a)}else Ha=t,t=a;else Ha=t,t=a;return t}())===a&&(n=null),n!==a?(t,o=r,u=e,i=n,ll.add(`create::${o.table}::${o.column}`),t=r={column:o,definition:u,resource:"column",...i||{}}):(Ha=t,t=a)):(Ha=t,t=a),t}function fu(){var r,e;return r=Ha,function(){var r,e,n,o;r=Ha,"collate"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(ft));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="COLLATE"):(Ha=r,r=a)):(Ha=r,r=a);return r}()!==a&&_c()!==a&&(e=Ci())!==a?(r,r={type:"collate",value:e}):(Ha=r,r=a),r}function pu(){var t;return(t=function(){var t,r,e,n;t=Ha,(r=vc())!==a&&_c()!==a?((e=hc())===a&&(e=null),e!==a&&_c()!==a&&(n=lu())!==a?(t,o=e,u=n,r={action:"add",...u,keyword:o,resource:"column",type:"alter"},t=r):(Ha=t,t=a)):(Ha=t,t=a);var o,u;return t}())===a&&(t=function(){var t,r,e;t=Ha,as()!==a&&_c()!==a?((r=hc())===a&&(r=null),r!==a&&_c()!==a&&(e=Li())!==a?(t,t={action:"drop",column:e,keyword:r,resource:"column",type:"alter"}):(Ha=t,t=a)):(Ha=t,t=a);return t}())===a&&(t=function(){var t,r,e;t=Ha,(r=vc())!==a&&_c()!==a&&(e=hu())!==a?(t,n=e,r={action:"add",type:"alter",...n},t=r):(Ha=t,t=a);var n;return t}())===a&&(t=function(){var t,r,e;t=Ha,(r=vc())!==a&&_c()!==a&&(e=du())!==a?(t,n=e,r={action:"add",type:"alter",...n},t=r):(Ha=t,t=a);var n;return t}())===a&&(t=function(){var t,r,e,n;t=Ha,(r=ps())!==a&&_c()!==a?((e=os())===a&&(e=Os()),e===a&&(e=null),e!==a&&_c()!==a&&(n=Ci())!==a?(t,u=n,r={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:u},t=r):(Ha=t,t=a)):(Ha=t,t=a);var o,u;return t}())===a&&(t=bu())===a&&(t=vu()),t}function bu(){var r,e,n,o;return r=Ha,"algorithm"===t.substr(Ha,9).toLowerCase()?(e=t.substr(Ha,9),Ha+=9):(e=a,0===Wa&&za(A)),e!==a&&_c()!==a?((n=bc())===a&&(n=null),n!==a&&_c()!==a?("default"===t.substr(Ha,7).toLowerCase()?(o=t.substr(Ha,7),Ha+=7):(o=a,0===Wa&&za(L)),o===a&&("instant"===t.substr(Ha,7).toLowerCase()?(o=t.substr(Ha,7),Ha+=7):(o=a,0===Wa&&za(g)),o===a&&("inplace"===t.substr(Ha,7).toLowerCase()?(o=t.substr(Ha,7),Ha+=7):(o=a,0===Wa&&za(R)),o===a&&("copy"===t.substr(Ha,4).toLowerCase()?(o=t.substr(Ha,4),Ha+=4):(o=a,0===Wa&&za(I))))),o!==a?(r,r=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r}function vu(){var r,e,n,o;return r=Ha,"lock"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(N)),e!==a&&_c()!==a?((n=bc())===a&&(n=null),n!==a&&_c()!==a?("default"===t.substr(Ha,7).toLowerCase()?(o=t.substr(Ha,7),Ha+=7):(o=a,0===Wa&&za(L)),o===a&&("none"===t.substr(Ha,4).toLowerCase()?(o=t.substr(Ha,4),Ha+=4):(o=a,0===Wa&&za(U)),o===a&&("shared"===t.substr(Ha,6).toLowerCase()?(o=t.substr(Ha,6),Ha+=6):(o=a,0===Wa&&za(_)),o===a&&("exclusive"===t.substr(Ha,9).toLowerCase()?(o=t.substr(Ha,9),Ha+=9):(o=a,0===Wa&&za(x))))),o!==a?(r,r=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r}function hu(){var t,r,e,n,o,u,i,s;return t=Ha,(r=dc())===a&&(r=yc()),r!==a&&_c()!==a?((e=Ai())===a&&(e=null),e!==a&&_c()!==a?((n=xu())===a&&(n=null),n!==a&&_c()!==a&&(o=Su())!==a&&_c()!==a?((u=ku())===a&&(u=null),u!==a&&_c()!==a?(t,i=n,s=u,t=r={index:e,definition:o,keyword:r.toLowerCase(),index_type:i,resource:"index",index_options:s}):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a),t}function du(){var r,e,n,o,u,i,s,c,l;return r=Ha,(e=function(){var r,e,n,o;r=Ha,"fulltext"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(ca));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="FULLTEXT"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(e=function(){var r,e,n,o;r=Ha,"spatial"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(la));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SPATIAL"):(Ha=r,r=a)):(Ha=r,r=a);return r}()),e!==a&&_c()!==a?((n=dc())===a&&(n=yc()),n===a&&(n=null),n!==a&&_c()!==a?((o=Ai())===a&&(o=null),o!==a&&_c()!==a&&(u=Su())!==a&&_c()!==a?((i=ku())===a&&(i=null),i!==a&&_c()!==a?(r,s=e,l=i,r=e={index:o,definition:u,keyword:(c=n)&&`${s.toLowerCase()} ${c.toLowerCase()}`||s.toLowerCase(),index_options:l,resource:"index"}):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r}function yu(){var t,r,e,n;return t=Ha,(r=Ec())!==a&&_c()!==a?((e=Ci())===a&&(e=null),e!==a?(t,n=e,t=r={keyword:r.toLowerCase(),constraint:n}):(Ha=t,t=a)):(Ha=t,t=a),t}function Ou(){var r,e,n,o,u,i,s,c,l,f;return r=Ha,(e=function(){var r,e,n,o;r=Ha,"references"===t.substr(Ha,10).toLowerCase()?(e=t.substr(Ha,10),Ha+=10):(e=a,0===Wa&&za(va));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="REFERENCES"):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&_c()!==a&&(n=Vu())!==a&&_c()!==a&&(o=Su())!==a&&_c()!==a?("match full"===t.substr(Ha,10).toLowerCase()?(u=t.substr(Ha,10),Ha+=10):(u=a,0===Wa&&za(V)),u===a&&("match partial"===t.substr(Ha,13).toLowerCase()?(u=t.substr(Ha,13),Ha+=13):(u=a,0===Wa&&za(P)),u===a&&("match simple"===t.substr(Ha,12).toLowerCase()?(u=t.substr(Ha,12),Ha+=12):(u=a,0===Wa&&za(D)))),u===a&&(u=null),u!==a&&_c()!==a?((i=mu())===a&&(i=null),i!==a&&_c()!==a?((s=mu())===a&&(s=null),s!==a?(r,c=u,l=i,f=s,r=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(t=>t)}):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r}function mu(){var r,e,n,o;return r=Ha,Es()!==a&&_c()!==a?((e=cs())===a&&(e=us()),e!==a&&_c()!==a&&(n=function(){var r,e,n;r=Ha,(e=lc())!==a&&_c()!==a&&Cc()!==a&&_c()!==a?((n=ei())===a&&(n=null),n!==a&&_c()!==a&&Tc()!==a?(r,r=e={type:"function",name:e,args:n}):(Ha=r,r=a)):(Ha=r,r=a);r===a&&(r=Ha,"restrict"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(q)),e===a&&("cascade"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(B)),e===a&&("set null"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(G)),e===a&&("no action"===t.substr(Ha,9).toLowerCase()?(e=t.substr(Ha,9),Ha+=9):(e=a,0===Wa&&za(F)),e===a&&("set default"===t.substr(Ha,11).toLowerCase()?(e=t.substr(Ha,11),Ha+=11):(e=a,0===Wa&&za(H)),e===a&&(e=lc()))))),e!==a&&(r,e={type:"origin",value:e.toLowerCase()}),r=e);return r}())!==a?(r,o=n,r={type:"on "+e[0].toLowerCase(),value:o}):(Ha=r,r=a)):(Ha=r,r=a),r}function Eu(){var r,e,n,o,u,i,s;return r=Ha,(e=ls())===a&&(e=cs())===a&&(e=sc()),e!==a&&(r,s=e,e={keyword:Array.isArray(s)?s[0].toLowerCase():s.toLowerCase()}),(r=e)===a&&(r=Ha,(e=us())!==a&&_c()!==a?(n=Ha,"of"===t.substr(Ha,2).toLowerCase()?(o=t.substr(Ha,2),Ha+=2):(o=a,0===Wa&&za(z)),o!==a&&(u=_c())!==a&&(i=Yu())!==a?n=o=[o,u,i]:(Ha=n,n=a),n===a&&(n=null),n!==a?(r,r=e=function(t,r){return{keyword:t&&t[0]&&t[0].toLowerCase(),args:r&&{keyword:r[0],columns:r[2]}||null}}(e,n)):(Ha=r,r=a)):(Ha=r,r=a)),r}function ju(){var r,e,n,o,u,i,s,c,l;return r=Ha,(e=ns())===a&&(e=null),e!==a&&_c()!==a?((n=function(){var r,e,n;return r=Ha,"character"===t.substr(Ha,9).toLowerCase()?(e=t.substr(Ha,9),Ha+=9):(e=a,0===Wa&&za(st)),e!==a&&_c()!==a?("set"===t.substr(Ha,3).toLowerCase()?(n=t.substr(Ha,3),Ha+=3):(n=a,0===Wa&&za(ct)),n!==a?(r,r=e="CHARACTER SET"):(Ha=r,r=a)):(Ha=r,r=a),r}())===a&&("charset"===t.substr(Ha,7).toLowerCase()?(n=t.substr(Ha,7),Ha+=7):(n=a,0===Wa&&za(lt)),n===a&&("collate"===t.substr(Ha,7).toLowerCase()?(n=t.substr(Ha,7),Ha+=7):(n=a,0===Wa&&za(ft)))),n!==a&&_c()!==a?((o=bc())===a&&(o=null),o!==a&&_c()!==a&&(u=Ri())!==a?(r,s=n,c=o,l=u,r=e={keyword:(i=e)&&`${i[0].toLowerCase()} ${s.toLowerCase()}`||s.toLowerCase(),symbol:c,value:l}):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r}function wu(){var r,e,n,o,u,i,s,c,l;return r=Ha,"auto_increment"===t.substr(Ha,14).toLowerCase()?(e=t.substr(Ha,14),Ha+=14):(e=a,0===Wa&&za(d)),e===a&&("avg_row_length"===t.substr(Ha,14).toLowerCase()?(e=t.substr(Ha,14),Ha+=14):(e=a,0===Wa&&za(pt)),e===a&&("key_block_size"===t.substr(Ha,14).toLowerCase()?(e=t.substr(Ha,14),Ha+=14):(e=a,0===Wa&&za(bt)),e===a&&("max_rows"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(vt)),e===a&&("min_rows"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(ht)),e===a&&("stats_sample_pages"===t.substr(Ha,18).toLowerCase()?(e=t.substr(Ha,18),Ha+=18):(e=a,0===Wa&&za(dt))))))),e!==a&&_c()!==a?((n=bc())===a&&(n=null),n!==a&&_c()!==a&&(o=Zi())!==a?(r,c=n,l=o,r=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=ju())===a&&(r=Ha,(e=mc())===a&&("connection"===t.substr(Ha,10).toLowerCase()?(e=t.substr(Ha,10),Ha+=10):(e=a,0===Wa&&za(yt))),e!==a&&_c()!==a?((n=bc())===a&&(n=null),n!==a&&_c()!==a&&(o=Qi())!==a?(r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:`'${e.value}'`}}(e,n,o)):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,"compression"===t.substr(Ha,11).toLowerCase()?(e=t.substr(Ha,11),Ha+=11):(e=a,0===Wa&&za(Ot)),e!==a&&_c()!==a?((n=bc())===a&&(n=null),n!==a&&_c()!==a?(o=Ha,39===t.charCodeAt(Ha)?(u="'",Ha++):(u=a,0===Wa&&za(mt)),u!==a?("zlib"===t.substr(Ha,4).toLowerCase()?(i=t.substr(Ha,4),Ha+=4):(i=a,0===Wa&&za(Et)),i===a&&("lz4"===t.substr(Ha,3).toLowerCase()?(i=t.substr(Ha,3),Ha+=3):(i=a,0===Wa&&za(jt)),i===a&&("none"===t.substr(Ha,4).toLowerCase()?(i=t.substr(Ha,4),Ha+=4):(i=a,0===Wa&&za(U)))),i!==a?(39===t.charCodeAt(Ha)?(s="'",Ha++):(s=a,0===Wa&&za(mt)),s!==a?o=u=[u,i,s]:(Ha=o,o=a)):(Ha=o,o=a)):(Ha=o,o=a),o!==a?(r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.join("").toUpperCase()}}(e,n,o)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,"engine"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(wt)),e!==a&&_c()!==a?((n=bc())===a&&(n=null),n!==a&&_c()!==a&&(o=Ri())!==a?(r,r=e=function(t,r,e){return{keyword:t.toLowerCase(),symbol:r,value:e.toUpperCase()}}(e,n,o)):(Ha=r,r=a)):(Ha=r,r=a)))),r}function Lu(){var r,e,n,o,u,i,s;return(r=Au())===a&&(r=Ha,e=Ha,40===t.charCodeAt(Ha)?(n="(",Ha++):(n=a,0===Wa&&za(_t)),n!==a&&(o=_c())!==a&&(u=Lu())!==a&&(i=_c())!==a?(41===t.charCodeAt(Ha)?(s=")",Ha++):(s=a,0===Wa&&za(xt)),s!==a?e=n=[n,o,u,i,s]:(Ha=e,e=a)):(Ha=e,e=a),e!==a&&(r,e={...e[2],parentheses_symbol:!0}),r=e),r}function Cu(){var r,e,n,o,u,i,s,c,l;if(r=Ha,Ss()!==a)if(_c()!==a)if((e=Tu())!==a){for(n=[],o=Ha,(u=_c())!==a&&(i=wc())!==a&&(s=_c())!==a&&(c=Tu())!==a?o=u=[u,i,s,c]:(Ha=o,o=a);o!==a;)n.push(o),o=Ha,(u=_c())!==a&&(i=wc())!==a&&(s=_c())!==a&&(c=Tu())!==a?o=u=[u,i,s,c]:(Ha=o,o=a);n!==a?(r,r=nl(e,n)):(Ha=r,r=a)}else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;return r===a&&(r=Ha,_c()!==a&&Ss()!==a&&(e=_c())!==a&&(n=function(){var r,e,n,o;r=Ha,"RECURSIVE"===t.substr(Ha,9)?(e="RECURSIVE",Ha+=9):(e=a,0===Wa&&za(Ke));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&(o=_c())!==a&&(u=Tu())!==a?(r,(l=u).recursive=!0,r=[l]):(Ha=r,r=a)),r}function Tu(){var t,r,e,n,o;return t=Ha,(r=Qi())===a&&(r=Ri()),r!==a&&_c()!==a?((e=Su())===a&&(e=null),e!==a&&_c()!==a&&Os()!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(n=au())!==a&&_c()!==a&&Tc()!==a?(t,"string"==typeof(o=r)&&(o={type:"default",value:o}),t=r={name:o,stmt:n,columns:e}):(Ha=t,t=a)):(Ha=t,t=a),t}function Su(){var t,r;return t=Ha,Cc()!==a&&_c()!==a&&(r=Yu())!==a&&_c()!==a&&Tc()!==a?(t,t=r):(Ha=t,t=a),t}function Au(){var r,e,n,o,u,i,s,c,l,f,p,b,v,h,d,y,O,m,E,j,w;return r=Ha,_c()!==a?((e=Cu())===a&&(e=null),e!==a&&_c()!==a&&function(){var r,e,n,o;r=Ha,"select"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Ye));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}()!==a&&xc()!==a?((n=function(){var t,r,e,n,o,u;if(t=Ha,(r=gu())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=gu())!==a?n=o=[o,u]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=gu())!==a?n=o=[o,u]:(Ha=n,n=a);e!==a?(t,r=function(t,r){const e=[t];for(let t=0,n=r.length;t<n;++t)e.push(r[t][1]);return e}(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())===a&&(n=null),n!==a&&_c()!==a?((o=Ns())===a&&(o=null),o!==a&&_c()!==a&&(u=Ru())!==a&&_c()!==a?((i=Uu())===a&&(i=null),i!==a&&_c()!==a?((s=Hu())===a&&(s=null),s!==a&&_c()!==a?((c=function(){var r,e,n;r=Ha,(e=function(){var r,e,n,o;r=Ha,"group"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(Sn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&_c()!==a&&As()!==a&&_c()!==a&&(n=ei())!==a?(r,e=n.value,r=e):(Ha=r,r=a);return r}())===a&&(c=null),c!==a&&_c()!==a?((l=function(){var r,e;r=Ha,function(){var r,e,n,o;r=Ha,"having"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Rn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}()!==a&&_c()!==a&&(e=ci())!==a?(r,r=e):(Ha=r,r=a);return r}())===a&&(l=null),l!==a&&_c()!==a?((f=Qu())===a&&(f=null),f!==a&&_c()!==a?((p=Xu())===a&&(p=null),p!==a?(r,b=e,v=n,h=o,d=u,O=s,m=c,E=l,j=f,w=p,(y=i)&&y.forEach(t=>t.table&&cl.add(`select::${t.db}::${t.table}`)),r={with:b,type:"select",options:v,distinct:h,columns:d,from:y,where:O,groupby:m,having:E,orderby:j,limit:w}):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r}function gu(){var r,e;return r=Ha,(e=function(){var r;"sql_calc_found_rows"===t.substr(Ha,19).toLowerCase()?(r=t.substr(Ha,19),Ha+=19):(r=a,0===Wa&&za(ha));return r}())===a&&((e=function(){var r;"sql_cache"===t.substr(Ha,9).toLowerCase()?(r=t.substr(Ha,9),Ha+=9):(r=a,0===Wa&&za(da));return r}())===a&&(e=function(){var r;"sql_no_cache"===t.substr(Ha,12).toLowerCase()?(r=t.substr(Ha,12),Ha+=12):(r=a,0===Wa&&za(ya));return r}()),e===a&&(e=function(){var r;"sql_big_result"===t.substr(Ha,14).toLowerCase()?(r=t.substr(Ha,14),Ha+=14):(r=a,0===Wa&&za(ma));return r}())===a&&(e=function(){var r;"sql_small_result"===t.substr(Ha,16).toLowerCase()?(r=t.substr(Ha,16),Ha+=16):(r=a,0===Wa&&za(Oa));return r}())===a&&(e=function(){var r;"sql_buffer_result"===t.substr(Ha,17).toLowerCase()?(r=t.substr(Ha,17),Ha+=17):(r=a,0===Wa&&za(Ea));return r}())),e!==a&&(r,e=e),r=e}function Ru(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Is())===a&&(r=Ha,(e=Lc())!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Lc())),r!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Iu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Iu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,t=r=function(t,r){ll.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return r&&r.length>0?nl(e,r):[e]}(0,e)):(Ha=t,t=a)}else Ha=t,t=a;if(t===a)if(t=Ha,(r=Iu())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Iu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Iu())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,t=r=nl(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function Iu(){var t,r,e,n,o;return t=Ha,(r=si())!==a&&(e=pc())!==a&&(n=Kc())!==a?(t,t=r={type:"cast",expr:r,symbol:"::",target:n}):(Ha=t,t=a),t===a&&(t=Ha,r=Ha,(e=Ci())!==a&&(n=_c())!==a&&(o=jc())!==a?r=e=[e,n,o]:(Ha=r,r=a),r===a&&(r=null),r!==a&&(e=_c())!==a&&(n=Lc())!==a?(t,t=r=function(t){const r=t&&t[0]||null;return ll.add(`select::${r}::(.*)`),{expr:{type:"column_ref",table:r,column:"*"},as:null}}(r)):(Ha=t,t=a),t===a&&(t=Ha,(r=si())!==a&&(e=_c())!==a?((n=Nu())===a&&(n=null),n!==a?(t,t=r=function(t,r){return{type:"expr",expr:t,as:r}}(r,n)):(Ha=t,t=a)):(Ha=t,t=a))),t}function Nu(){var t,r,e;return t=Ha,(r=Os())!==a&&_c()!==a&&(e=function(){var t,r;t=Ha,(r=Ri())!==a?(Ha,(function(t){if(!0===zc[t.toUpperCase()])throw new Error("Error: "+JSON.stringify(t)+" is a reserved word, can not as alias clause");return!1}(r)?a:void 0)!==a?(t,t=r=r):(Ha=t,t=a)):(Ha=t,t=a);t===a&&(t=Ha,(r=Ti())!==a&&(t,r=r),t=r);return t}())!==a?(t,t=r=e):(Ha=t,t=a),t===a&&(t=Ha,(r=Os())===a&&(r=null),r!==a&&_c()!==a&&(e=Ci())!==a?(t,t=r=e):(Ha=t,t=a)),t}function Uu(){var t,r;return t=Ha,ds()!==a&&_c()!==a&&(r=Vu())!==a?(t,t=r):(Ha=t,t=a),t}function _u(){var t,r,e;return t=Ha,(r=Bu())!==a&&_c()!==a&&os()!==a&&_c()!==a&&(e=Bu())!==a?(t,t=r=[r,e]):(Ha=t,t=a),t}function xu(){var r,e;return r=Ha,Ts()!==a&&_c()!==a?("btree"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(kt)),e===a&&("hash"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Mt)),e===a&&("gist"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Vt)),e===a&&("gin"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Pt))))),e!==a?(r,r={keyword:"using",type:e.toLowerCase()}):(Ha=r,r=a)):(Ha=r,r=a),r}function ku(){var t,r,e,n,o,u;if(t=Ha,(r=Mu())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=Mu())!==a?n=o=[o,u]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=Mu())!==a?n=o=[o,u]:(Ha=n,n=a);e!==a?(t,t=r=function(t,r){const e=[t];for(let t=0;t<r.length;t++)e.push(r[t][1]);return e}(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function Mu(){var r,e,n,o,u,i;return r=Ha,(e=function(){var r,e,n,o;r=Ha,"key_block_size"===t.substr(Ha,14).toLowerCase()?(e=t.substr(Ha,14),Ha+=14):(e=a,0===Wa&&za(bt));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="KEY_BLOCK_SIZE"):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&_c()!==a?((n=bc())===a&&(n=null),n!==a&&_c()!==a&&(o=Zi())!==a?(r,u=n,i=o,r=e={type:e.toLowerCase(),symbol:u,expr:i}):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,(e=Ri())!==a&&_c()!==a&&(n=bc())!==a&&_c()!==a?((o=Zi())===a&&(o=Ci()),o!==a?(r,r=e=function(t,r,e){return{type:t.toLowerCase(),symbol:r,expr:"string"==typeof e&&{type:"origin",value:e}||e}}(e,n,o)):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=xu())===a&&(r=Ha,"with"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Dt)),e!==a&&_c()!==a?("parser"===t.substr(Ha,6).toLowerCase()?(n=t.substr(Ha,6),Ha+=6):(n=a,0===Wa&&za(qt)),n!==a&&_c()!==a&&(o=Ri())!==a?(r,r=e={type:"with parser",expr:o}):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,"visible"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Bt)),e===a&&("invisible"===t.substr(Ha,9).toLowerCase()?(e=t.substr(Ha,9),Ha+=9):(e=a,0===Wa&&za(Gt))),e!==a&&(r,e=function(t){return{type:t.toLowerCase(),expr:t.toLowerCase()}}(e)),(r=e)===a&&(r=Mc())))),r}function Vu(){var t,r,e,n;if(t=Ha,(r=Du())!==a){for(e=[],n=Pu();n!==a;)e.push(n),n=Pu();e!==a?(t,t=r=Ft(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function Pu(){var t,r,e;return t=Ha,_c()!==a&&(r=wc())!==a&&_c()!==a&&(e=Du())!==a?(t,t=e):(Ha=t,t=a),t===a&&(t=Ha,_c()!==a&&(r=function(){var t,r,e,n,o,u,i,s,c,l,f;if(t=Ha,(r=qu())!==a)if(_c()!==a)if((e=Du())!==a)if(_c()!==a)if((n=Ts())!==a)if(_c()!==a)if(Cc()!==a)if(_c()!==a)if((o=Ri())!==a){for(u=[],i=Ha,(s=_c())!==a&&(c=wc())!==a&&(l=_c())!==a&&(f=Ri())!==a?i=s=[s,c,l,f]:(Ha=i,i=a);i!==a;)u.push(i),i=Ha,(s=_c())!==a&&(c=wc())!==a&&(l=_c())!==a&&(f=Ri())!==a?i=s=[s,c,l,f]:(Ha=i,i=a);u!==a&&(i=_c())!==a&&(s=Tc())!==a?(t,p=r,v=o,h=u,(b=e).join=p,b.using=nl(v,h),t=r=b):(Ha=t,t=a)}else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;var p,b,v,h;t===a&&(t=Ha,(r=qu())!==a&&_c()!==a&&(e=Du())!==a&&_c()!==a?((n=Fu())===a&&(n=null),n!==a?(t,r=function(t,r,e){return r.join=t,r.on=e,r}(r,e,n),t=r):(Ha=t,t=a)):(Ha=t,t=a),t===a&&(t=Ha,(r=qu())!==a&&_c()!==a&&(e=Cc())!==a&&_c()!==a&&(n=au())!==a&&_c()!==a&&Tc()!==a&&_c()!==a?((o=Nu())===a&&(o=null),o!==a&&(u=_c())!==a?((i=Fu())===a&&(i=null),i!==a?(t,r=function(t,r,e,n){return r.parentheses=!0,{expr:r,as:e,join:t,on:n}}(r,n,o,i),t=r):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a)));return t}())!==a?(t,t=r):(Ha=t,t=a)),t}function Du(){var r,e,n,o,u,i,s,c,l,f,p;return r=Ha,(e=function(){var r;"dual"===t.substr(Ha,4).toLowerCase()?(r=t.substr(Ha,4),Ha+=4):(r=a,0===Wa&&za(aa));return r}())!==a&&(r,e={type:"dual"}),(r=e)===a&&(r=Ha,(e=Bu())!==a&&_c()!==a?((n=Nu())===a&&(n=null),n!==a?(r,p=n,r=e="var"===(f=e).type?(f.as=p,f):{db:f.db,table:f.table,as:p}):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,(e=Cc())!==a&&_c()!==a&&(n=au())!==a&&_c()!==a&&Tc()!==a&&_c()!==a?((o=Nu())===a&&(o=null),o!==a?(r,r=e=function(t,r){return t.parentheses=!0,{expr:t,as:r}}(n,o)):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,(e=ms())!==a&&_c()!==a&&(n=Cc())!==a&&_c()!==a&&function(){var r,e,n,o;r=Ha,"tumble"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(zn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="TUMBLE"):(Ha=r,r=a)):(Ha=r,r=a);return r}()!==a&&_c()!==a&&(o=Cc())!==a&&_c()!==a&&ms()!==a&&_c()!==a&&(u=Bu())!==a&&_c()!==a&&wc()!==a&&_c()!==a?("descriptor"===t.substr(Ha,10).toLowerCase()?(i=t.substr(Ha,10),Ha+=10):(i=a,0===Wa&&za(Ht)),i!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(s=Li())!==a&&_c()!==a&&Tc()!==a&&_c()!==a&&wc()!==a&&_c()!==a&&(c=ni())!==a&&_c()!==a&&Tc()!==a&&_c()!==a&&Tc()!==a&&_c()!==a?((l=Nu())===a&&(l=null),l!==a?(r,r=e=function(t,r,e,n){return{expr:{type:"tumble",data:t,timecol:r,size:e},as:n}}(u,s,c,l)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)))),r}function qu(){var r,e,n,o,u,i;return r=Ha,(e=function(){var r,e,n,o;r=Ha,"natural"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(fn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="NATURAL"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(e=null),e!==a&&(n=_c())!==a?((o=function(){var r,e,n,o;r=Ha,"left"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(pn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="LEFT"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(o=function(){var r,e,n,o;r=Ha,"right"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(bn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="RIGHT"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(o=function(){var r,e,n,o;r=Ha,"full"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(vn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="FULL"):(Ha=r,r=a)):(Ha=r,r=a);return r}()),o===a&&(o=null),o!==a&&_c()!==a?((u=Ls())===a&&(u=null),u!==a&&_c()!==a&&js()!==a?(r,r=e=`${e?"NATURAL ":""}${(i=o)?i+" ":""}${u?"OUTER ":""}JOIN`):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,e=Ha,(n=function(){var r,e,n,o;r=Ha,"inner"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(hn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="INNER"):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&(o=_c())!==a?e=n=[n,o]:(Ha=e,e=a),e===a&&(e=null),e!==a&&(n=js())!==a?(r,r=e=e?"INNER JOIN":"JOIN"):(Ha=r,r=a),r===a&&(r=Ha,(e=ws())!==a&&(n=_c())!==a&&(o=js())!==a?(r,r=e="CROSS JOIN"):(Ha=r,r=a),r===a&&(r=Ha,(e=ws())===a&&(e=Ls()),e!==a&&(n=_c())!==a&&(o=function(){var r,e,n,o;r=Ha,"apply"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(On));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a?(r,r=e=e[0].toUpperCase()+" APPLY"):(Ha=r,r=a)))),r}function Bu(){var t,r,e,n,o,u,i,s,c,l;return t=Ha,(r=Ci())!==a?(e=Ha,(n=_c())!==a&&(o=jc())!==a&&(u=_c())!==a&&(i=Ci())!==a?e=n=[n,o,u,i]:(Ha=e,e=a),e!==a?(n=Ha,(o=_c())!==a&&(u=jc())!==a&&(i=_c())!==a&&(s=Ci())!==a?n=o=[o,u,i,s]:(Ha=n,n=a),n!==a?(t,t=r=function(t,r,e){const n={db:null,table:t};return null!==e&&(n.db=`${t}.${r[3]}`,n.table=e[3]),n}(r,e,n)):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a),t===a&&(t=Ha,(r=Ci())!==a&&(e=_c())!==a&&(n=jc())!==a&&(o=_c())!==a&&(u=Lc())!==a?(t,l=r,cl.add(`select::${l}::(.*)`),t=r={db:l,table:"*"}):(Ha=t,t=a),t===a&&(t=Ha,(r=Ci())!==a?(e=Ha,(n=_c())!==a&&(o=jc())!==a&&(u=_c())!==a&&(i=Ci())!==a?e=n=[n,o,u,i]:(Ha=e,e=a),e===a&&(e=null),e!==a?(t,t=r=function(t,r){const e={db:null,table:t};return null!==r&&(e.db=t,e.table=r[3]),e}(r,e)):(Ha=t,t=a)):(Ha=t,t=a),t===a&&(t=Ha,(r=Xc())!==a&&(t,(c=r).db=null,c.table=c.name,r=c),t=r))),t}function Gu(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=ii())!==a){for(e=[],n=Ha,(o=_c())!==a?((u=Ds())===a&&(u=qs()),u!==a&&(i=_c())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(Ha=n,n=a)):(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a?((u=Ds())===a&&(u=qs()),u!==a&&(i=_c())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(Ha=n,n=a)):(Ha=n,n=a);e!==a?(t,t=r=function(t,r){const e=r.length;let n=t;for(let t=0;t<e;++t)n=rl(r[t][1],n,r[t][3]);return n}(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function Fu(){var t,r;return t=Ha,Es()!==a&&_c()!==a&&(r=ci())!==a?(t,t=r):(Ha=t,t=a),t}function Hu(){var r,e;return r=Ha,function(){var r,e,n,o;r=Ha,"where"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(Tn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}()!==a&&_c()!==a?((e=ci())===a&&(e=ii()),e!==a?(r,r=e):(Ha=r,r=a)):(Ha=r,r=a),r}function Yu(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Li())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Li())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Li())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,t=r=nl(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function Qu(){var r,e;return r=Ha,function(){var r,e,n,o;r=Ha,"order"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(gn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}()!==a&&_c()!==a&&As()!==a&&_c()!==a&&(e=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=$u())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=$u())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=$u())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=nl(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())!==a?(r,r=e):(Ha=r,r=a),r}function $u(){var t,r,e;return t=Ha,(r=ii())!==a&&_c()!==a?((e=Rs())===a&&(e=gs()),e===a&&(e=null),e!==a?(t,t=r={expr:r,type:e}):(Ha=t,t=a)):(Ha=t,t=a),t}function Wu(){var t;return(t=Zi())===a&&(t=_i()),t}function Xu(){var r,e,n,o,u,i;return r=Ha,function(){var r,e,n,o;r=Ha,"limit"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(In));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}()!==a&&_c()!==a?((e=Wu())===a&&(e=Is()),e!==a&&_c()!==a?(n=Ha,(o=function(){var r,e,n,o;r=Ha,"offset"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Nn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="OFFSET"):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&(u=_c())!==a&&(i=Wu())!==a?n=o=[o,u,i]:(Ha=n,n=a),n===a&&(n=null),n!==a?(r,r=function(t,r){const e=[];return"string"==typeof t?e.push({type:"origin",value:"all"}):e.push(t),r&&e.push(r[2]),{seperator:r&&r[0]&&r[0].toLowerCase()||"",value:e}}(e,n)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r}function Zu(){var r,e,n,o,u,i,s,c,l;return r=Ha,e=Ha,(n=Ci())!==a&&(o=_c())!==a&&(u=jc())!==a?e=n=[n,o,u]:(Ha=e,e=a),e===a&&(e=null),e!==a&&(n=_c())!==a&&(o=Si())!==a&&(u=_c())!==a?(61===t.charCodeAt(Ha)?(i="=",Ha++):(i=a,0===Wa&&za(Yt)),i!==a&&_c()!==a&&(s=Oi())!==a?(r,r=e={column:o,value:s,table:(l=e)&&l[0]}):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,e=Ha,(n=Ci())!==a&&(o=_c())!==a&&(u=jc())!==a?e=n=[n,o,u]:(Ha=e,e=a),e===a&&(e=null),e!==a&&(n=_c())!==a&&(o=Si())!==a&&(u=_c())!==a?(61===t.charCodeAt(Ha)?(i="=",Ha++):(i=a,0===Wa&&za(Yt)),i!==a&&_c()!==a&&(s=Cs())!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(c=Li())!==a&&_c()!==a&&Tc()!==a?(r,r=e=function(t,r,e){return{column:r,value:e,table:t&&t[0],keyword:"values"}}(e,o,c)):(Ha=r,r=a)):(Ha=r,r=a)),r}function Ku(){var r,e,n,o,u;return r=Ha,(e=function(){var r,e,n,o;r=Ha,"returning"===t.substr(Ha,9).toLowerCase()?(e=t.substr(Ha,9),Ha+=9):(e=a,0===Wa&&za(ze));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="RETURNING"):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&_c()!==a?((n=Lc())===a&&(n=Yu()),n!==a?(r,u=n,r=e={type:(o=e)&&o.toLowerCase()||"returning",columns:"*"===u&&[{type:"columne_ref",table:null,column:"*"}]||u}):(Ha=r,r=a)):(Ha=r,r=a),r}function Ju(){var t;return(t=function(){var t,r;t=Ha,Cs()!==a&&_c()!==a&&(r=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=ri())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=ri())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=ri())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=nl(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())!==a?(t,t=r):(Ha=t,t=a);return t}())===a&&(t=Au()),t}function zu(){var t,r,e,n,o,u,i,s,c;if(t=Ha,vs()!==a)if(_c()!==a)if((r=Cc())!==a)if(_c()!==a)if((e=Ri())!==a){for(n=[],o=Ha,(u=_c())!==a&&(i=wc())!==a&&(s=_c())!==a&&(c=Ri())!==a?o=u=[u,i,s,c]:(Ha=o,o=a);o!==a;)n.push(o),o=Ha,(u=_c())!==a&&(i=wc())!==a&&(s=_c())!==a&&(c=Ri())!==a?o=u=[u,i,s,c]:(Ha=o,o=a);n!==a&&(o=_c())!==a&&(u=Tc())!==a?(t,t=nl(e,n)):(Ha=t,t=a)}else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;return t===a&&(t=Ha,vs()!==a&&_c()!==a&&(r=ri())!==a?(t,t=r):(Ha=t,t=a)),t}function ti(){var t,r;return t=Ha,(r=ls())!==a&&(t,r="insert"),(t=r)===a&&(t=Ha,(r=fs())!==a&&(t,r="replace"),t=r),t}function ri(){var t,r;return t=Ha,Cc()!==a&&_c()!==a&&(r=ei())!==a&&_c()!==a&&Tc()!==a?(t,t=r):(Ha=t,t=a),t}function ei(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=ii())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,t=r=function(t,r){const e={type:"expr_list"};return e.value=nl(t,r),e}(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function ni(){var r,e,n;return r=Ha,cc()!==a&&_c()!==a&&(e=ii())!==a&&_c()!==a&&(n=function(){var r,e;(r=function(){var r,e,n,o;r=Ha,"year"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(ie));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="YEAR"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"month"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(te));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="MONTH"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"day"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Br));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="DAY"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"hour"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za($r));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="HOUR"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"minute"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(zr));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="MINUTE"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"second"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(ee));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SECOND"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=Ha,"years"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(ka)),e===a&&("months"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Ma)),e===a&&("days"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Va)),e===a&&("hours"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(Pa)),e===a&&("minutes"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Da)),e===a&&("seconds"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(qa))))))),e!==a&&(r,e=e.toUpperCase()),r=e);return r}())!==a?(r,r={type:"interval",expr:e,unit:n.toLowerCase()}):(Ha=r,r=a),r===a&&(r=Ha,cc()!==a&&_c()!==a&&(e=Qi())!==a?(r,r=function(t){return{type:"interval",expr:t,unit:""}}(e)):(Ha=r,r=a)),r}function oi(){var r,e,n,o,u,i,s,c;return r=Ha,Gs()!==a&&_c()!==a?((e=ii())===a&&(e=null),e!==a&&_c()!==a&&(n=function(){var t,r,e,n,o,u;if(t=Ha,(r=ai())!==a)if(_c()!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=ai())!==a?n=o=[o,u]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=ai())!==a?n=o=[o,u]:(Ha=n,n=a);e!==a?(t,r=p(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;else Ha=t,t=a;return t}())!==a&&_c()!==a?((o=function(){var r,e;r=Ha,function(){var r,e,n,o;r=Ha,"else"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(ao));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}()!==a&&_c()!==a&&(e=ii())!==a?(r,r={type:"else",result:e}):(Ha=r,r=a);return r}())===a&&(o=null),o!==a&&_c()!==a&&function(){var r,e,n,o;r=Ha,"end"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(uo));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}()!==a&&_c()!==a?((u=Gs())===a&&(u=null),u!==a?(r,i=e,s=n,(c=o)&&s.push(c),r={type:"case",expr:i||null,args:s}):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r}function ai(){var r,e,n;return r=Ha,Fs()!==a&&_c()!==a&&(e=ci())!==a&&_c()!==a&&function(){var r,e,n,o;r=Ha,"then"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(oo));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}()!==a&&_c()!==a&&(n=ii())!==a?(r,r={type:"when",cond:e,result:n}):(Ha=r,r=a),r}function ui(){var t;return(t=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=wi())!==a){if(e=[],n=Ha,(o=_c())!==a&&(u=Uc())!==a&&(i=_c())!==a&&(s=wi())!==a?n=o=[o,u,i,s]:(Ha=n,n=a),n!==a)for(;n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=Uc())!==a&&(i=_c())!==a&&(s=wi())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);else e=a;e!==a&&(n=_c())!==a?((o=vi())===a&&(o=null),o!==a?(t,r=function(t,r,e){const n=ol(t,r);return null===e?n:"arithmetic"===e.type?ol(n,e.tail):rl(e.op,n,e.right)}(r,e,o),t=r):(Ha=t,t=a)):(Ha=t,t=a)}else Ha=t,t=a;return t}())===a&&(t=function(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=li())!==a){for(e=[],n=Ha,(o=xc())!==a&&(u=qs())!==a&&(i=_c())!==a&&(s=li())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=xc())!==a&&(u=qs())!==a&&(i=_c())!==a&&(s=li())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,r=Qt(r,e),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}())===a&&(t=function(){var t,r,e,n,o,u;if(t=Ha,(r=mi())!==a){if(e=[],n=Ha,(o=_c())!==a&&(u=wi())!==a?n=o=[o,u]:(Ha=n,n=a),n!==a)for(;n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wi())!==a?n=o=[o,u]:(Ha=n,n=a);else e=a;e!==a?(t,r=tl(r,e[0][1]),t=r):(Ha=t,t=a)}else Ha=t,t=a;return t}()),t}function ii(){var t;return(t=ui())===a&&(t=au()),t}function si(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=ii())!==a){for(e=[],n=Ha,(o=_c())!==a?((u=Ds())===a&&(u=qs())===a&&(u=Uc()),u!==a&&(i=_c())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(Ha=n,n=a)):(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a?((u=Ds())===a&&(u=qs())===a&&(u=Uc()),u!==a&&(i=_c())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(Ha=n,n=a)):(Ha=n,n=a);e!==a?(t,t=r=function(t,r){const e=t.ast;if(e&&"select"===e.type&&(!(t.parentheses_symbol||t.parentheses||t.ast.parentheses||t.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!r||0===r.length)return t;const n=r.length;let o=r[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?t:r[e-1][3];o=rl(r[e][1],n,o)}return o}(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function ci(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=ii())!==a){for(e=[],n=Ha,(o=_c())!==a?((u=Ds())===a&&(u=qs())===a&&(u=wc()),u!==a&&(i=_c())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(Ha=n,n=a)):(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a?((u=Ds())===a&&(u=qs())===a&&(u=wc()),u!==a&&(i=_c())!==a&&(s=ii())!==a?n=o=[o,u,i,s]:(Ha=n,n=a)):(Ha=n,n=a);e!==a?(t,t=r=function(t,r){const e=r.length;let n=t,o="";for(let t=0;t<e;++t)","===r[t][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(r[t][3])):n=rl(r[t][1],n,r[t][3]);if(","===o){const t={type:"expr_list"};return t.value=n,t}return n}(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function li(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=fi())!==a){for(e=[],n=Ha,(o=xc())!==a&&(u=Ds())!==a&&(i=_c())!==a&&(s=fi())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=xc())!==a&&(u=Ds())!==a&&(i=_c())!==a&&(s=fi())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,t=r=Qt(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function fi(){var r,e,n,o,u;return(r=pi())===a&&(r=function(){var t,r,e;t=Ha,(r=bi())!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(e=au())!==a&&_c()!==a&&Tc()!==a?(t,n=r,(o=e).parentheses=!0,r=tl(n,o),t=r):(Ha=t,t=a);var n,o;return t}())===a&&(r=Ha,(e=Ps())===a&&(e=Ha,33===t.charCodeAt(Ha)?(n="!",Ha++):(n=a,0===Wa&&za($t)),n!==a?(o=Ha,Wa++,61===t.charCodeAt(Ha)?(u="=",Ha++):(u=a,0===Wa&&za(Yt)),Wa--,u===a?o=void 0:(Ha=o,o=a),o!==a?e=n=[n,o]:(Ha=e,e=a)):(Ha=e,e=a)),e!==a&&(n=_c())!==a&&(o=fi())!==a?(r,r=e=tl("NOT",o)):(Ha=r,r=a)),r}function pi(){var t,r,e,n,o;return t=Ha,(r=Oi())!==a&&_c()!==a?((e=vi())===a&&(e=null),e!==a?(t,n=r,t=r=null===(o=e)?n:"arithmetic"===o.type?ol(n,o.tail):rl(o.op,n,o.right)):(Ha=t,t=a)):(Ha=t,t=a),t===a&&(t=Qi())===a&&(t=Li()),t}function bi(){var t,r,e,n,o,u;return t=Ha,r=Ha,(e=Ps())!==a&&(n=_c())!==a&&(o=Vs())!==a?r=e=[e,n,o]:(Ha=r,r=a),r!==a&&(t,r=(u=r)[0]+" "+u[2]),(t=r)===a&&(t=Vs()),t}function vi(){var r;return(r=function(){var t,r,e,n,o,u,i;t=Ha,r=[],e=Ha,(n=_c())!==a&&(o=hi())!==a&&(u=_c())!==a&&(i=Oi())!==a?e=n=[n,o,u,i]:(Ha=e,e=a);if(e!==a)for(;e!==a;)r.push(e),e=Ha,(n=_c())!==a&&(o=hi())!==a&&(u=_c())!==a&&(i=Oi())!==a?e=n=[n,o,u,i]:(Ha=e,e=a);else r=a;r!==a&&(t,r={type:"arithmetic",tail:r});return t=r}())===a&&(r=function(){var t,r,e,n;t=Ha,(r=yi())!==a&&_c()!==a&&(e=Cc())!==a&&_c()!==a&&(n=ei())!==a&&_c()!==a&&Tc()!==a?(t,t=r={op:r,right:n}):(Ha=t,t=a);t===a&&(t=Ha,(r=yi())!==a&&_c()!==a?((e=Xc())===a&&(e=Qi()),e!==a?(t,r=function(t,r){return{op:t,right:r}}(r,e),t=r):(Ha=t,t=a)):(Ha=t,t=a));return t}())===a&&(r=function(){var t,r,e;t=Ha,(r=bi())!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(e=ei())!==a&&_c()!==a&&Tc()!==a?(t,t=r={op:r,right:e}):(Ha=t,t=a);return t}())===a&&(r=function(){var t,r,e,n;t=Ha,(r=function(){var t,r,e,n,o;t=Ha,r=Ha,(e=Ps())!==a&&(n=_c())!==a&&(o=Us())!==a?r=e=[e,n,o]:(Ha=r,r=a);r!==a&&(t,r=(u=r)[0]+" "+u[2]);var u;(t=r)===a&&(t=Us());return t}())!==a&&_c()!==a&&(e=Oi())!==a&&_c()!==a&&Ds()!==a&&_c()!==a&&(n=Oi())!==a?(t,t=r={op:r,right:{type:"expr_list",value:[e,n]}}):(Ha=t,t=a);return t}())===a&&(r=function(){var t,r,e;t=Ha,(r=function(){var t;t=Ha,xs()!==a&&_c()!==a&&Ps()!==a&&_c()!==a&&Ns()!==a&&_c()!==a&&ds()!==a?(t,t="IS NOT DISTINCT FROM"):(Ha=t,t=a);t===a&&(t=Ha,xs()!==a&&_c()!==a&&Ns()!==a&&_c()!==a&&ds()!==a?(t,t="IS DISTINCT FROM"):(Ha=t,t=a));return t}())!==a&&_c()!==a&&(e=ii())!==a?(t,t=r={op:r,right:e}):(Ha=t,t=a);return t}())===a&&(r=function(){var t,r,e,n,o,u,i,s,c;t=Ha,(r=xs())!==a&&(e=_c())!==a&&(n=Oi())!==a?(t,t=r={op:"IS",right:n}):(Ha=t,t=a);t===a&&(t=Ha,(r=xs())!==a&&(e=_c())!==a?(n=Ha,(o=Ns())!==a&&(u=_c())!==a&&(i=ds())!==a&&(s=_c())!==a&&(c=Bu())!==a?n=o=[o,u,i,s,c]:(Ha=n,n=a),n!==a?(t,r=function(t){const{db:r,table:e}=t.pop(),n="*"===e?"*":`"${e}"`;return{op:"IS",right:{type:"origin",value:"DISTINCT FROM "+(r?`"${r}".${n}`:n)}}}(n),t=r):(Ha=t,t=a)):(Ha=t,t=a),t===a&&(t=Ha,r=Ha,(e=xs())!==a&&(n=_c())!==a&&(o=Ps())!==a?r=e=[e,n,o]:(Ha=r,r=a),r!==a&&(e=_c())!==a&&(n=Oi())!==a?(t,r=function(t){return{op:"IS NOT",right:t}}(n),t=r):(Ha=t,t=a)));return t}())===a&&(r=function(){var t,r,e,n;t=Ha,(r=function(){var t,r,e,n,o;t=Ha,r=Ha,(e=Ps())!==a&&(n=_c())!==a&&(o=ks())!==a?r=e=[e,n,o]:(Ha=r,r=a);r!==a&&(t,r=(u=r)[0]+" "+u[2]);var u;(t=r)===a&&(t=ks());return t}())!==a&&_c()!==a?((e=Hi())===a&&(e=pi()),e!==a&&_c()!==a?((n=di())===a&&(n=null),n!==a?(t,o=r,u=e,(i=n)&&(u.escape=i),t=r={op:o,right:u}):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a);var o,u,i;return t}())===a&&(r=function(){var t,r,e,n;t=Ha,(r=function(){var t,r,e,n,o,u,i;t=Ha,r=Ha,(e=Ps())!==a&&(n=_c())!==a&&(o=Ms())!==a&&(u=_c())!==a&&(i=os())!==a?r=e=[e,n,o,u,i]:(Ha=r,r=a);r!==a&&(t,r="NOT SIMILAR TO");(t=r)===a&&(t=Ha,(r=Ms())!==a&&(e=_c())!==a&&(n=os())!==a?(t,t=r="SIMILAR TO"):(Ha=t,t=a));return t}())!==a&&_c()!==a?((e=Hi())===a&&(e=pi()),e!==a&&_c()!==a?((n=di())===a&&(n=null),n!==a?(t,o=r,u=e,(i=n)&&(u.escape=i),t=r={op:o,right:u}):(Ha=t,t=a)):(Ha=t,t=a)):(Ha=t,t=a);var o,u,i;return t}())===a&&(r=function(){var r,e,n;r=Ha,"@>"===t.substr(Ha,2)?(e="@>",Ha+=2):(e=a,0===Wa&&za(rr));e===a&&("<@"===t.substr(Ha,2)?(e="<@",Ha+=2):(e=a,0===Wa&&za(er)),e===a&&(e=Nc())===a&&(e=function(){var r;"#>>"===t.substr(Ha,3)?(r="#>>",Ha+=3):(r=a,0===Wa&&za(ga));return r}())===a&&(e=function(){var r;"#>"===t.substr(Ha,2)?(r="#>",Ha+=2):(r=a,0===Wa&&za(Aa));return r}())===a&&(63===t.charCodeAt(Ha)?(e="?",Ha++):(e=a,0===Wa&&za(nr)),e===a&&("?|"===t.substr(Ha,2)?(e="?|",Ha+=2):(e=a,0===Wa&&za(or)),e===a&&("?&"===t.substr(Ha,2)?(e="?&",Ha+=2):(e=a,0===Wa&&za(ar)),e===a&&("#-"===t.substr(Ha,2)?(e="#-",Ha+=2):(e=a,0===Wa&&za(ur)))))));e!==a&&_c()!==a&&(n=Iu())!==a?(r,e={op:e,right:(o=n)&&o.expr||o},r=e):(Ha=r,r=a);var o;return r}()),r}function hi(){var r;return">="===t.substr(Ha,2)?(r=">=",Ha+=2):(r=a,0===Wa&&za(Wt)),r===a&&(62===t.charCodeAt(Ha)?(r=">",Ha++):(r=a,0===Wa&&za(Xt)),r===a&&("<="===t.substr(Ha,2)?(r="<=",Ha+=2):(r=a,0===Wa&&za(Zt)),r===a&&("<>"===t.substr(Ha,2)?(r="<>",Ha+=2):(r=a,0===Wa&&za(Kt)),r===a&&(60===t.charCodeAt(Ha)?(r="<",Ha++):(r=a,0===Wa&&za(Jt)),r===a&&(61===t.charCodeAt(Ha)?(r="=",Ha++):(r=a,0===Wa&&za(Yt)),r===a&&("!="===t.substr(Ha,2)?(r="!=",Ha+=2):(r=a,0===Wa&&za(zt)))))))),r}function di(){var r,e,n;return r=Ha,"escape"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(tr)),e!==a&&_c()!==a&&(n=Qi())!==a?(r,r=e={type:"ESCAPE",value:n}):(Ha=r,r=a),r}function yi(){var t,r,e,n,o,u;return t=Ha,r=Ha,(e=Ps())!==a&&(n=_c())!==a&&(o=_s())!==a?r=e=[e,n,o]:(Ha=r,r=a),r!==a&&(t,r=(u=r)[0]+" "+u[2]),(t=r)===a&&(t=_s()),t}function Oi(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Ei())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=mi())!==a&&(i=_c())!==a&&(s=Ei())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=mi())!==a&&(i=_c())!==a&&(s=Ei())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,t=r=Qt(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function mi(){var r;return 43===t.charCodeAt(Ha)?(r="+",Ha++):(r=a,0===Wa&&za(ir)),r===a&&(45===t.charCodeAt(Ha)?(r="-",Ha++):(r=a,0===Wa&&za(sr))),r}function Ei(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=wi())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=ji())!==a&&(i=_c())!==a&&(s=wi())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=ji())!==a&&(i=_c())!==a&&(s=wi())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,t=r=ol(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function ji(){var r;return 42===t.charCodeAt(Ha)?(r="*",Ha++):(r=a,0===Wa&&za(cr)),r===a&&(47===t.charCodeAt(Ha)?(r="/",Ha++):(r=a,0===Wa&&za(lr)),r===a&&(37===t.charCodeAt(Ha)?(r="%",Ha++):(r=a,0===Wa&&za(fr)))),r}function wi(){var r,e,n,o;return(r=function(){var r,e,n,o,u,i,s,c;r=Ha,(e=Hi())===a&&(e=xi())===a&&(e=Bi())===a&&(e=oi())===a&&(e=ni())===a&&(e=Li())===a&&(e=_i());e!==a&&pc()!==a&&(n=Kc())!==a?(r,r=e={type:"cast",keyword:"cast",expr:e,symbol:"::",target:n}):(Ha=r,r=a);r===a&&(r=Ha,(e=Hs())===a&&(e=Ys()),e!==a&&_c()!==a&&(n=Cc())!==a&&_c()!==a&&(o=ii())!==a&&_c()!==a&&Os()!==a&&_c()!==a&&(u=Kc())!==a&&_c()!==a&&(i=Tc())!==a?(r,e=function(t,r,e){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:e}}(e,o,u),r=e):(Ha=r,r=a),r===a&&(r=Ha,(e=Hs())===a&&(e=Ys()),e!==a&&_c()!==a&&(n=Cc())!==a&&_c()!==a&&(o=ii())!==a&&_c()!==a&&Os()!==a&&_c()!==a&&(u=Xs())!==a&&_c()!==a&&(i=Cc())!==a&&_c()!==a&&(s=Ki())!==a&&_c()!==a&&Tc()!==a&&_c()!==a&&(c=Tc())!==a?(r,e=function(t,r,e){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:{dataType:"DECIMAL("+e+")"}}}(e,o,s),r=e):(Ha=r,r=a),r===a&&(r=Ha,(e=Hs())===a&&(e=Ys()),e!==a&&_c()!==a&&(n=Cc())!==a&&_c()!==a&&(o=ii())!==a&&_c()!==a&&Os()!==a&&_c()!==a&&(u=Xs())!==a&&_c()!==a&&(i=Cc())!==a&&_c()!==a&&(s=Ki())!==a&&_c()!==a&&wc()!==a&&_c()!==a&&(c=Ki())!==a&&_c()!==a&&Tc()!==a&&_c()!==a&&Tc()!==a?(r,e=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:{dataType:"DECIMAL("+e+", "+n+")"}}}(e,o,s,c),r=e):(Ha=r,r=a),r===a&&(r=Ha,(e=Hs())===a&&(e=Ys()),e!==a&&_c()!==a&&(n=Cc())!==a&&_c()!==a&&(o=ii())!==a&&_c()!==a&&Os()!==a&&_c()!==a&&(u=function(){var r;(r=function(){var r,e,n,o;r=Ha,"signed"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(yo));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SIGNED"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=Zs());return r}())!==a&&_c()!==a?((i=Js())===a&&(i=null),i!==a&&_c()!==a&&(s=Tc())!==a?(r,e=function(t,r,e,n){return{type:"cast",keyword:t.toLowerCase(),expr:r,symbol:"as",target:{dataType:e+(n?" "+n:"")}}}(e,o,u,i),r=e):(Ha=r,r=a)):(Ha=r,r=a)))));return r}())===a&&(r=Hi())===a&&(r=xi())===a&&(r=Bi())===a&&(r=oi())===a&&(r=ni())===a&&(r=Li())===a&&(r=_i())===a&&(r=Ha,Cc()!==a&&(e=_c())!==a&&(n=ci())!==a&&_c()!==a&&Tc()!==a?(r,(o=n).parentheses=!0,r=o):(Ha=r,r=a),r===a&&(r=Xc())===a&&(r=Ha,_c()!==a?(36===t.charCodeAt(Ha)?(e="$",Ha++):(e=a,0===Wa&&za(pr)),e!==a&&(n=Zi())!==a?(r,r={type:"origin",value:"$"+n.value}):(Ha=r,r=a)):(Ha=r,r=a))),r}function Li(){var t,r,e,n,o,u,i,s,c,l,f,p;if(t=Ha,r=Ha,(e=Ci())!==a&&(n=_c())!==a&&(o=jc())!==a?r=e=[e,n,o]:(Ha=r,r=a),r===a&&(r=null),r!==a&&(e=_c())!==a&&(n=Lc())!==a?(t,t=r=function(t){const r=t&&t[0]||null;return ll.add(`select::${r}::(.*)`),{type:"column_ref",table:r,column:"*"}}(r)):(Ha=t,t=a),t===a){if(t=Ha,r=Ha,(e=Ci())!==a&&(n=_c())!==a&&(o=jc())!==a?r=e=[e,n,o]:(Ha=r,r=a),r===a&&(r=null),r!==a)if((e=_c())!==a)if((n=Ai())!==a)if((o=_c())!==a){if(u=[],i=Ha,(s=Ic())===a&&(s=Rc()),s!==a&&(c=_c())!==a?((l=Qi())===a&&(l=Zi()),l!==a?i=s=[s,c,l]:(Ha=i,i=a)):(Ha=i,i=a),i!==a)for(;i!==a;)u.push(i),i=Ha,(s=Ic())===a&&(s=Rc()),s!==a&&(c=_c())!==a?((l=Qi())===a&&(l=Zi()),l!==a?i=s=[s,c,l]:(Ha=i,i=a)):(Ha=i,i=a);else u=a;u!==a?(t,t=r=function(t,r,e){const n=t&&t[0]||null;return ll.add(`select::${n}::${r}`),{type:"column_ref",table:n,column:r,arrows:e.map(t=>t[0]),properties:e.map(t=>t[2])}}(r,n,u)):(Ha=t,t=a)}else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;else Ha=t,t=a;t===a&&(t=Ha,(r=Ci())!==a&&(e=_c())!==a&&(n=jc())!==a&&(o=_c())!==a&&(u=Ai())!==a?(t,f=r,p=u,ll.add(`select::${f}::${p}`),t=r={type:"column_ref",table:f,column:p}):(Ha=t,t=a),t===a&&(t=Ha,(r=Ai())!==a&&(t,r=function(t){return ll.add("select::null::"+t),{type:"column_ref",table:null,column:t}}(r)),t=r))}return t}function Ci(){var t,r;return t=Ha,(r=Ri())!==a?(Ha,(br(r)?a:void 0)!==a?(t,t=r=r):(Ha=t,t=a)):(Ha=t,t=a),t===a&&(t=Ha,(r=Ti())!==a&&(t,r=r),t=r),t}function Ti(){var r;return(r=function(){var r,e,n,o;r=Ha,34===t.charCodeAt(Ha)?(e='"',Ha++):(e=a,0===Wa&&za(vr));if(e!==a){if(n=[],hr.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(dr)),o!==a)for(;o!==a;)n.push(o),hr.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(dr));else n=a;n!==a?(34===t.charCodeAt(Ha)?(o='"',Ha++):(o=a,0===Wa&&za(vr)),o!==a?(r,e=yr(n),r=e):(Ha=r,r=a)):(Ha=r,r=a)}else Ha=r,r=a;return r}())===a&&(r=function(){var r,e,n,o;r=Ha,39===t.charCodeAt(Ha)?(e="'",Ha++):(e=a,0===Wa&&za(mt));if(e!==a){if(n=[],Or.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(mr)),o!==a)for(;o!==a;)n.push(o),Or.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(mr));else n=a;n!==a?(39===t.charCodeAt(Ha)?(o="'",Ha++):(o=a,0===Wa&&za(mt)),o!==a?(r,e=yr(n),r=e):(Ha=r,r=a)):(Ha=r,r=a)}else Ha=r,r=a;return r}())===a&&(r=function(){var r,e,n,o;r=Ha,96===t.charCodeAt(Ha)?(e="`",Ha++):(e=a,0===Wa&&za(Er));if(e!==a){if(n=[],jr.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(wr)),o!==a)for(;o!==a;)n.push(o),jr.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(wr));else n=a;n!==a?(96===t.charCodeAt(Ha)?(o="`",Ha++):(o=a,0===Wa&&za(Er)),o!==a?(r,e=yr(n),r=e):(Ha=r,r=a)):(Ha=r,r=a)}else Ha=r,r=a;return r}()),r}function Si(){var t,r;return t=Ha,(r=gi())!==a&&(t,r=r),(t=r)===a&&(t=Ti()),t}function Ai(){var t,r;return t=Ha,(r=gi())!==a?(Ha,(br(r)?a:void 0)!==a?(t,t=r=r):(Ha=t,t=a)):(Ha=t,t=a),t===a&&(t=Ti()),t}function gi(){var t,r,e,n;if(t=Ha,(r=Ii())!==a){for(e=[],n=Ui();n!==a;)e.push(n),n=Ui();e!==a?(t,t=r=r+e.join("")):(Ha=t,t=a)}else Ha=t,t=a;return t}function Ri(){var t,r,e,n;if(t=Ha,(r=Ii())!==a){for(e=[],n=Ni();n!==a;)e.push(n),n=Ni();e!==a?(t,t=r=r+e.join("")):(Ha=t,t=a)}else Ha=t,t=a;return t}function Ii(){var r;return Lr.test(t.charAt(Ha))?(r=t.charAt(Ha),Ha++):(r=a,0===Wa&&za(Cr)),r}function Ni(){var r;return Tr.test(t.charAt(Ha))?(r=t.charAt(Ha),Ha++):(r=a,0===Wa&&za(Sr)),r}function Ui(){var r;return Ar.test(t.charAt(Ha))?(r=t.charAt(Ha),Ha++):(r=a,0===Wa&&za(gr)),r}function _i(){var r,e,n,o;return r=Ha,e=Ha,58===t.charCodeAt(Ha)?(n=":",Ha++):(n=a,0===Wa&&za(Rr)),n!==a&&(o=Ri())!==a?e=n=[n,o]:(Ha=e,e=a),e!==a&&(r,e={type:"param",value:e[1]}),r=e}function xi(){var r;return(r=function(){var r,e,n;r=Ha,(e=function(){var r,e,n,o;r=Ha,"count"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(Fn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="COUNT"):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(n=function(){var r,e,n,o,u,i,s,c,l,f;r=Ha,(e=function(){var r,e;r=Ha,42===t.charCodeAt(Ha)?(e="*",Ha++):(e=a,0===Wa&&za(cr));e!==a&&(r,e={type:"star",value:"*"});return r=e}())!==a&&(r,e={expr:e});if((r=e)===a){if(r=Ha,(e=Ns())===a&&(e=null),e!==a)if(_c()!==a)if((n=Cc())!==a)if(_c()!==a)if((o=ii())!==a)if(_c()!==a)if(Tc()!==a){for(u=[],i=Ha,(s=_c())!==a?((c=Ds())===a&&(c=qs()),c!==a&&(l=_c())!==a&&(f=ii())!==a?i=s=[s,c,l,f]:(Ha=i,i=a)):(Ha=i,i=a);i!==a;)u.push(i),i=Ha,(s=_c())!==a?((c=Ds())===a&&(c=qs()),c!==a&&(l=_c())!==a&&(f=ii())!==a?i=s=[s,c,l,f]:(Ha=i,i=a)):(Ha=i,i=a);u!==a&&(i=_c())!==a?((s=Qu())===a&&(s=null),s!==a?(r,e=function(t,r,e,n){const o=e.length;let a=r;a.parentheses=!0;for(let t=0;t<o;++t)a=rl(e[t][1],a,e[t][3]);return{distinct:t,expr:a,orderby:n}}(e,o,u,s),r=e):(Ha=r,r=a)):(Ha=r,r=a)}else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;r===a&&(r=Ha,(e=Ns())===a&&(e=null),e!==a&&_c()!==a&&(n=Gu())!==a&&_c()!==a?((o=Qu())===a&&(o=null),o!==a?(r,r=e={distinct:e,expr:n,orderby:o}):(Ha=r,r=a)):(Ha=r,r=a))}return r}())!==a&&_c()!==a&&Tc()!==a?(r,r=e={type:"aggr_func",name:e,args:n}):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o,u,i,s,c;r=Ha,(e=function(){var r;(r=function(){var r,e,n,o;r=Ha,"sum"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Qn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SUM"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"max"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Hn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="MAX"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"min"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Yn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="MIN"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"avg"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za($n));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="AVG"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"collect"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Wn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="COLLECT"):(Ha=r,r=a)):(Ha=r,r=a);return r}());return r}())!==a&&_c()!==a&&Cc()!==a&&_c()!==a?((n=Ns())===a&&(n=null),n!==a&&(o=_c())!==a&&(u=Oi())!==a&&(i=_c())!==a&&(s=Tc())!==a?(r,r=e={type:"aggr_func",name:e,args:{expr:u,distinct:n}}):(Ha=r,r=a)):(Ha=r,r=a);r===a&&(r=Ha,(e=function(){var r;(r=function(){var r,e,n,o;r=Ha,"rank"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Xn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="RANK"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"dense_rank"===t.substr(Ha,10).toLowerCase()?(e=t.substr(Ha,10),Ha+=10):(e=a,0===Wa&&za(Zn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="DENSE_RANK"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"row_number"===t.substr(Ha,10).toLowerCase()?(e=t.substr(Ha,10),Ha+=10):(e=a,0===Wa&&za(Jn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="ROW_NUMBER"):(Ha=r,r=a)):(Ha=r,r=a);return r}());return r}())!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(n=Tc())!==a?(r,e=function(t){return{type:"aggr_func",name:t}}(e),r=e):(Ha=r,r=a),r===a&&(r=Ha,(e=function(){var r,e,n,o;r=Ha,"listagg"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Kn));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="LISTAGG"):(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(n=Oi())!==a?(o=Ha,(u=_c())!==a&&(i=wc())!==a&&(s=_c())!==a&&(c=Qi())!==a?o=u=[u,i,s,c]:(Ha=o,o=a),o===a&&(o=null),o!==a&&(u=_c())!==a&&(i=Tc())!==a?(r,e=function(t,r,e){return{type:"aggr_func",name:t,args:{expr:r,separator:e}}}(e,n,o),r=e):(Ha=r,r=a)):(Ha=r,r=a)));return r}()),r}function ki(){var t,r,e;return t=Ha,Es()!==a&&_c()!==a&&us()!==a&&_c()!==a&&(r=lc())!==a&&_c()!==a&&Cc()!==a&&_c()!==a?((e=ei())===a&&(e=null),e!==a&&_c()!==a&&Tc()!==a?(t,t={type:"on update",keyword:r,parentheses:!0,expr:e}):(Ha=t,t=a)):(Ha=t,t=a),t===a&&(t=Ha,Es()!==a&&_c()!==a&&us()!==a&&_c()!==a&&(r=lc())!==a?(t,t=function(t){return{type:"on update",keyword:t}}(r)):(Ha=t,t=a)),t}function Mi(){var r,e,n,o;return r=Ha,"over"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Ir)),e!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&vs()!==a&&_c()!==a&&As()!==a&&_c()!==a&&(n=Ru())!==a&&_c()!==a?((o=Qu())===a&&(o=null),o!==a&&_c()!==a&&Tc()!==a?(r,r=e={partitionby:n,orderby:o}):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=ki()),r}function Vi(){var r,e,n;return r=Ha,"position"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(Nr)),e!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(n=function(){var t,r,e,n,o,u,i,s;return t=Ha,(r=Qi())!==a&&_c()!==a&&_s()!==a&&_c()!==a&&(e=ii())!==a?(n=Ha,(o=_c())!==a&&(u=ds())!==a&&(i=_c())!==a&&(s=Zi())!==a?n=o=[o,u,i,s]:(Ha=n,n=a),n===a&&(n=null),n!==a?(t,t=r=function(t,r,e){let n=[t,{type:"origin",value:"in"},r];return e&&(n.push({type:"origin",value:"from"}),n.push(e[3])),{type:"expr_list",value:n}}(r,e,n)):(Ha=t,t=a)):(Ha=t,t=a),t}())!==a&&_c()!==a&&Tc()!==a?(r,r=e={type:"function",name:"POSITION",separator:" ",args:n}):(Ha=r,r=a),r}function Pi(){var r,e,n;return r=Ha,(e=function(){var r;return"both"===t.substr(Ha,4).toLowerCase()?(r=t.substr(Ha,4),Ha+=4):(r=a,0===Wa&&za(Ur)),r===a&&("leading"===t.substr(Ha,7).toLowerCase()?(r=t.substr(Ha,7),Ha+=7):(r=a,0===Wa&&za(_r)),r===a&&("trailing"===t.substr(Ha,8).toLowerCase()?(r=t.substr(Ha,8),Ha+=8):(r=a,0===Wa&&za(xr)))),r}())===a&&(e=null),e!==a&&_c()!==a?((n=Qi())===a&&(n=null),n!==a&&_c()!==a&&ds()!==a?(r,r=e=function(t,r,e){let n=[];return t&&n.push({type:"origin",value:t}),r&&n.push(r),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):(Ha=r,r=a)):(Ha=r,r=a),r}function Di(){var r,e,n;return r=Ha,"overlay"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Pr)),e!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(n=function(){var r,e,n,o,u,i,s,c,l,f;return r=Ha,(e=ii())!==a&&_c()!==a?("placing"===t.substr(Ha,7).toLowerCase()?(n=t.substr(Ha,7),Ha+=7):(n=a,0===Wa&&za(Mr)),n!==a&&_c()!==a&&(o=ii())!==a&&_c()!==a&&ds()!==a&&_c()!==a&&(u=Zi())!==a?(i=Ha,(s=_c())!==a?("for"===t.substr(Ha,3).toLowerCase()?(c=t.substr(Ha,3),Ha+=3):(c=a,0===Wa&&za(Vr)),c!==a&&(l=_c())!==a&&(f=Zi())!==a?i=s=[s,c,l,f]:(Ha=i,i=a)):(Ha=i,i=a),i===a&&(i=null),i!==a?(r,r=e=function(t,r,e,n){let o=[t,{type:"origin",value:"placing"},r,{type:"origin",value:"from"},e];return n&&(o.push({type:"origin",value:"for"}),o.push(n[3])),{type:"expr_list",value:o}}(e,o,u,i)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r}())!==a&&_c()!==a&&Tc()!==a?(r,r=e={type:"function",name:"OVERLAY",separator:" ",args:n}):(Ha=r,r=a),r}function qi(){var r,e,n;return r=Ha,"substring"===t.substr(Ha,9).toLowerCase()?(e=t.substr(Ha,9),Ha+=9):(e=a,0===Wa&&za(Dr)),e!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(n=function(){var r,e,n,o,u,i,s,c;return r=Ha,(e=ii())!==a&&_c()!==a&&ds()!==a&&_c()!==a&&(n=Zi())!==a?(o=Ha,(u=_c())!==a?("for"===t.substr(Ha,3).toLowerCase()?(i=t.substr(Ha,3),Ha+=3):(i=a,0===Wa&&za(Vr)),i!==a&&(s=_c())!==a&&(c=Zi())!==a?o=u=[u,i,s,c]:(Ha=o,o=a)):(Ha=o,o=a),o===a&&(o=null),o!==a?(r,r=e=function(t,r,e){let n=[t,{type:"origin",value:"from"},r];return e&&(n.push({type:"origin",value:"for"}),n.push(e[3])),{type:"expr_list",value:n}}(e,n,o)):(Ha=r,r=a)):(Ha=r,r=a),r}())!==a&&_c()!==a&&Tc()!==a?(r,r=e={type:"function",name:"SUBSTRING",separator:" ",args:n}):(Ha=r,r=a),r}function Bi(){var r,e,n,o,u;return(r=Vi())===a&&(r=function(){var r,e,n,o;return r=Ha,"trim"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(kr)),e!==a&&_c()!==a&&Cc()!==a&&_c()!==a?((n=Pi())===a&&(n=null),n!==a&&_c()!==a&&(o=ii())!==a&&_c()!==a&&Tc()!==a?(r,r=e=function(t,r){let e=t||{type:"expr_list",value:[]};return e.value.push(r),{type:"function",name:"TRIM",args:e}}(n,o)):(Ha=r,r=a)):(Ha=r,r=a),r}())===a&&(r=qi())===a&&(r=Di())===a&&(r=Ha,(e=function(){var r;(r=Fi())===a&&(r=function(){var r,e,n,o;r=Ha,"current_user"===t.substr(Ha,12).toLowerCase()?(e=t.substr(Ha,12),Ha+=12):(e=a,0===Wa&&za(Qo));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="CURRENT_USER"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"user"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Po));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="USER"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"session_user"===t.substr(Ha,12).toLowerCase()?(e=t.substr(Ha,12),Ha+=12):(e=a,0===Wa&&za($o));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SESSION_USER"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"system_user"===t.substr(Ha,11).toLowerCase()?(e=t.substr(Ha,11),Ha+=11):(e=a,0===Wa&&za(Wo));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SYSTEM_USER"):(Ha=r,r=a)):(Ha=r,r=a);return r}());return r}())!==a&&_c()!==a&&(n=Cc())!==a&&_c()!==a?((o=ei())===a&&(o=null),o!==a&&_c()!==a&&Tc()!==a&&_c()!==a?((u=Mi())===a&&(u=null),u!==a?(r,r=e={type:"function",name:e,args:o||{type:"expr_list",value:[]},over:u}):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=function(){var t,r,e,n,o;t=Ha,(r=Bs())!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(e=Gi())!==a&&_c()!==a&&ds()!==a&&_c()!==a?((n=ic())===a&&(n=cc())===a&&(n=uc())===a&&(n=oc()),n!==a&&_c()!==a&&(o=ii())!==a&&_c()!==a&&Tc()!==a?(t,u=e,i=n,s=o,r={type:r.toLowerCase(),args:{field:u,cast_type:i,source:s}},t=r):(Ha=t,t=a)):(Ha=t,t=a);var u,i,s;t===a&&(t=Ha,(r=Bs())!==a&&_c()!==a&&Cc()!==a&&_c()!==a&&(e=Gi())!==a&&_c()!==a&&ds()!==a&&_c()!==a&&(n=ii())!==a&&_c()!==a&&(o=Tc())!==a?(t,r=function(t,r,e){return{type:t.toLowerCase(),args:{field:r,source:e}}}(r,e,n),t=r):(Ha=t,t=a));return t}())===a&&(r=Ha,(e=Fi())!==a&&_c()!==a?((n=ki())===a&&(n=null),n!==a?(r,r=e={type:"function",name:e,over:n}):(Ha=r,r=a)):(Ha=r,r=a),r===a&&(r=Ha,(e=Qc())!==a&&_c()!==a&&(n=Cc())!==a&&_c()!==a?((o=ci())===a&&(o=null),o!==a&&_c()!==a&&Tc()!==a&&_c()!==a?((u=Mi())===a&&(u=null),u!==a?(r,r=e=function(t,r,e){return r&&"expr_list"!==r.type&&(r={type:"expr_list",value:[r]}),{type:"function",name:t,args:r||{type:"expr_list",value:[]},over:e}}(e,o,u)):(Ha=r,r=a)):(Ha=r,r=a)):(Ha=r,r=a)))),r}function Gi(){var r,e;return r=Ha,"century"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(qr)),e===a&&("day"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Br)),e===a&&("date"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Gr)),e===a&&("decade"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Fr)),e===a&&("dow"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Hr)),e===a&&("doy"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Yr)),e===a&&("epoch"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(Qr)),e===a&&("hour"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za($r)),e===a&&("isodow"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Wr)),e===a&&("isoyear"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Xr)),e===a&&("microseconds"===t.substr(Ha,12).toLowerCase()?(e=t.substr(Ha,12),Ha+=12):(e=a,0===Wa&&za(Zr)),e===a&&("millennium"===t.substr(Ha,10).toLowerCase()?(e=t.substr(Ha,10),Ha+=10):(e=a,0===Wa&&za(Kr)),e===a&&("milliseconds"===t.substr(Ha,12).toLowerCase()?(e=t.substr(Ha,12),Ha+=12):(e=a,0===Wa&&za(Jr)),e===a&&("minute"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(zr)),e===a&&("month"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(te)),e===a&&("quarter"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(re)),e===a&&("second"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(ee)),e===a&&("timezone"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(ne)),e===a&&("timezone_hour"===t.substr(Ha,13).toLowerCase()?(e=t.substr(Ha,13),Ha+=13):(e=a,0===Wa&&za(oe)),e===a&&("timezone_minute"===t.substr(Ha,15).toLowerCase()?(e=t.substr(Ha,15),Ha+=15):(e=a,0===Wa&&za(ae)),e===a&&("week"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(ue)),e===a&&("year"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(ie))))))))))))))))))))))),e!==a&&(r,e=e),r=e}function Fi(){var r;return(r=function(){var r,e,n,o;r=Ha,"current_date"===t.substr(Ha,12).toLowerCase()?(e=t.substr(Ha,12),Ha+=12):(e=a,0===Wa&&za(Go));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="CURRENT_DATE"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n,o;r=Ha,"current_time"===t.substr(Ha,12).toLowerCase()?(e=t.substr(Ha,12),Ha+=12):(e=a,0===Wa&&za(Ho));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="CURRENT_TIME"):(Ha=r,r=a)):(Ha=r,r=a);return r}())===a&&(r=lc()),r}function Hi(){var r;return(r=Qi())===a&&(r=Zi())===a&&(r=function(){var r,e;r=Ha,(e=function(){var r,e,n,o;r=Ha,"true"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(De));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&(r,e={type:"bool",value:!0});(r=e)===a&&(r=Ha,(e=function(){var r,e,n,o;r=Ha,"false"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(Be));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&(r,e={type:"bool",value:!1}),r=e);return r}())===a&&(r=Yi())===a&&(r=function(){var r,e,n,o,u,i;r=Ha,(e=uc())===a&&(e=oc())===a&&(e=ic())===a&&(e=ac());if(e!==a)if(_c()!==a){if(n=Ha,39===t.charCodeAt(Ha)?(o="'",Ha++):(o=a,0===Wa&&za(mt)),o!==a){for(u=[],i=Wi();i!==a;)u.push(i),i=Wi();u!==a?(39===t.charCodeAt(Ha)?(i="'",Ha++):(i=a,0===Wa&&za(mt)),i!==a?n=o=[o,u,i]:(Ha=n,n=a)):(Ha=n,n=a)}else Ha=n,n=a;n!==a?(r,s=n,e={type:e.toLowerCase(),value:s[1].join("")},r=e):(Ha=r,r=a)}else Ha=r,r=a;else Ha=r,r=a;var s;if(r===a)if(r=Ha,(e=uc())===a&&(e=oc())===a&&(e=ic())===a&&(e=ac()),e!==a)if(_c()!==a){if(n=Ha,34===t.charCodeAt(Ha)?(o='"',Ha++):(o=a,0===Wa&&za(vr)),o!==a){for(u=[],i=$i();i!==a;)u.push(i),i=$i();u!==a?(34===t.charCodeAt(Ha)?(i='"',Ha++):(i=a,0===Wa&&za(vr)),i!==a?n=o=[o,u,i]:(Ha=n,n=a)):(Ha=n,n=a)}else Ha=n,n=a;n!==a?(r,e=function(t,r){return{type:t.toLowerCase(),value:r[1].join("")}}(e,n),r=e):(Ha=r,r=a)}else Ha=r,r=a;else Ha=r,r=a;return r}()),r}function Yi(){var r,e;return r=Ha,(e=function(){var r,e,n,o;r=Ha,"null"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Ve));e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a);return r}())!==a&&(r,e={type:"null",value:null}),r=e}function Qi(){var r,e,n,o,u;if(r=Ha,e=Ha,39===t.charCodeAt(Ha)?(n="'",Ha++):(n=a,0===Wa&&za(mt)),n!==a){for(o=[],u=Wi();u!==a;)o.push(u),u=Wi();o!==a?(39===t.charCodeAt(Ha)?(u="'",Ha++):(u=a,0===Wa&&za(mt)),u!==a?e=n=[n,o,u]:(Ha=e,e=a)):(Ha=e,e=a)}else Ha=e,e=a;if(e!==a&&(r,e={type:"single_quote_string",value:e[1].join("")}),(r=e)===a){if(r=Ha,e=Ha,34===t.charCodeAt(Ha)?(n='"',Ha++):(n=a,0===Wa&&za(vr)),n!==a){for(o=[],u=$i();u!==a;)o.push(u),u=$i();o!==a?(34===t.charCodeAt(Ha)?(u='"',Ha++):(u=a,0===Wa&&za(vr)),u!==a?e=n=[n,o,u]:(Ha=e,e=a)):(Ha=e,e=a)}else Ha=e,e=a;e!==a?(n=Ha,Wa++,o=jc(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e=function(t){return{type:"double_quote_string",value:t[1].join("")}}(e)):(Ha=r,r=a)):(Ha=r,r=a)}return r}function $i(){var r;return se.test(t.charAt(Ha))?(r=t.charAt(Ha),Ha++):(r=a,0===Wa&&za(ce)),r===a&&(r=Xi()),r}function Wi(){var r;return le.test(t.charAt(Ha))?(r=t.charAt(Ha),Ha++):(r=a,0===Wa&&za(fe)),r===a&&(r=Xi()),r}function Xi(){var r,e,n,o,u,i,s,c,l,f;return r=Ha,"\\'"===t.substr(Ha,2)?(e="\\'",Ha+=2):(e=a,0===Wa&&za(pe)),e!==a&&(r,e="\\'"),(r=e)===a&&(r=Ha,'\\"'===t.substr(Ha,2)?(e='\\"',Ha+=2):(e=a,0===Wa&&za(be)),e!==a&&(r,e='\\"'),(r=e)===a&&(r=Ha,"\\\\"===t.substr(Ha,2)?(e="\\\\",Ha+=2):(e=a,0===Wa&&za(ve)),e!==a&&(r,e="\\\\"),(r=e)===a&&(r=Ha,"\\/"===t.substr(Ha,2)?(e="\\/",Ha+=2):(e=a,0===Wa&&za(he)),e!==a&&(r,e="\\/"),(r=e)===a&&(r=Ha,"\\b"===t.substr(Ha,2)?(e="\\b",Ha+=2):(e=a,0===Wa&&za(de)),e!==a&&(r,e="\b"),(r=e)===a&&(r=Ha,"\\f"===t.substr(Ha,2)?(e="\\f",Ha+=2):(e=a,0===Wa&&za(ye)),e!==a&&(r,e="\f"),(r=e)===a&&(r=Ha,"\\n"===t.substr(Ha,2)?(e="\\n",Ha+=2):(e=a,0===Wa&&za(Oe)),e!==a&&(r,e="\n"),(r=e)===a&&(r=Ha,"\\r"===t.substr(Ha,2)?(e="\\r",Ha+=2):(e=a,0===Wa&&za(me)),e!==a&&(r,e="\r"),(r=e)===a&&(r=Ha,"\\t"===t.substr(Ha,2)?(e="\\t",Ha+=2):(e=a,0===Wa&&za(Ee)),e!==a&&(r,e="\t"),(r=e)===a&&(r=Ha,"\\u"===t.substr(Ha,2)?(e="\\u",Ha+=2):(e=a,0===Wa&&za(je)),e!==a&&(n=es())!==a&&(o=es())!==a&&(u=es())!==a&&(i=es())!==a?(r,s=n,c=o,l=u,f=i,r=e=String.fromCharCode(parseInt("0x"+s+c+l+f))):(Ha=r,r=a),r===a&&(r=Ha,92===t.charCodeAt(Ha)?(e="\\",Ha++):(e=a,0===Wa&&za(we)),e!==a&&(r,e="\\"),(r=e)===a&&(r=Ha,"''"===t.substr(Ha,2)?(e="''",Ha+=2):(e=a,0===Wa&&za(Le)),e!==a&&(r,e="''"),(r=e)===a&&(r=Ha,'""'===t.substr(Ha,2)?(e='""',Ha+=2):(e=a,0===Wa&&za(Ce)),e!==a&&(r,e='""'),(r=e)===a&&(r=Ha,"``"===t.substr(Ha,2)?(e="``",Ha+=2):(e=a,0===Wa&&za(Te)),e!==a&&(r,e="``"),r=e))))))))))))),r}function Zi(){var t,r,e;return t=Ha,(r=function(){var t,r,e,n;t=Ha,(r=Ki())!==a&&(e=Ji())!==a&&(n=zi())!==a?(t,t=r={type:"bigint",value:r+e+n}):(Ha=t,t=a);t===a&&(t=Ha,(r=Ki())!==a&&(e=Ji())!==a?(t,r=function(t,r){const e=t+r;return el(t)?{type:"bigint",value:e}:parseFloat(e)}(r,e),t=r):(Ha=t,t=a),t===a&&(t=Ha,(r=Ki())!==a&&(e=zi())!==a?(t,r=function(t,r){return{type:"bigint",value:t+r}}(r,e),t=r):(Ha=t,t=a),t===a&&(t=Ha,(r=Ki())!==a&&(t,r=function(t){return el(t)?{type:"bigint",value:t}:parseFloat(t)}(r)),t=r)));return t}())!==a&&(t,r=(e=r)&&"bigint"===e.type?e:{type:"number",value:e}),t=r}function Ki(){var r,e,n;return(r=ts())===a&&(r=rs())===a&&(r=Ha,45===t.charCodeAt(Ha)?(e="-",Ha++):(e=a,0===Wa&&za(sr)),e===a&&(43===t.charCodeAt(Ha)?(e="+",Ha++):(e=a,0===Wa&&za(ir))),e!==a&&(n=ts())!==a?(r,r=e=e+n):(Ha=r,r=a),r===a&&(r=Ha,45===t.charCodeAt(Ha)?(e="-",Ha++):(e=a,0===Wa&&za(sr)),e===a&&(43===t.charCodeAt(Ha)?(e="+",Ha++):(e=a,0===Wa&&za(ir))),e!==a&&(n=rs())!==a?(r,r=e=function(t,r){return t+r}(e,n)):(Ha=r,r=a))),r}function Ji(){var r,e,n;return r=Ha,46===t.charCodeAt(Ha)?(e=".",Ha++):(e=a,0===Wa&&za(ge)),e!==a&&(n=ts())!==a?(r,r=e="."+n):(Ha=r,r=a),r}function zi(){var r,e,n;return r=Ha,(e=function(){var r,e,n;r=Ha,_e.test(t.charAt(Ha))?(e=t.charAt(Ha),Ha++):(e=a,0===Wa&&za(xe));e!==a?(ke.test(t.charAt(Ha))?(n=t.charAt(Ha),Ha++):(n=a,0===Wa&&za(Me)),n===a&&(n=null),n!==a?(r,r=e=e+(null!==(o=n)?o:"")):(Ha=r,r=a)):(Ha=r,r=a);var o;return r}())!==a&&(n=ts())!==a?(r,r=e=e+n):(Ha=r,r=a),r}function ts(){var t,r,e;if(t=Ha,r=[],(e=rs())!==a)for(;e!==a;)r.push(e),e=rs();else r=a;return r!==a&&(t,r=r.join("")),t=r}function rs(){var r;return Re.test(t.charAt(Ha))?(r=t.charAt(Ha),Ha++):(r=a,0===Wa&&za(Ie)),r}function es(){var r;return Ne.test(t.charAt(Ha))?(r=t.charAt(Ha),Ha++):(r=a,0===Wa&&za(Ue)),r}function ns(){var r,e,n,o;return r=Ha,"default"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(L)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function os(){var r,e,n,o;return r=Ha,"to"===t.substr(Ha,2).toLowerCase()?(e=t.substr(Ha,2),Ha+=2):(e=a,0===Wa&&za(qe)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function as(){var r,e,n,o;return r=Ha,"drop"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Ge)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="DROP"):(Ha=r,r=a)):(Ha=r,r=a),r}function us(){var r,e,n,o;return r=Ha,"update"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Qe)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function is(){var r,e,n,o;return r=Ha,"create"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za($e)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function ss(){var r,e,n,o;return r=Ha,"temporary"===t.substr(Ha,9).toLowerCase()?(e=t.substr(Ha,9),Ha+=9):(e=a,0===Wa&&za(We)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function cs(){var r,e,n,o;return r=Ha,"delete"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Xe)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function ls(){var r,e,n,o;return r=Ha,"insert"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Ze)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function fs(){var r,e,n,o;return r=Ha,"replace"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Je)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function ps(){var r,e,n,o;return r=Ha,"rename"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(tn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function bs(){var r,e,n,o;return r=Ha,"ignore"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(rn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function vs(){var r,e,n,o;return r=Ha,"partition"===t.substr(Ha,9).toLowerCase()?(e=t.substr(Ha,9),Ha+=9):(e=a,0===Wa&&za(en)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="PARTITION"):(Ha=r,r=a)):(Ha=r,r=a),r}function hs(){var r,e,n,o;return r=Ha,"into"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(nn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function ds(){var r,e,n,o;return r=Ha,"from"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(on)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function ys(){var r,e,n,o;return r=Ha,"set"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(ct)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SET"):(Ha=r,r=a)):(Ha=r,r=a),r}function Os(){var r,e,n,o;return r=Ha,"as"===t.substr(Ha,2).toLowerCase()?(e=t.substr(Ha,2),Ha+=2):(e=a,0===Wa&&za(an)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function ms(){var r,e,n,o;return r=Ha,"table"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(un)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="TABLE"):(Ha=r,r=a)):(Ha=r,r=a),r}function Es(){var r,e,n,o;return r=Ha,"on"===t.substr(Ha,2).toLowerCase()?(e=t.substr(Ha,2),Ha+=2):(e=a,0===Wa&&za(X)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function js(){var r,e,n,o;return r=Ha,"join"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(dn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function ws(){var r,e,n,o;return r=Ha,"cross"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(yn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function Ls(){var r,e,n,o;return r=Ha,"outer"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(mn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function Cs(){var r,e,n,o;return r=Ha,"values"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(Ln)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function Ts(){var r,e,n,o;return r=Ha,"using"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(Cn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function Ss(){var r,e,n,o;return r=Ha,"with"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Dt)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function As(){var r,e,n,o;return r=Ha,"by"===t.substr(Ha,2).toLowerCase()?(e=t.substr(Ha,2),Ha+=2):(e=a,0===Wa&&za(An)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function gs(){var r,e,n,o;return r=Ha,"asc"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Un)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="ASC"):(Ha=r,r=a)):(Ha=r,r=a),r}function Rs(){var r,e,n,o;return r=Ha,"desc"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(_n)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="DESC"):(Ha=r,r=a)):(Ha=r,r=a),r}function Is(){var r,e,n,o;return r=Ha,"all"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(xn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="ALL"):(Ha=r,r=a)):(Ha=r,r=a),r}function Ns(){var r,e,n,o;return r=Ha,"distinct"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(kn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="DISTINCT"):(Ha=r,r=a)):(Ha=r,r=a),r}function Us(){var r,e,n,o;return r=Ha,"between"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Mn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="BETWEEN"):(Ha=r,r=a)):(Ha=r,r=a),r}function _s(){var r,e,n,o;return r=Ha,"in"===t.substr(Ha,2).toLowerCase()?(e=t.substr(Ha,2),Ha+=2):(e=a,0===Wa&&za(Lt)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="IN"):(Ha=r,r=a)):(Ha=r,r=a),r}function xs(){var r,e,n,o;return r=Ha,"is"===t.substr(Ha,2).toLowerCase()?(e=t.substr(Ha,2),Ha+=2):(e=a,0===Wa&&za(Vn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="IS"):(Ha=r,r=a)):(Ha=r,r=a),r}function ks(){var r,e,n,o;return r=Ha,"like"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Pn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="LIKE"):(Ha=r,r=a)):(Ha=r,r=a),r}function Ms(){var r,e,n,o;return r=Ha,"similar"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(Dn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SIMILAR"):(Ha=r,r=a)):(Ha=r,r=a),r}function Vs(){var r,e,n,o;return r=Ha,"exists"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(qn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="EXISTS"):(Ha=r,r=a)):(Ha=r,r=a),r}function Ps(){var r,e,n,o;return r=Ha,"not"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(tt)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="NOT"):(Ha=r,r=a)):(Ha=r,r=a),r}function Ds(){var r,e,n,o;return r=Ha,"and"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Bn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="AND"):(Ha=r,r=a)):(Ha=r,r=a),r}function qs(){var r,e,n,o;return r=Ha,"or"===t.substr(Ha,2).toLowerCase()?(e=t.substr(Ha,2),Ha+=2):(e=a,0===Wa&&za(Gn)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="OR"):(Ha=r,r=a)):(Ha=r,r=a),r}function Bs(){var r,e,n,o;return r=Ha,"extract"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(to)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="EXTRACT"):(Ha=r,r=a)):(Ha=r,r=a),r}function Gs(){var r,e,n,o;return r=Ha,"case"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(eo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function Fs(){var r,e,n,o;return r=Ha,"when"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(no)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?r=e=[e,n]:(Ha=r,r=a)):(Ha=r,r=a),r}function Hs(){var r,e,n,o;return r=Ha,"cast"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(io)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="CAST"):(Ha=r,r=a)):(Ha=r,r=a),r}function Ys(){var r,e,n,o;return r=Ha,"try_cast"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(so)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="TRY_CAST"):(Ha=r,r=a)):(Ha=r,r=a),r}function Qs(){var r,e,n,o;return r=Ha,"char"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(fo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="CHAR"):(Ha=r,r=a)):(Ha=r,r=a),r}function $s(){var r,e,n,o;return r=Ha,"varchar"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(po)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="VARCHAR"):(Ha=r,r=a)):(Ha=r,r=a),r}function Ws(){var r,e,n,o;return r=Ha,"numeric"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(vo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="NUMERIC"):(Ha=r,r=a)):(Ha=r,r=a),r}function Xs(){var r,e,n,o;return r=Ha,"decimal"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(ho)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="DECIMAL"):(Ha=r,r=a)):(Ha=r,r=a),r}function Zs(){var r,e,n,o;return r=Ha,"unsigned"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(Oo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="UNSIGNED"):(Ha=r,r=a)):(Ha=r,r=a),r}function Ks(){var r,e,n,o;return r=Ha,"int"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(mo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="INT"):(Ha=r,r=a)):(Ha=r,r=a),r}function Js(){var r,e,n,o;return r=Ha,"integer"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(jo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="INTEGER"):(Ha=r,r=a)):(Ha=r,r=a),r}function zs(){var r,e,n,o;return r=Ha,"smallint"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(To)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="SMALLINT"):(Ha=r,r=a)):(Ha=r,r=a),r}function tc(){var r,e,n,o;return r=Ha,"tinyint"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(So)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="TINYINT"):(Ha=r,r=a)):(Ha=r,r=a),r}function rc(){var r,e,n,o;return r=Ha,"bigint"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(No)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="BIGINT"):(Ha=r,r=a)):(Ha=r,r=a),r}function ec(){var r,e,n,o;return r=Ha,"float"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(Uo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="FLOAT"):(Ha=r,r=a)):(Ha=r,r=a),r}function nc(){var r,e,n,o;return r=Ha,"double"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(_o)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="DOUBLE"):(Ha=r,r=a)):(Ha=r,r=a),r}function oc(){var r,e,n,o;return r=Ha,"date"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Gr)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="DATE"):(Ha=r,r=a)):(Ha=r,r=a),r}function ac(){var r,e,n,o;return r=Ha,"datetime"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(xo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="DATETIME"):(Ha=r,r=a)):(Ha=r,r=a),r}function uc(){var r,e,n,o;return r=Ha,"time"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(ko)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="TIME"):(Ha=r,r=a)):(Ha=r,r=a),r}function ic(){var r,e,n,o;return r=Ha,"timestamp"===t.substr(Ha,9).toLowerCase()?(e=t.substr(Ha,9),Ha+=9):(e=a,0===Wa&&za(Mo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="TIMESTAMP"):(Ha=r,r=a)):(Ha=r,r=a),r}function sc(){var r,e,n,o;return r=Ha,"truncate"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(Vo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="TRUNCATE"):(Ha=r,r=a)):(Ha=r,r=a),r}function cc(){var r,e,n,o;return r=Ha,"interval"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(Fo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="INTERVAL"):(Ha=r,r=a)):(Ha=r,r=a),r}function lc(){var r,e,n,o;return r=Ha,"current_timestamp"===t.substr(Ha,17).toLowerCase()?(e=t.substr(Ha,17),Ha+=17):(e=a,0===Wa&&za(Yo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="CURRENT_TIMESTAMP"):(Ha=r,r=a)):(Ha=r,r=a),r}function fc(){var r;return(r=function(){var r;return"@@"===t.substr(Ha,2)?(r="@@",Ha+=2):(r=a,0===Wa&&za(ra)),r}())===a&&(r=function(){var r;return 64===t.charCodeAt(Ha)?(r="@",Ha++):(r=a,0===Wa&&za(ta)),r}())===a&&(r=function(){var r;return 36===t.charCodeAt(Ha)?(r="$",Ha++):(r=a,0===Wa&&za(pr)),r}()),r}function pc(){var r;return"::"===t.substr(Ha,2)?(r="::",Ha+=2):(r=a,0===Wa&&za(oa)),r}function bc(){var r;return 61===t.charCodeAt(Ha)?(r="=",Ha++):(r=a,0===Wa&&za(Yt)),r}function vc(){var r,e,n,o;return r=Ha,"add"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(ua)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="ADD"):(Ha=r,r=a)):(Ha=r,r=a),r}function hc(){var r,e,n,o;return r=Ha,"column"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(ia)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="COLUMN"):(Ha=r,r=a)):(Ha=r,r=a),r}function dc(){var r,e,n,o;return r=Ha,"index"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(sa)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="INDEX"):(Ha=r,r=a)):(Ha=r,r=a),r}function yc(){var r,e,n,o;return r=Ha,"key"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(O)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="KEY"):(Ha=r,r=a)):(Ha=r,r=a),r}function Oc(){var r,e,n,o;return r=Ha,"unique"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(y)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="UNIQUE"):(Ha=r,r=a)):(Ha=r,r=a),r}function mc(){var r,e,n,o;return r=Ha,"comment"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(fa)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="COMMENT"):(Ha=r,r=a)):(Ha=r,r=a),r}function Ec(){var r,e,n,o;return r=Ha,"constraint"===t.substr(Ha,10).toLowerCase()?(e=t.substr(Ha,10),Ha+=10):(e=a,0===Wa&&za(pa)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="CONSTRAINT"):(Ha=r,r=a)):(Ha=r,r=a),r}function jc(){var r;return 46===t.charCodeAt(Ha)?(r=".",Ha++):(r=a,0===Wa&&za(ge)),r}function wc(){var r;return 44===t.charCodeAt(Ha)?(r=",",Ha++):(r=a,0===Wa&&za(ja)),r}function Lc(){var r;return 42===t.charCodeAt(Ha)?(r="*",Ha++):(r=a,0===Wa&&za(cr)),r}function Cc(){var r;return 40===t.charCodeAt(Ha)?(r="(",Ha++):(r=a,0===Wa&&za(_t)),r}function Tc(){var r;return 41===t.charCodeAt(Ha)?(r=")",Ha++):(r=a,0===Wa&&za(xt)),r}function Sc(){var r;return 60===t.charCodeAt(Ha)?(r="<",Ha++):(r=a,0===Wa&&za(Jt)),r}function Ac(){var r;return 62===t.charCodeAt(Ha)?(r=">",Ha++):(r=a,0===Wa&&za(Xt)),r}function gc(){var r;return 59===t.charCodeAt(Ha)?(r=";",Ha++):(r=a,0===Wa&&za(Ca)),r}function Rc(){var r;return"->"===t.substr(Ha,2)?(r="->",Ha+=2):(r=a,0===Wa&&za(Ta)),r}function Ic(){var r;return"->>"===t.substr(Ha,3)?(r="->>",Ha+=3):(r=a,0===Wa&&za(Sa)),r}function Nc(){var r;return"||"===t.substr(Ha,2)?(r="||",Ha+=2):(r=a,0===Wa&&za(Ra)),r}function Uc(){var r;return(r=Nc())===a&&(r=function(){var r;return"&&"===t.substr(Ha,2)?(r="&&",Ha+=2):(r=a,0===Wa&&za(Ia)),r}()),r}function _c(){var t,r;for(t=[],(r=Pc())===a&&(r=kc());r!==a;)t.push(r),(r=Pc())===a&&(r=kc());return t}function xc(){var t,r;if(t=[],(r=Pc())===a&&(r=kc()),r!==a)for(;r!==a;)t.push(r),(r=Pc())===a&&(r=kc());else t=a;return t}function kc(){var r;return(r=function(){var r,e,n,o,u,i;r=Ha,"/*"===t.substr(Ha,2)?(e="/*",Ha+=2):(e=a,0===Wa&&za(Na));if(e!==a){for(n=[],o=Ha,u=Ha,Wa++,"*/"===t.substr(Ha,2)?(i="*/",Ha+=2):(i=a,0===Wa&&za(Ua)),Wa--,i===a?u=void 0:(Ha=u,u=a),u!==a&&(i=Vc())!==a?o=u=[u,i]:(Ha=o,o=a);o!==a;)n.push(o),o=Ha,u=Ha,Wa++,"*/"===t.substr(Ha,2)?(i="*/",Ha+=2):(i=a,0===Wa&&za(Ua)),Wa--,i===a?u=void 0:(Ha=u,u=a),u!==a&&(i=Vc())!==a?o=u=[u,i]:(Ha=o,o=a);n!==a?("*/"===t.substr(Ha,2)?(o="*/",Ha+=2):(o=a,0===Wa&&za(Ua)),o!==a?r=e=[e,n,o]:(Ha=r,r=a)):(Ha=r,r=a)}else Ha=r,r=a;return r}())===a&&(r=function(){var r,e,n,o,u,i;r=Ha,"--"===t.substr(Ha,2)?(e="--",Ha+=2):(e=a,0===Wa&&za(_a));if(e!==a){for(n=[],o=Ha,u=Ha,Wa++,i=Dc(),Wa--,i===a?u=void 0:(Ha=u,u=a),u!==a&&(i=Vc())!==a?o=u=[u,i]:(Ha=o,o=a);o!==a;)n.push(o),o=Ha,u=Ha,Wa++,i=Dc(),Wa--,i===a?u=void 0:(Ha=u,u=a),u!==a&&(i=Vc())!==a?o=u=[u,i]:(Ha=o,o=a);n!==a?r=e=[e,n]:(Ha=r,r=a)}else Ha=r,r=a;return r}()),r}function Mc(){var t,r,e,n,o,u,i;return t=Ha,(r=mc())!==a&&_c()!==a?((e=bc())===a&&(e=null),e!==a&&_c()!==a&&(n=Qi())!==a?(t,u=e,i=n,t=r={type:(o=r).toLowerCase(),keyword:o.toLowerCase(),symbol:u,value:i}):(Ha=t,t=a)):(Ha=t,t=a),t}function Vc(){var r;return t.length>Ha?(r=t.charAt(Ha),Ha++):(r=a,0===Wa&&za(xa)),r}function Pc(){var r;return Ba.test(t.charAt(Ha))?(r=t.charAt(Ha),Ha++):(r=a,0===Wa&&za(Ga)),r}function Dc(){var r,e;if((r=function(){var r,e;r=Ha,Wa++,t.length>Ha?(e=t.charAt(Ha),Ha++):(e=a,0===Wa&&za(xa));Wa--,e===a?r=void 0:(Ha=r,r=a);return r}())===a)if(r=[],Se.test(t.charAt(Ha))?(e=t.charAt(Ha),Ha++):(e=a,0===Wa&&za(Ae)),e!==a)for(;e!==a;)r.push(e),Se.test(t.charAt(Ha))?(e=t.charAt(Ha),Ha++):(e=a,0===Wa&&za(Ae));else r=a;return r}function qc(){var r,e;return r=Ha,Ha,sl=[],(!0?void 0:a)!==a&&_c()!==a?((e=Bc())===a&&(e=function(){var r,e;r=Ha,function(){var r;return"return"===t.substr(Ha,6).toLowerCase()?(r=t.substr(Ha,6),Ha+=6):(r=a,0===Wa&&za(ea)),r}()!==a&&_c()!==a&&(e=Gc())!==a?(r,r={type:"return",expr:e}):(Ha=r,r=a);return r}()),e!==a?(r,r={type:"proc",stmt:e,vars:sl}):(Ha=r,r=a)):(Ha=r,r=a),r}function Bc(){var r,e,n,o;return r=Ha,(e=Xc())===a&&(e=Zc()),e!==a&&_c()!==a?((n=function(){var r;return":="===t.substr(Ha,2)?(r=":=",Ha+=2):(r=a,0===Wa&&za(na)),r}())===a&&(n=bc()),n!==a&&_c()!==a&&(o=Gc())!==a?(r,r=e={type:"assign",left:e,symbol:n,right:o}):(Ha=r,r=a)):(Ha=r,r=a),r}function Gc(){var r;return(r=Lu())===a&&(r=function(){var t,r,e,n,o;t=Ha,(r=Xc())!==a&&_c()!==a&&(e=qu())!==a&&_c()!==a&&(n=Xc())!==a&&_c()!==a&&(o=Fu())!==a?(t,t=r={type:"join",ltable:r,rtable:n,op:e,on:o}):(Ha=t,t=a);return t}())===a&&(r=Fc())===a&&(r=function(){var r,e;r=Ha,function(){var r;return 91===t.charCodeAt(Ha)?(r="[",Ha++):(r=a,0===Wa&&za(wa)),r}()!==a&&_c()!==a&&(e=Wc())!==a&&_c()!==a&&function(){var r;return 93===t.charCodeAt(Ha)?(r="]",Ha++):(r=a,0===Wa&&za(La)),r}()!==a?(r,r={type:"array",value:e}):(Ha=r,r=a);return r}()),r}function Fc(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Hc())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=mi())!==a&&(i=_c())!==a&&(s=Hc())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=mi())!==a&&(i=_c())!==a&&(s=Hc())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,t=r=Qt(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function Hc(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Yc())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=ji())!==a&&(i=_c())!==a&&(s=Yc())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=ji())!==a&&(i=_c())!==a&&(s=Yc())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,t=r=Qt(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function Yc(){var t,r,e;return(t=Hi())===a&&(t=Xc())===a&&(t=$c())===a&&(t=_i())===a&&(t=Ha,Cc()!==a&&_c()!==a&&(r=Fc())!==a&&_c()!==a&&Tc()!==a?(t,(e=r).parentheses=!0,t=e):(Ha=t,t=a)),t}function Qc(){var t,r,e,n,o,u,i;return t=Ha,(r=Ri())!==a?(e=Ha,(n=_c())!==a&&(o=jc())!==a&&(u=_c())!==a&&(i=Ri())!==a?e=n=[n,o,u,i]:(Ha=e,e=a),e===a&&(e=null),e!==a?(t,t=r=function(t,r){let e=t;return null!==r&&(e=`${t}.${r[3]}`),e}(r,e)):(Ha=t,t=a)):(Ha=t,t=a),t}function $c(){var t,r,e;return t=Ha,(r=Qc())!==a&&_c()!==a&&Cc()!==a&&_c()!==a?((e=Wc())===a&&(e=null),e!==a&&_c()!==a&&Tc()!==a?(t,t=r={type:"function",name:r,args:{type:"expr_list",value:e}}):(Ha=t,t=a)):(Ha=t,t=a),t===a&&(t=Ha,(r=Qc())!==a&&(t,r=function(t){return{type:"function",name:t,args:null}}(r)),t=r),t}function Wc(){var t,r,e,n,o,u,i,s;if(t=Ha,(r=Yc())!==a){for(e=[],n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Yc())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);n!==a;)e.push(n),n=Ha,(o=_c())!==a&&(u=wc())!==a&&(i=_c())!==a&&(s=Yc())!==a?n=o=[o,u,i,s]:(Ha=n,n=a);e!==a?(t,t=r=nl(r,e)):(Ha=t,t=a)}else Ha=t,t=a;return t}function Xc(){var t,r,e,n,o;return t=Ha,(r=fc())!==a&&(e=Zc())!==a?(t,n=r,o=e,t=r={type:"var",...o,prefix:n}):(Ha=t,t=a),t}function Zc(){var r,e,n,o,u;return r=Ha,(e=Ri())!==a&&(n=function(){var r,e,n,o,u;r=Ha,e=[],n=Ha,46===t.charCodeAt(Ha)?(o=".",Ha++):(o=a,0===Wa&&za(ge));o!==a&&(u=Ri())!==a?n=o=[o,u]:(Ha=n,n=a);for(;n!==a;)e.push(n),n=Ha,46===t.charCodeAt(Ha)?(o=".",Ha++):(o=a,0===Wa&&za(ge)),o!==a&&(u=Ri())!==a?n=o=[o,u]:(Ha=n,n=a);e!==a&&(r,e=function(t){const r=[];for(let e=0;e<t.length;e++)r.push(t[e][1]);return r}(e));return r=e}())!==a?(r,o=e,u=n,sl.push(o),r=e={type:"var",name:o,members:u,prefix:null}):(Ha=r,r=a),r===a&&(r=Ha,(e=Zi())!==a&&(r,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),r=e),r}function Kc(){var r;return(r=function(){var r,e,n,o;r=Ha,(e=Qs())===a&&(e=$s());if(e!==a)if(_c()!==a)if(Cc()!==a)if(_c()!==a){if(n=[],Re.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(Ie)),o!==a)for(;o!==a;)n.push(o),Re.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(Ie));else n=a;n!==a&&(o=_c())!==a&&Tc()!==a?(r,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},r=e):(Ha=r,r=a)}else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;r===a&&(r=Ha,(e=Qs())!==a&&(r,e=function(t){return{dataType:t}}(e)),(r=e)===a&&(r=Ha,(e=$s())!==a&&(r,e=Fa(e)),(r=e)===a&&(r=Ha,(e=function(){var r,e,n,o;return r=Ha,"string"===t.substr(Ha,6).toLowerCase()?(e=t.substr(Ha,6),Ha+=6):(e=a,0===Wa&&za(bo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="STRING"):(Ha=r,r=a)):(Ha=r,r=a),r}())!==a&&(r,e=function(t){return{dataType:t}}(e)),r=e)));return r}())===a&&(r=function(){var r,e,n,o,u,i,s,c,l,f,p,b;r=Ha,(e=Ws())===a&&(e=Xs())===a&&(e=Ks())===a&&(e=Js())===a&&(e=zs())===a&&(e=tc())===a&&(e=rc())===a&&(e=ec())===a&&(e=nc());if(e!==a)if((n=_c())!==a)if((o=Cc())!==a)if((u=_c())!==a){if(i=[],Re.test(t.charAt(Ha))?(s=t.charAt(Ha),Ha++):(s=a,0===Wa&&za(Ie)),s!==a)for(;s!==a;)i.push(s),Re.test(t.charAt(Ha))?(s=t.charAt(Ha),Ha++):(s=a,0===Wa&&za(Ie));else i=a;if(i!==a)if((s=_c())!==a){if(c=Ha,(l=wc())!==a)if((f=_c())!==a){if(p=[],Re.test(t.charAt(Ha))?(b=t.charAt(Ha),Ha++):(b=a,0===Wa&&za(Ie)),b!==a)for(;b!==a;)p.push(b),Re.test(t.charAt(Ha))?(b=t.charAt(Ha),Ha++):(b=a,0===Wa&&za(Ie));else p=a;p!==a?c=l=[l,f,p]:(Ha=c,c=a)}else Ha=c,c=a;else Ha=c,c=a;c===a&&(c=null),c!==a&&(l=_c())!==a&&(f=Tc())!==a&&(p=_c())!==a?((b=Jc())===a&&(b=null),b!==a?(r,v=c,h=b,e={dataType:e,length:parseInt(i.join(""),10),scale:v&&parseInt(v[2].join(""),10),parentheses:!0,suffix:h},r=e):(Ha=r,r=a)):(Ha=r,r=a)}else Ha=r,r=a;else Ha=r,r=a}else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;var v,h;if(r===a){if(r=Ha,(e=Ws())===a&&(e=Xs())===a&&(e=Ks())===a&&(e=Js())===a&&(e=zs())===a&&(e=tc())===a&&(e=rc())===a&&(e=ec())===a&&(e=nc()),e!==a){if(n=[],Re.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(Ie)),o!==a)for(;o!==a;)n.push(o),Re.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(Ie));else n=a;n!==a&&(o=_c())!==a?((u=Jc())===a&&(u=null),u!==a?(r,e=function(t,r,e){return{dataType:t,length:parseInt(r.join(""),10),suffix:e}}(e,n,u),r=e):(Ha=r,r=a)):(Ha=r,r=a)}else Ha=r,r=a;r===a&&(r=Ha,(e=Ws())===a&&(e=Xs())===a&&(e=Ks())===a&&(e=Js())===a&&(e=zs())===a&&(e=tc())===a&&(e=rc())===a&&(e=ec())===a&&(e=nc()),e!==a&&(n=_c())!==a?((o=Jc())===a&&(o=null),o!==a&&(u=_c())!==a?(r,e=function(t,r){return{dataType:t,suffix:r}}(e,o),r=e):(Ha=r,r=a)):(Ha=r,r=a))}return r}())===a&&(r=function(){var r,e,n,o;r=Ha,(e=oc())===a&&(e=ac())===a&&(e=uc())===a&&(e=ic());if(e!==a)if(_c()!==a)if(Cc()!==a)if(_c()!==a){if(n=[],Re.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(Ie)),o!==a)for(;o!==a;)n.push(o),Re.test(t.charAt(Ha))?(o=t.charAt(Ha),Ha++):(o=a,0===Wa&&za(Ie));else n=a;n!==a&&(o=_c())!==a&&Tc()!==a?(r,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},r=e):(Ha=r,r=a)}else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;else Ha=r,r=a;r===a&&(r=Ha,(e=oc())===a&&(e=ac())===a&&(e=uc())===a&&(e=ic()),e!==a&&(r,e=Fa(e)),r=e);return r}())===a&&(r=function(){var r,e;r=Ha,(e=function(){var r,e,n,o;return r=Ha,"json"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(wo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="JSON"):(Ha=r,r=a)):(Ha=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Ha,"jsonb"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(Lo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="JSONB"):(Ha=r,r=a)):(Ha=r,r=a),r}());e!==a&&(r,e=Fa(e));return r=e}())===a&&(r=function(){var r,e;r=Ha,(e=function(){var r,e,n,o;return r=Ha,"geometry"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(Co)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="GEOMETRY"):(Ha=r,r=a)):(Ha=r,r=a),r}())!==a&&(r,e={dataType:e});return r=e}())===a&&(r=function(){var r,e;r=Ha,(e=function(){var r,e,n,o;return r=Ha,"tinytext"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(Ao)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="TINYTEXT"):(Ha=r,r=a)):(Ha=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Ha,"text"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(go)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="TEXT"):(Ha=r,r=a)):(Ha=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Ha,"mediumtext"===t.substr(Ha,10).toLowerCase()?(e=t.substr(Ha,10),Ha+=10):(e=a,0===Wa&&za(Ro)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="MEDIUMTEXT"):(Ha=r,r=a)):(Ha=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Ha,"longtext"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(Io)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="LONGTEXT"):(Ha=r,r=a)):(Ha=r,r=a),r}());e!==a&&(r,e={dataType:e});return r=e}())===a&&(r=function(){var r,e;r=Ha,(e=function(){var r,e,n,o;return r=Ha,"uuid"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(Do)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="UUID"):(Ha=r,r=a)):(Ha=r,r=a),r}())!==a&&(r,e={dataType:e});return r=e}())===a&&(r=function(){var r,e;r=Ha,(e=function(){var r,e,n,o;return r=Ha,"bool"===t.substr(Ha,4).toLowerCase()?(e=t.substr(Ha,4),Ha+=4):(e=a,0===Wa&&za(co)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="BOOL"):(Ha=r,r=a)):(Ha=r,r=a),r}())===a&&(e=function(){var r,e,n,o;return r=Ha,"boolean"===t.substr(Ha,7).toLowerCase()?(e=t.substr(Ha,7),Ha+=7):(e=a,0===Wa&&za(lo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="BOOLEAN"):(Ha=r,r=a)):(Ha=r,r=a),r}());e!==a&&(r,e={dataType:e});return r=e}())===a&&(r=function(){var r,e,n;r=Ha,(e=function(){var r,e,n,o;return r=Ha,"array"===t.substr(Ha,5).toLowerCase()?(e=t.substr(Ha,5),Ha+=5):(e=a,0===Wa&&za(qo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="ARRAY"):(Ha=r,r=a)):(Ha=r,r=a),r}())!==a&&Sc()!==a&&(n=Kc())!==a&&Ac()!==a?(r,r=e={dataType:e,subType:n}):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e,n;r=Ha,(e=function(){var r,e,n,o;return r=Ha,"map"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(Bo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="MAP"):(Ha=r,r=a)):(Ha=r,r=a),r}())!==a&&Sc()!==a&&Kc()!==a&&wc()!==a&&(n=Kc())!==a&&Ac()!==a?(r,r=e={dataType:e,subType:n}):(Ha=r,r=a);return r}())===a&&(r=function(){var r,e;r=Ha,(e=function(){var r,e,n,o;return r=Ha,"row"===t.substr(Ha,3).toLowerCase()?(e=t.substr(Ha,3),Ha+=3):(e=a,0===Wa&&za(ut)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="ROW"):(Ha=r,r=a)):(Ha=r,r=a),r}())!==a&&(r,e={dataType:e});return r=e}()),r}function Jc(){var r,e,n;return r=Ha,(e=Zs())===a&&(e=null),e!==a&&_c()!==a?((n=function(){var r,e,n,o;return r=Ha,"zerofill"===t.substr(Ha,8).toLowerCase()?(e=t.substr(Ha,8),Ha+=8):(e=a,0===Wa&&za(Eo)),e!==a?(n=Ha,Wa++,o=Ii(),Wa--,o===a?n=void 0:(Ha=n,n=a),n!==a?(r,r=e="ZEROFILL"):(Ha=r,r=a)):(Ha=r,r=a),r}())===a&&(n=null),n!==a?(r,r=e=function(t,r){const e=[];return t&&e.push(t),r&&e.push(r),e}(e,n)):(Ha=r,r=a)):(Ha=r,r=a),r}const zc={ABS:!0,ALL:!0,ALLOCATE:!0,ALLOW:!0,ALTER:!0,AND:!0,ANY:!0,ARE:!0,ARRAY:!0,ARRAY_MAX_CARDINALITY:!0,AS:!0,ASENSITIVE:!0,ASYMMETRIC:!0,AT:!0,ATOMIC:!0,AUTHORIZATION:!0,AVG:!0,BEGIN:!0,BEGIN_FRAME:!0,BEGIN_PARTITION:!0,BETWEEN:!0,BIGINT:!0,BINARY:!0,BIT:!0,BLOB:!0,BOOLEAN:!0,BOTH:!0,BY:!0,CALL:!0,CALLED:!0,CARDINALITY:!0,CASCADED:!0,CASE:!0,CAST:!0,CEIL:!0,CEILING:!0,CHAR:!0,CHARACTER:!0,CHARACTER_LENGTH:!0,CHAR_LENGTH:!0,CHECK:!0,CLASSIFIER:!0,CLOB:!0,CLOSE:!0,COALESCE:!0,COLLATE:!0,COLLECT:!0,COLUMN:!0,COMMIT:!0,CONDITION:!0,CONNECT:!0,CONSTRAINT:!0,CONTAINS:!0,CONVERT:!0,CORR:!0,CORRESPONDING:!0,COUNT:!0,COVAR_POP:!0,COVAR_SAMP:!0,CREATE:!0,CROSS:!0,CUBE:!0,CUME_DIST:!0,CURRENT:!0,CURRENT_CATALOG:!0,CURRENT_DATE:!0,CURRENT_DEFAULT_TRANSFORM_GROUP:!0,CURRENT_PATH:!0,CURRENT_ROLE:!0,CURRENT_ROW:!0,CURRENT_SCHEMA:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_TRANSFORM_GROUP_FOR_TYPE:!0,CURRENT_USER:!0,CURSOR:!0,CYCLE:!0,DATE:!0,DAY:!0,DEALLOCATE:!0,DEC:!0,DECIMAL:!0,DECLARE:!0,DEFAULT:!0,DEFINE:!0,DELETE:!0,DENSE_RANK:!0,DEREF:!0,DESCRIBE:!0,DETERMINISTIC:!0,DISALLOW:!0,DISCONNECT:!0,DISTINCT:!0,DOUBLE:!0,DROP:!0,DYNAMIC:!0,EACH:!0,ELEMENT:!0,ELSE:!0,EMPTY:!0,END:!0,"END-EXEC":!0,END_FRAME:!0,END_PARTITION:!0,EQUALS:!0,ESCAPE:!0,EVERY:!0,EXCEPT:!0,EXEC:!0,EXECUTE:!0,EXISTS:!0,EXP:!0,EXPLAIN:!0,EXTEND:!0,EXTERNAL:!0,EXTRACT:!0,FALSE:!0,FETCH:!0,FILTER:!0,FIRST_VALUE:!0,FLOAT:!0,FLOOR:!0,FOR:!0,FOREIGN:!0,FRAME_ROW:!0,FREE:!0,FROM:!0,FULL:!0,FUNCTION:!0,FUSION:!0,GET:!0,GLOBAL:!0,GRANT:!0,GROUP:!0,GROUPING:!0,GROUPS:!0,HAVING:!0,HOLD:!0,HOUR:!0,IDENTITY:!0,IMPORT:!0,IN:!0,INDICATOR:!0,INITIAL:!0,INNER:!0,INOUT:!0,INSENSITIVE:!0,INSERT:!0,INT:!0,INTEGER:!0,INTERSECT:!0,INTERSECTION:!0,INTERVAL:!0,INTO:!0,IS:!0,JOIN:!0,JSON_ARRAY:!0,JSON_ARRAYAGG:!0,JSON_EXISTS:!0,JSON_OBJECT:!0,JSON_OBJECTAGG:!0,JSON_QUERY:!0,JSON_VALUE:!0,LAG:!0,LANGUAGE:!0,LARGE:!0,LAST_VALUE:!0,LATERAL:!0,LEAD:!0,LEADING:!0,LEFT:!0,LIKE:!0,LIKE_REGEX:!0,LIMIT:!0,LN:!0,LOCAL:!0,LOCALTIME:!0,LOCALTIMESTAMP:!0,LOWER:!0,MATCH:!0,MATCHES:!0,MATCH_NUMBER:!0,MATCH_RECOGNIZE:!0,MAX:!0,MEASURES:!0,MEMBER:!0,MERGE:!0,METHOD:!0,MIN:!0,MINUS:!0,MINUTE:!0,MOD:!0,MODIFIES:!0,MODULE:!0,MONTH:!0,MULTISET:!0,NATIONAL:!0,NATURAL:!0,NCHAR:!0,NCLOB:!0,NEW:!0,NEXT:!0,NO:!0,NONE:!0,NORMALIZE:!0,NOT:!0,NTH_VALUE:!0,NTILE:!0,NULL:!0,NULLIF:!0,NUMERIC:!0,OCCURRENCES_REGEX:!0,OCTET_LENGTH:!0,OF:!0,OFFSET:!0,OLD:!0,OMIT:!0,ON:!0,ONE:!0,ONLY:!0,OPEN:!0,OR:!0,ORDER:!0,OUT:!0,OUTER:!0,OVER:!0,OVERLAPS:!0,OVERLAY:!0,PARAMETER:!0,PARTITION:!0,PATTERN:!0,PER:!0,PERCENT:!0,PERCENTILE_CONT:!0,PERCENTILE_DISC:!0,PERCENT_RANK:!0,PERIOD:!0,PERMUTE:!0,PORTION:!0,POSITION:!0,POSITION_REGEX:!0,POWER:!0,PRECEDES:!0,PRECISION:!0,PREPARE:!0,PREV:!0,PRIMARY:!0,PROCEDURE:!0,RANGE:!0,RANK:!0,READS:!0,REAL:!0,RECURSIVE:!0,REF:!0,REFERENCES:!0,REFERENCING:!0,REGR_AVGX:!0,REGR_AVGY:!0,REGR_COUNT:!0,REGR_INTERCEPT:!0,REGR_R2:!0,REGR_SLOPE:!0,REGR_SXX:!0,REGR_SXY:!0,REGR_SYY:!0,RELEASE:!0,RESET:!0,RESULT:!0,RETURN:!0,RETURNS:!0,REVOKE:!0,RIGHT:!0,ROLLBACK:!0,ROLLUP:!0,ROW:!0,ROWS:!0,ROW_NUMBER:!0,RUNNING:!0,SAVEPOINT:!0,SCOPE:!0,SCROLL:!0,SEARCH:!0,SECOND:!0,SEEK:!0,SELECT:!0,SENSITIVE:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SIMILAR:!0,SIMILAR:!0,SKIP:!0,SMALLINT:!0,SOME:!0,SPECIFIC:!0,SPECIFICTYPE:!0,SQL:!0,SQLEXCEPTION:!0,SQLSTATE:!0,SQLWARNING:!0,SQRT:!0,START:!0,STATIC:!0,STDDEV_POP:!0,STDDEV_SAMP:!0,STREAM:!0,SUBMULTISET:!0,SUBSET:!0,SUBSTRING:!0,SUBSTRING_REGEX:!0,SUCCEEDS:!0,SUM:!0,SYMMETRIC:!0,SYSTEM:!0,SYSTEM_TIME:!0,SYSTEM_USER:!0,TABLE:!0,TABLESAMPLE:!0,THEN:!0,TO:!0,TIME:!0,TIMESTAMP:!0,TIMEZONE_HOUR:!0,TIMEZONE_MINUTE:!0,TINYINT:!0,TO:!0,TRAILING:!0,TRANSLATE:!0,TRANSLATE_REGEX:!0,TRANSLATION:!0,TREAT:!0,TRIGGER:!0,TRIM:!0,TRIM_ARRAY:!0,TRUE:!0,TRUNCATE:!0,UESCAPE:!0,UNION:!0,UNIQUE:!0,UNKNOWN:!0,UNNEST:!0,UPDATE:!0,UPPER:!0,UPSERT:!0,USER:!0,USING:!0,VALUE:!0,VALUES:!0,VALUE_OF:!0,VARBINARY:!0,VARCHAR:!0,VARYING:!0,VAR_POP:!0,VAR_SAMP:!0,VERSIONING:!0,WHEN:!0,WHENEVER:!0,WHERE:!0,WIDTH_BUCKET:!0,WINDOW:!0,WITH:!0,WITHIN:!0,WITHOUT:!0,YEAR:!0};function tl(t,r){return{type:"unary_expr",operator:t,expr:r}}function rl(t,r,e){return{type:"binary_expr",operator:t,left:r,right:e}}function el(t){const r=n(Number.MAX_SAFE_INTEGER);return!(n(t)<r)}function nl(t,r,e=3){const n=[t];for(let t=0;t<r.length;t++)delete r[t][e].tableList,delete r[t][e].columnList,n.push(r[t][e]);return n}function ol(t,r){let e=t;for(let t=0;t<r.length;t++)e=rl(r[t][1],e,r[t][3]);return e}function al(t){const r=fl[t];return r||(t||null)}function ul(t){const r=new Set;for(let e of t.keys()){const t=e.split("::");if(!t){r.add(e);break}t&&t[1]&&(t[1]=al(t[1])),r.add(t.join("::"))}return Array.from(r)}function il(t){return"string"==typeof t?{type:"same",value:t}:t}let sl=[];const cl=new Set,ll=new Set,fl={};if((e=i())!==a&&Ha===t.length)return e;throw e!==a&&Ha<t.length&&za({type:"end"}),tu($a,Qa<t.length?t.charAt(Qa):null,Qa<t.length?Ja(Qa,Qa+1):Ja(Qa,Qa))}}},function(t,r,e){t.exports=e(26)},function(t,r,e){"use strict";e.r(r),function(t){var n=e(23);e.d(r,"Parser",(function(){return n.a}));var o=e(0);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e.d(r,"util",(function(){return o})),"object"===("undefined"==typeof self?"undefined":a(self))&&self&&(self.NodeSQLParser={Parser:n.a,util:o}),void 0===t&&"object"===("undefined"==typeof window?"undefined":a(window))&&window&&(window.global=window),"object"===(void 0===t?"undefined":a(t))&&t&&t.window&&(t.window.NodeSQLParser={Parser:n.a,util:o})}.call(this,e(27))},function(t,r){var e;e=function(){return this}();try{e=e||new Function("return this")()}catch(t){"object"==typeof window&&(e=window)}t.exports=e},function(t,r,e){(function(t){var n,o=function(t){"use strict";var r=1e7,e=9007199254740992,n=f(e),a="function"==typeof BigInt;function u(t,r,e,n){return void 0===t?u[0]:void 0!==r&&(10!=+r||e)?q(t,r,e,n):Y(t)}function i(t,r){this.value=t,this.sign=r,this.isSmall=!1}function s(t){this.value=t,this.sign=t<0,this.isSmall=!0}function c(t){this.value=t}function l(t){return-e<t&&t<e}function f(t){return t<1e7?[t]:t<1e14?[t%1e7,Math.floor(t/1e7)]:[t%1e7,Math.floor(t/1e7)%1e7,Math.floor(t/1e14)]}function p(t){b(t);var e=t.length;if(e<4&&g(t,n)<0)switch(e){case 0:return 0;case 1:return t[0];case 2:return t[0]+t[1]*r;default:return t[0]+(t[1]+t[2]*r)*r}return t}function b(t){for(var r=t.length;0===t[--r];);t.length=r+1}function v(t){for(var r=new Array(t),e=-1;++e<t;)r[e]=0;return r}function h(t){return t>0?Math.floor(t):Math.ceil(t)}function d(t,e){var n,o,a=t.length,u=e.length,i=new Array(a),s=0,c=r;for(o=0;o<u;o++)s=(n=t[o]+e[o]+s)>=c?1:0,i[o]=n-s*c;for(;o<a;)s=(n=t[o]+s)===c?1:0,i[o++]=n-s*c;return s>0&&i.push(s),i}function y(t,r){return t.length>=r.length?d(t,r):d(r,t)}function O(t,e){var n,o,a=t.length,u=new Array(a),i=r;for(o=0;o<a;o++)n=t[o]-i+e,e=Math.floor(n/i),u[o]=n-e*i,e+=1;for(;e>0;)u[o++]=e%i,e=Math.floor(e/i);return u}function m(t,r){var e,n,o=t.length,a=r.length,u=new Array(o),i=0;for(e=0;e<a;e++)(n=t[e]-i-r[e])<0?(n+=1e7,i=1):i=0,u[e]=n;for(e=a;e<o;e++){if(!((n=t[e]-i)<0)){u[e++]=n;break}n+=1e7,u[e]=n}for(;e<o;e++)u[e]=t[e];return b(u),u}function E(t,r,e){var n,o,a=t.length,u=new Array(a),c=-r;for(n=0;n<a;n++)o=t[n]+c,c=Math.floor(o/1e7),o%=1e7,u[n]=o<0?o+1e7:o;return"number"==typeof(u=p(u))?(e&&(u=-u),new s(u)):new i(u,e)}function j(t,r){var e,n,o,a,u=t.length,i=r.length,s=v(u+i);for(o=0;o<u;++o){a=t[o];for(var c=0;c<i;++c)e=a*r[c]+s[o+c],n=Math.floor(e/1e7),s[o+c]=e-1e7*n,s[o+c+1]+=n}return b(s),s}function w(t,e){var n,o,a=t.length,u=new Array(a),i=r,s=0;for(o=0;o<a;o++)n=t[o]*e+s,s=Math.floor(n/i),u[o]=n-s*i;for(;s>0;)u[o++]=s%i,s=Math.floor(s/i);return u}function L(t,r){for(var e=[];r-- >0;)e.push(0);return e.concat(t)}function C(t,e,n){return new i(t<r?w(e,t):j(e,f(t)),n)}function T(t){var r,e,n,o,a=t.length,u=v(a+a);for(n=0;n<a;n++){e=0-(o=t[n])*o;for(var i=n;i<a;i++)r=o*t[i]*2+u[n+i]+e,e=Math.floor(r/1e7),u[n+i]=r-1e7*e;u[n+a]=e}return b(u),u}function S(t,r){var e,n,o,a,u=t.length,i=v(u);for(o=0,e=u-1;e>=0;--e)o=(a=1e7*o+t[e])-(n=h(a/r))*r,i[e]=0|n;return[i,0|o]}function A(t,e){var n,o=Y(e);if(a)return[new c(t.value/o.value),new c(t.value%o.value)];var l,d=t.value,y=o.value;if(0===y)throw new Error("Cannot divide by zero");if(t.isSmall)return o.isSmall?[new s(h(d/y)),new s(d%y)]:[u[0],t];if(o.isSmall){if(1===y)return[t,u[0]];if(-1==y)return[t.negate(),u[0]];var O=Math.abs(y);if(O<r){l=p((n=S(d,O))[0]);var E=n[1];return t.sign&&(E=-E),"number"==typeof l?(t.sign!==o.sign&&(l=-l),[new s(l),new s(E)]):[new i(l,t.sign!==o.sign),new s(E)]}y=f(O)}var j=g(d,y);if(-1===j)return[u[0],t];if(0===j)return[u[t.sign===o.sign?1:-1],u[0]];l=(n=d.length+y.length<=200?function(t,e){var n,o,a,u,i,s,c,l=t.length,f=e.length,b=r,h=v(e.length),d=e[f-1],y=Math.ceil(b/(2*d)),O=w(t,y),m=w(e,y);for(O.length<=l&&O.push(0),m.push(0),d=m[f-1],o=l-f;o>=0;o--){for(n=b-1,O[o+f]!==d&&(n=Math.floor((O[o+f]*b+O[o+f-1])/d)),a=0,u=0,s=m.length,i=0;i<s;i++)a+=n*m[i],c=Math.floor(a/b),u+=O[o+i]-(a-c*b),a=c,u<0?(O[o+i]=u+b,u=-1):(O[o+i]=u,u=0);for(;0!==u;){for(n-=1,a=0,i=0;i<s;i++)(a+=O[o+i]-b+m[i])<0?(O[o+i]=a+b,a=0):(O[o+i]=a,a=1);u+=a}h[o]=n}return O=S(O,y)[0],[p(h),p(O)]}(d,y):function(t,r){for(var e,n,o,a,u,i=t.length,s=r.length,c=[],l=[];i;)if(l.unshift(t[--i]),b(l),g(l,r)<0)c.push(0);else{o=1e7*l[(n=l.length)-1]+l[n-2],a=1e7*r[s-1]+r[s-2],n>s&&(o=1e7*(o+1)),e=Math.ceil(o/a);do{if(g(u=w(r,e),l)<=0)break;e--}while(e);c.push(e),l=m(l,u)}return c.reverse(),[p(c),p(l)]}(d,y))[0];var L=t.sign!==o.sign,C=n[1],T=t.sign;return"number"==typeof l?(L&&(l=-l),l=new s(l)):l=new i(l,L),"number"==typeof C?(T&&(C=-C),C=new s(C)):C=new i(C,T),[l,C]}function g(t,r){if(t.length!==r.length)return t.length>r.length?1:-1;for(var e=t.length-1;e>=0;e--)if(t[e]!==r[e])return t[e]>r[e]?1:-1;return 0}function R(t){var r=t.abs();return!r.isUnit()&&(!!(r.equals(2)||r.equals(3)||r.equals(5))||!(r.isEven()||r.isDivisibleBy(3)||r.isDivisibleBy(5))&&(!!r.lesser(49)||void 0))}function I(t,r){for(var e,n,a,u=t.prev(),i=u,s=0;i.isEven();)i=i.divide(2),s++;t:for(n=0;n<r.length;n++)if(!t.lesser(r[n])&&!(a=o(r[n]).modPow(i,t)).isUnit()&&!a.equals(u)){for(e=s-1;0!=e;e--){if((a=a.square().mod(t)).isUnit())return!1;if(a.equals(u))continue t}return!1}return!0}i.prototype=Object.create(u.prototype),s.prototype=Object.create(u.prototype),c.prototype=Object.create(u.prototype),i.prototype.add=function(t){var r=Y(t);if(this.sign!==r.sign)return this.subtract(r.negate());var e=this.value,n=r.value;return r.isSmall?new i(O(e,Math.abs(n)),this.sign):new i(y(e,n),this.sign)},i.prototype.plus=i.prototype.add,s.prototype.add=function(t){var r=Y(t),e=this.value;if(e<0!==r.sign)return this.subtract(r.negate());var n=r.value;if(r.isSmall){if(l(e+n))return new s(e+n);n=f(Math.abs(n))}return new i(O(n,Math.abs(e)),e<0)},s.prototype.plus=s.prototype.add,c.prototype.add=function(t){return new c(this.value+Y(t).value)},c.prototype.plus=c.prototype.add,i.prototype.subtract=function(t){var r=Y(t);if(this.sign!==r.sign)return this.add(r.negate());var e=this.value,n=r.value;return r.isSmall?E(e,Math.abs(n),this.sign):function(t,r,e){var n;return g(t,r)>=0?n=m(t,r):(n=m(r,t),e=!e),"number"==typeof(n=p(n))?(e&&(n=-n),new s(n)):new i(n,e)}(e,n,this.sign)},i.prototype.minus=i.prototype.subtract,s.prototype.subtract=function(t){var r=Y(t),e=this.value;if(e<0!==r.sign)return this.add(r.negate());var n=r.value;return r.isSmall?new s(e-n):E(n,Math.abs(e),e>=0)},s.prototype.minus=s.prototype.subtract,c.prototype.subtract=function(t){return new c(this.value-Y(t).value)},c.prototype.minus=c.prototype.subtract,i.prototype.negate=function(){return new i(this.value,!this.sign)},s.prototype.negate=function(){var t=this.sign,r=new s(-this.value);return r.sign=!t,r},c.prototype.negate=function(){return new c(-this.value)},i.prototype.abs=function(){return new i(this.value,!1)},s.prototype.abs=function(){return new s(Math.abs(this.value))},c.prototype.abs=function(){return new c(this.value>=0?this.value:-this.value)},i.prototype.multiply=function(t){var e,n,o,a=Y(t),s=this.value,c=a.value,l=this.sign!==a.sign;if(a.isSmall){if(0===c)return u[0];if(1===c)return this;if(-1===c)return this.negate();if((e=Math.abs(c))<r)return new i(w(s,e),l);c=f(e)}return n=s.length,o=c.length,new i(-.012*n-.012*o+15e-6*n*o>0?function t(r,e){var n=Math.max(r.length,e.length);if(n<=30)return j(r,e);n=Math.ceil(n/2);var o=r.slice(n),a=r.slice(0,n),u=e.slice(n),i=e.slice(0,n),s=t(a,i),c=t(o,u),l=t(y(a,o),y(i,u)),f=y(y(s,L(m(m(l,s),c),n)),L(c,2*n));return b(f),f}(s,c):j(s,c),l)},i.prototype.times=i.prototype.multiply,s.prototype._multiplyBySmall=function(t){return l(t.value*this.value)?new s(t.value*this.value):C(Math.abs(t.value),f(Math.abs(this.value)),this.sign!==t.sign)},i.prototype._multiplyBySmall=function(t){return 0===t.value?u[0]:1===t.value?this:-1===t.value?this.negate():C(Math.abs(t.value),this.value,this.sign!==t.sign)},s.prototype.multiply=function(t){return Y(t)._multiplyBySmall(this)},s.prototype.times=s.prototype.multiply,c.prototype.multiply=function(t){return new c(this.value*Y(t).value)},c.prototype.times=c.prototype.multiply,i.prototype.square=function(){return new i(T(this.value),!1)},s.prototype.square=function(){var t=this.value*this.value;return l(t)?new s(t):new i(T(f(Math.abs(this.value))),!1)},c.prototype.square=function(t){return new c(this.value*this.value)},i.prototype.divmod=function(t){var r=A(this,t);return{quotient:r[0],remainder:r[1]}},c.prototype.divmod=s.prototype.divmod=i.prototype.divmod,i.prototype.divide=function(t){return A(this,t)[0]},c.prototype.over=c.prototype.divide=function(t){return new c(this.value/Y(t).value)},s.prototype.over=s.prototype.divide=i.prototype.over=i.prototype.divide,i.prototype.mod=function(t){return A(this,t)[1]},c.prototype.mod=c.prototype.remainder=function(t){return new c(this.value%Y(t).value)},s.prototype.remainder=s.prototype.mod=i.prototype.remainder=i.prototype.mod,i.prototype.pow=function(t){var r,e,n,o=Y(t),a=this.value,i=o.value;if(0===i)return u[1];if(0===a)return u[0];if(1===a)return u[1];if(-1===a)return o.isEven()?u[1]:u[-1];if(o.sign)return u[0];if(!o.isSmall)throw new Error("The exponent "+o.toString()+" is too large.");if(this.isSmall&&l(r=Math.pow(a,i)))return new s(h(r));for(e=this,n=u[1];!0&i&&(n=n.times(e),--i),0!==i;)i/=2,e=e.square();return n},s.prototype.pow=i.prototype.pow,c.prototype.pow=function(t){var r=Y(t),e=this.value,n=r.value,o=BigInt(0),a=BigInt(1),i=BigInt(2);if(n===o)return u[1];if(e===o)return u[0];if(e===a)return u[1];if(e===BigInt(-1))return r.isEven()?u[1]:u[-1];if(r.isNegative())return new c(o);for(var s=this,l=u[1];(n&a)===a&&(l=l.times(s),--n),n!==o;)n/=i,s=s.square();return l},i.prototype.modPow=function(t,r){if(t=Y(t),(r=Y(r)).isZero())throw new Error("Cannot take modPow with modulus 0");var e=u[1],n=this.mod(r);for(t.isNegative()&&(t=t.multiply(u[-1]),n=n.modInv(r));t.isPositive();){if(n.isZero())return u[0];t.isOdd()&&(e=e.multiply(n).mod(r)),t=t.divide(2),n=n.square().mod(r)}return e},c.prototype.modPow=s.prototype.modPow=i.prototype.modPow,i.prototype.compareAbs=function(t){var r=Y(t),e=this.value,n=r.value;return r.isSmall?1:g(e,n)},s.prototype.compareAbs=function(t){var r=Y(t),e=Math.abs(this.value),n=r.value;return r.isSmall?e===(n=Math.abs(n))?0:e>n?1:-1:-1},c.prototype.compareAbs=function(t){var r=this.value,e=Y(t).value;return(r=r>=0?r:-r)===(e=e>=0?e:-e)?0:r>e?1:-1},i.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=Y(t),e=this.value,n=r.value;return this.sign!==r.sign?r.sign?1:-1:r.isSmall?this.sign?-1:1:g(e,n)*(this.sign?-1:1)},i.prototype.compareTo=i.prototype.compare,s.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=Y(t),e=this.value,n=r.value;return r.isSmall?e==n?0:e>n?1:-1:e<0!==r.sign?e<0?-1:1:e<0?1:-1},s.prototype.compareTo=s.prototype.compare,c.prototype.compare=function(t){if(t===1/0)return-1;if(t===-1/0)return 1;var r=this.value,e=Y(t).value;return r===e?0:r>e?1:-1},c.prototype.compareTo=c.prototype.compare,i.prototype.equals=function(t){return 0===this.compare(t)},c.prototype.eq=c.prototype.equals=s.prototype.eq=s.prototype.equals=i.prototype.eq=i.prototype.equals,i.prototype.notEquals=function(t){return 0!==this.compare(t)},c.prototype.neq=c.prototype.notEquals=s.prototype.neq=s.prototype.notEquals=i.prototype.neq=i.prototype.notEquals,i.prototype.greater=function(t){return this.compare(t)>0},c.prototype.gt=c.prototype.greater=s.prototype.gt=s.prototype.greater=i.prototype.gt=i.prototype.greater,i.prototype.lesser=function(t){return this.compare(t)<0},c.prototype.lt=c.prototype.lesser=s.prototype.lt=s.prototype.lesser=i.prototype.lt=i.prototype.lesser,i.prototype.greaterOrEquals=function(t){return this.compare(t)>=0},c.prototype.geq=c.prototype.greaterOrEquals=s.prototype.geq=s.prototype.greaterOrEquals=i.prototype.geq=i.prototype.greaterOrEquals,i.prototype.lesserOrEquals=function(t){return this.compare(t)<=0},c.prototype.leq=c.prototype.lesserOrEquals=s.prototype.leq=s.prototype.lesserOrEquals=i.prototype.leq=i.prototype.lesserOrEquals,i.prototype.isEven=function(){return 0==(1&this.value[0])},s.prototype.isEven=function(){return 0==(1&this.value)},c.prototype.isEven=function(){return(this.value&BigInt(1))===BigInt(0)},i.prototype.isOdd=function(){return 1==(1&this.value[0])},s.prototype.isOdd=function(){return 1==(1&this.value)},c.prototype.isOdd=function(){return(this.value&BigInt(1))===BigInt(1)},i.prototype.isPositive=function(){return!this.sign},s.prototype.isPositive=function(){return this.value>0},c.prototype.isPositive=s.prototype.isPositive,i.prototype.isNegative=function(){return this.sign},s.prototype.isNegative=function(){return this.value<0},c.prototype.isNegative=s.prototype.isNegative,i.prototype.isUnit=function(){return!1},s.prototype.isUnit=function(){return 1===Math.abs(this.value)},c.prototype.isUnit=function(){return this.abs().value===BigInt(1)},i.prototype.isZero=function(){return!1},s.prototype.isZero=function(){return 0===this.value},c.prototype.isZero=function(){return this.value===BigInt(0)},i.prototype.isDivisibleBy=function(t){var r=Y(t);return!r.isZero()&&(!!r.isUnit()||(0===r.compareAbs(2)?this.isEven():this.mod(r).isZero()))},c.prototype.isDivisibleBy=s.prototype.isDivisibleBy=i.prototype.isDivisibleBy,i.prototype.isPrime=function(t){var r=R(this);if(void 0!==r)return r;var e=this.abs(),n=e.bitLength();if(n<=64)return I(e,[2,3,5,7,11,13,17,19,23,29,31,37]);for(var a=Math.log(2)*n.toJSNumber(),u=Math.ceil(!0===t?2*Math.pow(a,2):a),i=[],s=0;s<u;s++)i.push(o(s+2));return I(e,i)},c.prototype.isPrime=s.prototype.isPrime=i.prototype.isPrime,i.prototype.isProbablePrime=function(t,r){var e=R(this);if(void 0!==e)return e;for(var n=this.abs(),a=void 0===t?5:t,u=[],i=0;i<a;i++)u.push(o.randBetween(2,n.minus(2),r));return I(n,u)},c.prototype.isProbablePrime=s.prototype.isProbablePrime=i.prototype.isProbablePrime,i.prototype.modInv=function(t){for(var r,e,n,a=o.zero,u=o.one,i=Y(t),s=this.abs();!s.isZero();)r=i.divide(s),e=a,n=i,a=u,i=s,u=e.subtract(r.multiply(u)),s=n.subtract(r.multiply(s));if(!i.isUnit())throw new Error(this.toString()+" and "+t.toString()+" are not co-prime");return-1===a.compare(0)&&(a=a.add(t)),this.isNegative()?a.negate():a},c.prototype.modInv=s.prototype.modInv=i.prototype.modInv,i.prototype.next=function(){var t=this.value;return this.sign?E(t,1,this.sign):new i(O(t,1),this.sign)},s.prototype.next=function(){var t=this.value;return t+1<e?new s(t+1):new i(n,!1)},c.prototype.next=function(){return new c(this.value+BigInt(1))},i.prototype.prev=function(){var t=this.value;return this.sign?new i(O(t,1),!0):E(t,1,this.sign)},s.prototype.prev=function(){var t=this.value;return t-1>-e?new s(t-1):new i(n,!0)},c.prototype.prev=function(){return new c(this.value-BigInt(1))};for(var N=[1];2*N[N.length-1]<=r;)N.push(2*N[N.length-1]);var U=N.length,_=N[U-1];function x(t){return Math.abs(t)<=r}function k(t,r,e){r=Y(r);for(var n=t.isNegative(),a=r.isNegative(),u=n?t.not():t,i=a?r.not():r,s=0,c=0,l=null,f=null,p=[];!u.isZero()||!i.isZero();)s=(l=A(u,_))[1].toJSNumber(),n&&(s=_-1-s),c=(f=A(i,_))[1].toJSNumber(),a&&(c=_-1-c),u=l[0],i=f[0],p.push(e(s,c));for(var b=0!==e(n?1:0,a?1:0)?o(-1):o(0),v=p.length-1;v>=0;v-=1)b=b.multiply(_).add(o(p[v]));return b}i.prototype.shiftLeft=function(t){var r=Y(t).toJSNumber();if(!x(r))throw new Error(String(r)+" is too large for shifting.");if(r<0)return this.shiftRight(-r);var e=this;if(e.isZero())return e;for(;r>=U;)e=e.multiply(_),r-=U-1;return e.multiply(N[r])},c.prototype.shiftLeft=s.prototype.shiftLeft=i.prototype.shiftLeft,i.prototype.shiftRight=function(t){var r,e=Y(t).toJSNumber();if(!x(e))throw new Error(String(e)+" is too large for shifting.");if(e<0)return this.shiftLeft(-e);for(var n=this;e>=U;){if(n.isZero()||n.isNegative()&&n.isUnit())return n;n=(r=A(n,_))[1].isNegative()?r[0].prev():r[0],e-=U-1}return(r=A(n,N[e]))[1].isNegative()?r[0].prev():r[0]},c.prototype.shiftRight=s.prototype.shiftRight=i.prototype.shiftRight,i.prototype.not=function(){return this.negate().prev()},c.prototype.not=s.prototype.not=i.prototype.not,i.prototype.and=function(t){return k(this,t,(function(t,r){return t&r}))},c.prototype.and=s.prototype.and=i.prototype.and,i.prototype.or=function(t){return k(this,t,(function(t,r){return t|r}))},c.prototype.or=s.prototype.or=i.prototype.or,i.prototype.xor=function(t){return k(this,t,(function(t,r){return t^r}))},c.prototype.xor=s.prototype.xor=i.prototype.xor;function M(t){var e=t.value,n="number"==typeof e?e|1<<30:"bigint"==typeof e?e|BigInt(1<<30):e[0]+e[1]*r|1073758208;return n&-n}function V(t,r){return t=Y(t),r=Y(r),t.greater(r)?t:r}function P(t,r){return t=Y(t),r=Y(r),t.lesser(r)?t:r}function D(t,r){if(t=Y(t).abs(),r=Y(r).abs(),t.equals(r))return t;if(t.isZero())return r;if(r.isZero())return t;for(var e,n,o=u[1];t.isEven()&&r.isEven();)e=P(M(t),M(r)),t=t.divide(e),r=r.divide(e),o=o.multiply(e);for(;t.isEven();)t=t.divide(M(t));do{for(;r.isEven();)r=r.divide(M(r));t.greater(r)&&(n=r,r=t,t=n),r=r.subtract(t)}while(!r.isZero());return o.isUnit()?t:t.multiply(o)}i.prototype.bitLength=function(){var t=this;return t.compareTo(o(0))<0&&(t=t.negate().subtract(o(1))),0===t.compareTo(o(0))?o(0):o(function t(r,e){if(e.compareTo(r)<=0){var n=t(r,e.square(e)),a=n.p,u=n.e,i=a.multiply(e);return i.compareTo(r)<=0?{p:i,e:2*u+1}:{p:a,e:2*u}}return{p:o(1),e:0}}(t,o(2)).e).add(o(1))},c.prototype.bitLength=s.prototype.bitLength=i.prototype.bitLength;var q=function(t,r,e,n){e=e||"0123456789abcdefghijklmnopqrstuvwxyz",t=String(t),n||(t=t.toLowerCase(),e=e.toLowerCase());var o,a=t.length,u=Math.abs(r),i={};for(o=0;o<e.length;o++)i[e[o]]=o;for(o=0;o<a;o++){if("-"!==(l=t[o])&&(l in i&&i[l]>=u)){if("1"===l&&1===u)continue;throw new Error(l+" is not a valid digit in base "+r+".")}}r=Y(r);var s=[],c="-"===t[0];for(o=c?1:0;o<t.length;o++){var l;if((l=t[o])in i)s.push(Y(i[l]));else{if("<"!==l)throw new Error(l+" is not a valid character");var f=o;do{o++}while(">"!==t[o]&&o<t.length);s.push(Y(t.slice(f+1,o)))}}return B(s,r,c)};function B(t,r,e){var n,o=u[0],a=u[1];for(n=t.length-1;n>=0;n--)o=o.add(t[n].times(a)),a=a.times(r);return e?o.negate():o}function G(t,r){if((r=o(r)).isZero()){if(t.isZero())return{value:[0],isNegative:!1};throw new Error("Cannot convert nonzero numbers to base 0.")}if(r.equals(-1)){if(t.isZero())return{value:[0],isNegative:!1};if(t.isNegative())return{value:[].concat.apply([],Array.apply(null,Array(-t.toJSNumber())).map(Array.prototype.valueOf,[1,0])),isNegative:!1};var e=Array.apply(null,Array(t.toJSNumber()-1)).map(Array.prototype.valueOf,[0,1]);return e.unshift([1]),{value:[].concat.apply([],e),isNegative:!1}}var n=!1;if(t.isNegative()&&r.isPositive()&&(n=!0,t=t.abs()),r.isUnit())return t.isZero()?{value:[0],isNegative:!1}:{value:Array.apply(null,Array(t.toJSNumber())).map(Number.prototype.valueOf,1),isNegative:n};for(var a,u=[],i=t;i.isNegative()||i.compareAbs(r)>=0;){a=i.divmod(r),i=a.quotient;var s=a.remainder;s.isNegative()&&(s=r.minus(s).abs(),i=i.next()),u.push(s.toJSNumber())}return u.push(i.toJSNumber()),{value:u.reverse(),isNegative:n}}function F(t,r,e){var n=G(t,r);return(n.isNegative?"-":"")+n.value.map((function(t){return function(t,r){return t<(r=r||"0123456789abcdefghijklmnopqrstuvwxyz").length?r[t]:"<"+t+">"}(t,e)})).join("")}function H(t){if(l(+t)){var r=+t;if(r===h(r))return a?new c(BigInt(r)):new s(r);throw new Error("Invalid integer: "+t)}var e="-"===t[0];e&&(t=t.slice(1));var n=t.split(/e/i);if(n.length>2)throw new Error("Invalid integer: "+n.join("e"));if(2===n.length){var o=n[1];if("+"===o[0]&&(o=o.slice(1)),(o=+o)!==h(o)||!l(o))throw new Error("Invalid integer: "+o+" is not a valid exponent.");var u=n[0],f=u.indexOf(".");if(f>=0&&(o-=u.length-f-1,u=u.slice(0,f)+u.slice(f+1)),o<0)throw new Error("Cannot include negative exponent part for integers");t=u+=new Array(o+1).join("0")}if(!/^([0-9][0-9]*)$/.test(t))throw new Error("Invalid integer: "+t);if(a)return new c(BigInt(e?"-"+t:t));for(var p=[],v=t.length,d=v-7;v>0;)p.push(+t.slice(d,v)),(d-=7)<0&&(d=0),v-=7;return b(p),new i(p,e)}function Y(t){return"number"==typeof t?function(t){if(a)return new c(BigInt(t));if(l(t)){if(t!==h(t))throw new Error(t+" is not an integer.");return new s(t)}return H(t.toString())}(t):"string"==typeof t?H(t):"bigint"==typeof t?new c(t):t}i.prototype.toArray=function(t){return G(this,t)},s.prototype.toArray=function(t){return G(this,t)},c.prototype.toArray=function(t){return G(this,t)},i.prototype.toString=function(t,r){if(void 0===t&&(t=10),10!==t||r)return F(this,t,r);for(var e,n=this.value,o=n.length,a=String(n[--o]);--o>=0;)e=String(n[o]),a+="0000000".slice(e.length)+e;return(this.sign?"-":"")+a},s.prototype.toString=function(t,r){return void 0===t&&(t=10),10!=t||r?F(this,t,r):String(this.value)},c.prototype.toString=s.prototype.toString,c.prototype.toJSON=i.prototype.toJSON=s.prototype.toJSON=function(){return this.toString()},i.prototype.valueOf=function(){return parseInt(this.toString(),10)},i.prototype.toJSNumber=i.prototype.valueOf,s.prototype.valueOf=function(){return this.value},s.prototype.toJSNumber=s.prototype.valueOf,c.prototype.valueOf=c.prototype.toJSNumber=function(){return parseInt(this.toString(),10)};for(var Q=0;Q<1e3;Q++)u[Q]=Y(Q),Q>0&&(u[-Q]=Y(-Q));return u.one=u[1],u.zero=u[0],u.minusOne=u[-1],u.max=V,u.min=P,u.gcd=D,u.lcm=function(t,r){return t=Y(t).abs(),r=Y(r).abs(),t.divide(D(t,r)).multiply(r)},u.isInstance=function(t){return t instanceof i||t instanceof s||t instanceof c},u.randBetween=function(t,e,n){t=Y(t),e=Y(e);var o=n||Math.random,a=P(t,e),i=V(t,e).subtract(a).add(1);if(i.isSmall)return a.add(Math.floor(o()*i));for(var s=G(i,r).value,c=[],l=!0,f=0;f<s.length;f++){var p=l?s[f]+(f+1<s.length?s[f+1]/r:0):r,b=h(o()*p);c.push(b),b<s[f]&&(l=!1)}return a.add(u.fromArray(c,r,!1))},u.fromArray=function(t,r,e){return B(t.map(Y),Y(r||10),e)},u}();t.hasOwnProperty("exports")&&(t.exports=o),void 0===(n=function(){return o}.call(r,e,r,t))||(t.exports=n)}).call(this,e(29)(t))},function(t,r){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}}])}));
//# sourceMappingURL=flinksql.umd.js.map