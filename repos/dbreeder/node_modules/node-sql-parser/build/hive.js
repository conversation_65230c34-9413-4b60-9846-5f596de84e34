!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?u(r.parts[t][0])+"-"+u(r.parts[t][1]):u(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function u(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,u=new Array(r.length);for(t=0;t<r.length;t++)u[t]=(o=r[t],e[o.type](o));if(u.sort(),u.length>0){for(t=1,n=1;t<u.length;t++)u[t-1]!==u[t]&&(u[n]=u[t],n++);u.length=n}switch(u.length){case 1:return u[0];case 2:return u[0]+" or "+u[1];default:return u.slice(0,-1).join(", ")+", or "+u[u.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,u={},a={start:ko},s=ko,i=function(r,t){return di(r,t,1)},c=To("IF",!0),l=function(r,t){return di(r,t)},f=To("AUTO_INCREMENT",!0),p=To("UNIQUE",!0),v=To("KEY",!0),b=To("PRIMARY",!0),d=To("COLUMN_FORMAT",!0),y=To("FIXED",!0),h=To("DYNAMIC",!0),m=To("DEFAULT",!0),w=To("STORAGE",!0),L=To("DISK",!0),C=To("MEMORY",!0),E=To("ALGORITHM",!0),A=To("INSTANT",!0),g=To("INPLACE",!0),S=To("COPY",!0),T=To("LOCK",!0),j=To("NONE",!0),_=To("SHARED",!0),x=To("EXCLUSIVE",!0),I=To("PRIMARY KEY",!0),N=To("FOREIGN KEY",!0),k=To("MATCH FULL",!0),R=To("MATCH PARTIAL",!0),O=To("MATCH SIMPLE",!0),U=To("RESTRICT",!0),M=To("CASCADE",!0),D=To("SET NULL",!0),P=To("NO ACTION",!0),F=To("SET DEFAULT",!0),$=To("CHARACTER",!0),H=To("SET",!0),W=To("CHARSET",!0),G=To("COLLATE",!0),q=To("AVG_ROW_LENGTH",!0),B=To("KEY_BLOCK_SIZE",!0),Y=To("MAX_ROWS",!0),V=To("MIN_ROWS",!0),Q=To("STATS_SAMPLE_PAGES",!0),X=To("CONNECTION",!0),K=To("COMPRESSION",!0),z=To("'",!1),Z=To("ZLIB",!0),J=To("LZ4",!0),rr=To("ENGINE",!0),tr=To("READ",!0),er=To("LOCAL",!0),nr=To("LOW_PRIORITY",!0),or=To("WRITE",!0),ur=To("(",!1),ar=To(")",!1),sr=To(".",!1),ir=To("BTREE",!0),cr=To("HASH",!0),lr=To("WITH",!0),fr=To("PARSER",!0),pr=To("VISIBLE",!0),vr=To("INVISIBLE",!0),br=function(r,t){return t.unshift(r),t.forEach(r=>{const{table:t,as:e}=r;Ei[t]=t,e&&(Ei[e]=t),function(r){const t=mi(r);r.clear(),t.forEach(t=>r.add(t))}(Ci)}),t},dr=To("FOLLOWING",!0),yr=To("PRECEDING",!0),hr=To("CURRENT",!0),mr=To("ROW",!0),wr=To("UNBOUNDED",!0),Lr=To("=",!1),Cr=function(r,t){return yi(r,t)},Er=To("!",!1),Ar=function(r){return r[0]+" "+r[2]},gr=To(">=",!1),Sr=To(">",!1),Tr=To("<=",!1),jr=To("<>",!1),_r=To("<",!1),xr=To("==",!1),Ir=To("!=",!1),Nr=function(r,t){return{op:r,right:t}},kr=To("+",!1),Rr=To("-",!1),Or=To("*",!1),Ur=To("/",!1),Mr=To("%",!1),Dr=function(r){return!0===fi[r.toUpperCase()]},Pr=To('"',!1),Fr=/^[^"]/,$r=jo(['"'],!0,!1),Hr=function(r){return r.join("")},Wr=/^[^']/,Gr=jo(["'"],!0,!1),qr=To("`",!1),Br=/^[^`]/,Yr=jo(["`"],!0,!1),Vr=function(r,t){return r+t.join("")},Qr=/^[A-Za-z_]/,Xr=jo([["A","Z"],["a","z"],"_"],!1,!1),Kr=/^[A-Za-z0-9_]/,zr=jo([["A","Z"],["a","z"],["0","9"],"_"],!1,!1),Zr=/^[A-Za-z0-9_:]/,Jr=jo([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),rt=To(":",!1),tt=To("OVER",!0),et=To("AT TIME ZONE",!0),nt=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}},ot=/^[^"\\\0-\x1F\x7F]/,ut=jo(['"',"\\",["\0",""],""],!0,!1),at=/^[^'\\]/,st=jo(["'","\\"],!0,!1),it=To("\\'",!1),ct=To('\\"',!1),lt=To("\\\\",!1),ft=To("\\/",!1),pt=To("\\b",!1),vt=To("\\f",!1),bt=To("\\n",!1),dt=To("\\r",!1),yt=To("\\t",!1),ht=To("\\u",!1),mt=To("\\",!1),wt=To("''",!1),Lt=To('""',!1),Ct=To("``",!1),Et=/^[\n\r]/,At=jo(["\n","\r"],!1,!1),gt=/^[0-9]/,St=jo([["0","9"]],!1,!1),Tt=/^[0-9a-fA-F]/,jt=jo([["0","9"],["a","f"],["A","F"]],!1,!1),_t=/^[eE]/,xt=jo(["e","E"],!1,!1),It=/^[+\-]/,Nt=jo(["+","-"],!1,!1),kt=To("NULL",!0),Rt=To("NOT NULL",!0),Ot=To("TRUE",!0),Ut=To("TO",!0),Mt=To("FALSE",!0),Dt=(To("SHOW",!0),To("DROP",!0)),Pt=To("USE",!0),Ft=To("ALTER",!0),$t=To("SELECT",!0),Ht=To("UPDATE",!0),Wt=To("CREATE",!0),Gt=To("TEMPORARY",!0),qt=To("DELETE",!0),Bt=To("INSERT",!0),Yt=To("RECURSIVE",!1),Vt=To("REPLACE",!0),Qt=To("RENAME",!0),Xt=To("IGNORE",!0),Kt=(To("EXPLAIN",!0),To("PARTITION",!0)),zt=To("INTO",!0),Zt=To("OVERWRITE",!0),Jt=To("FROM",!0),re=To("UNLOCK",!0),te=To("AS",!0),ee=To("TABLE",!0),ne=To("TABLES",!0),oe=To("DATABASE",!0),ue=To("SCHEME",!0),ae=To("ON",!0),se=To("LEFT",!0),ie=To("RIGHT",!0),ce=To("FULL",!0),le=To("CROSS",!0),fe=To("INNER",!0),pe=To("JOIN",!0),ve=To("OUTER",!0),be=To("UNION",!0),de=To("VALUES",!0),ye=To("USING",!0),he=To("WHERE",!0),me=To("GROUP",!0),we=To("BY",!0),Le=To("ORDER",!0),Ce=To("HAVING",!0),Ee=To("LIMIT",!0),Ae=To("OFFSET",!0),ge=To("ASC",!0),Se=To("DESC",!0),Te=To("ALL",!0),je=To("DISTINCT",!0),_e=To("BETWEEN",!0),xe=To("IN",!0),Ie=To("IS",!0),Ne=To("LIKE",!0),ke=To("RLIKE",!0),Re=To("EXISTS",!0),Oe=To("NOT",!0),Ue=To("AND",!0),Me=To("OR",!0),De=To("COUNT",!0),Pe=To("MAX",!0),Fe=To("MIN",!0),$e=To("SUM",!0),He=To("AVG",!0),We=To("CALL",!0),Ge=To("CASE",!0),qe=To("WHEN",!0),Be=To("THEN",!0),Ye=To("ELSE",!0),Ve=To("END",!0),Qe=To("CAST",!0),Xe=To("CHAR",!0),Ke=To("VARCHAR",!0),ze=To("NUMERIC",!0),Ze=To("DECIMAL",!0),Je=To("SIGNED",!0),rn=To("STRING",!0),tn=To("UNSIGNED",!0),en=To("INT",!0),nn=To("ZEROFILL",!0),on=To("INTEGER",!0),un=To("JSON",!0),an=To("SMALLINT",!0),sn=To("TINYINT",!0),cn=To("TINYTEXT",!0),ln=To("TEXT",!0),fn=To("MEDIUMTEXT",!0),pn=To("LONGTEXT",!0),vn=To("BIGINT",!0),bn=To("FLOAT",!0),dn=To("DOUBLE",!0),yn=To("DATE",!0),hn=To("DATETIME",!0),mn=To("ROWS",!0),wn=To("TIME",!0),Ln=To("TIMESTAMP",!0),Cn=To("TRUNCATE",!0),En=To("USER",!0),An=To("CURRENT_DATE",!0),gn=(To("ADDDATE",!0),To("INTERVAL",!0)),Sn=To("YEAR",!0),Tn=To("MONTH",!0),jn=To("DAY",!0),_n=To("HOUR",!0),xn=To("MINUTE",!0),In=To("SECOND",!0),Nn=To("CURRENT_TIME",!0),kn=To("CURRENT_TIMESTAMP",!0),Rn=To("CURRENT_USER",!0),On=To("SESSION_USER",!0),Un=To("SYSTEM_USER",!0),Mn=To("GLOBAL",!0),Dn=To("SESSION",!0),Pn=To("PERSIST",!0),Fn=To("PERSIST_ONLY",!0),$n=To("@",!1),Hn=To("@@",!1),Wn=To("$",!1),Gn=To("return",!0),qn=To(":=",!1),Bn=To("DUAL",!0),Yn=To("ADD",!0),Vn=To("COLUMN",!0),Qn=To("INDEX",!0),Xn=To("FULLTEXT",!0),Kn=To("SPATIAL",!0),zn=To("COMMENT",!0),Zn=To("CONSTRAINT",!0),Jn=To("REFERENCES",!0),ro=To("SQL_CALC_FOUND_ROWS",!0),to=To("SQL_CACHE",!0),eo=To("SQL_NO_CACHE",!0),no=To("SQL_SMALL_RESULT",!0),oo=To("SQL_BIG_RESULT",!0),uo=To("SQL_BUFFER_RESULT",!0),ao=To(",",!1),so=To("[",!1),io=To("]",!1),co=To(";",!1),lo=To("||",!1),fo=To("&&",!1),po=To("/*",!1),vo=To("*/",!1),bo=To("--",!1),yo=To("#",!1),ho={type:"any"},mo=/^[ \t\n\r]/,wo=jo([" ","\t","\n","\r"],!1,!1),Lo=function(r){return{dataType:r}},Co=0,Eo=[{line:1,column:1}],Ao=0,go=[],So=0;if("startRule"in t){if(!(t.startRule in a))throw new Error("Can't start parsing from rule \""+t.startRule+'".');s=a[t.startRule]}function To(r,t){return{type:"literal",text:r,ignoreCase:t}}function jo(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function _o(t){var e,n=Eo[t];if(n)return n;for(e=t-1;!Eo[e];)e--;for(n={line:(n=Eo[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Eo[t]=n,n}function xo(r,t){var e=_o(r),n=_o(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function Io(r){Co<Ao||(Co>Ao&&(Ao=Co,go=[]),go.push(r))}function No(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function ko(){var r,t;return r=Co,Bs()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Oo())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Gs())!==u&&(s=Bs())!==u&&(i=Oo())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Gs())!==u&&(s=Bs())!==u&&(i=Oo())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(Li),columnList:mi(Ci),ast:n}}(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())!==u?(r,r=t):(Co=r,r=u),r}function Ro(){var t;return(t=function(){var r,t,e,n,o,a;r=Co,(t=Na())!==u&&Bs()!==u&&(e=Wa())!==u&&Bs()!==u&&(n=bu())!==u?(r,s=t,c=e,(l=n)&&l.forEach(r=>Li.add(`${s}::${r.db}::${r.table}`)),t={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:s.toLowerCase(),keyword:c.toLowerCase(),name:l}},r=t):(Co=r,r=u);var s,c,l;r===u&&(r=Co,(t=Na())!==u&&Bs()!==u&&(e=Rs())!==u&&Bs()!==u&&(n=ea())!==u&&Bs()!==u&&qa()!==u&&Bs()!==u&&(o=mu())!==u&&Bs()!==u?((a=function(){var r,t,e,n,o,a;r=Co,(t=Wo())===u&&(t=Go());if(t!==u){for(e=[],n=Co,(o=Bs())!==u?((a=Wo())===u&&(a=Go()),a!==u?n=o=[o,a]:(Co=n,n=u)):(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u?((a=Wo())===u&&(a=Go()),a!==u?n=o=[o,a]:(Co=n,n=u)):(Co=n,n=u);e!==u?(r,t=i(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())===u&&(a=null),a!==u&&Bs()!==u?(r,t=function(r,t,e,n,o){return{tableList:Array.from(Li),columnList:mi(Ci),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),name:e,table:n,options:o}}}(t,e,n,o,a),r=t):(Co=r,r=u)):(Co=r,r=u));return r}())===u&&(t=function(){var t;(t=function(){var t,e,n,o,a,s,i,c,f,p;t=Co,(e=Ra())!==u&&Bs()!==u?((n=Oa())===u&&(n=null),n!==u&&Bs()!==u&&Wa()!==u&&Bs()!==u?((o=Do())===u&&(o=null),o!==u&&Bs()!==u&&(a=bu())!==u&&Bs()!==u&&(s=function(){var r,t,e,n,o,a,s,i,c;if(r=Co,(t=Fs())!==u)if(Bs()!==u)if((e=Po())!==u){for(n=[],o=Co,(a=Bs())!==u&&(s=Ds())!==u&&(i=Bs())!==u&&(c=Po())!==u?o=a=[a,s,i,c]:(Co=o,o=u);o!==u;)n.push(o),o=Co,(a=Bs())!==u&&(s=Ds())!==u&&(i=Bs())!==u&&(c=Po())!==u?o=a=[a,s,i,c]:(Co=o,o=u);n!==u&&(o=Bs())!==u&&(a=$s())!==u?(r,t=l(e,n),r=t):(Co=r,r=u)}else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;return r}())!==u&&Bs()!==u?((i=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Ko())!==u){for(e=[],n=Co,(o=Bs())!==u?((a=Ds())===u&&(a=null),a!==u&&(s=Bs())!==u&&(i=Ko())!==u?n=o=[o,a,s,i]:(Co=n,n=u)):(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u?((a=Ds())===u&&(a=null),a!==u&&(s=Bs())!==u&&(i=Ko())!==u?n=o=[o,a,s,i]:(Co=n,n=u)):(Co=n,n=u);e!==u?(r,t=di(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())===u&&(i=null),i!==u&&Bs()!==u?((c=function(){var t,e,n,o;t=Co,"ignore"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Xt));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(c=Ma()),c===u&&(c=null),c!==u&&Bs()!==u?((f=Ha())===u&&(f=null),f!==u&&Bs()!==u?((p=Mo())===u&&(p=null),p!==u?(t,v=e,b=n,d=o,h=s,m=i,w=c,L=f,C=p,(y=a)&&y.forEach(r=>Li.add(`create::${r.db}::${r.table}`)),e={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:v[0].toLowerCase(),keyword:"table",temporary:b&&b[0].toLowerCase(),if_not_exists:d,table:y,ignore_replace:w&&w[0].toLowerCase(),as:L&&L[0].toLowerCase(),query_expr:C&&C.ast,create_definitions:h,table_options:m}},t=e):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u);var v,b,d,y,h,m,w,L,C;t===u&&(t=Co,(e=Ra())!==u&&Bs()!==u?((n=Oa())===u&&(n=null),n!==u&&Bs()!==u&&Wa()!==u&&Bs()!==u?((o=Do())===u&&(o=null),o!==u&&Bs()!==u&&(a=bu())!==u&&Bs()!==u&&(s=function r(){var t,e;(t=function(){var r,t;r=Co,es()!==u&&Bs()!==u&&(t=bu())!==u?(r,r={type:"like",table:t}):(Co=r,r=u);return r}())===u&&(t=Co,Fs()!==u&&Bs()!==u&&(e=r())!==u&&Bs()!==u&&$s()!==u?(t,(n=e).parentheses=!0,t=n):(Co=t,t=u));var n;return t}())!==u?(t,e=function(r,t,e,n,o){return n&&n.forEach(r=>Li.add(`create::${r.db}::${r.table}`)),{tableList:Array.from(Li),columnList:mi(Ci),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(e,n,o,a,s),t=e):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u));return t}())===u&&(t=function(){var t,e,n,o,a,s;t=Co,(e=Ra())!==u&&Bs()!==u?((n=function(){var t,e,n,o;t=Co,"database"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(oe));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="DATABASE"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Co,"scheme"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(ue));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="SCHEME"):(Co=t,t=u)):(Co=t,t=u);return t}()),n!==u&&Bs()!==u?((o=Do())===u&&(o=null),o!==u&&Bs()!==u&&(a=ia())!==u&&Bs()!==u?((s=function(){var r,t,e,n,o,a;if(r=Co,(t=Xo())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Xo())!==u?n=o=[o,a]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Xo())!==u?n=o=[o,a]:(Co=n,n=u);e!==u?(r,t=i(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())===u&&(s=null),s!==u?(t,c=e,l=o,f=a,p=s,e={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:c[0].toLowerCase(),keyword:"database",if_not_exists:l,database:f,create_definitions:p}},t=e):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u);var c,l,f,p;return t}());return t}())===u&&(t=function(){var t,e,n,o;t=Co,(e=function(){var t,e,n,o;t=Co,"truncate"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(Cn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="TRUNCATE"):(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u?((n=Wa())===u&&(n=null),n!==u&&Bs()!==u&&(o=bu())!==u?(t,a=e,s=n,(i=o)&&i.forEach(r=>Li.add(`${a}::${r.db}::${r.table}`)),e={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:a.toLowerCase(),keyword:s&&s.toLowerCase()||"table",name:i}},t=e):(Co=t,t=u)):(Co=t,t=u);var a,s,i;return t}())===u&&(t=function(){var r,t,e;r=Co,(t=Da())!==u&&Bs()!==u&&Wa()!==u&&Bs()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=lu())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=lu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=lu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,t=l(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())!==u?(r,(n=e).forEach(r=>r.forEach(r=>r.table&&Li.add(`rename::${r.db}::${r.table}`))),t={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:"rename",table:n}},r=t):(Co=r,r=u);var n;return r}())===u&&(t=function(){var t,e,n;t=Co,(e=function(){var t,e,n,o;t=Co,"call"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(We));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="CALL"):(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u&&(n=ui())!==u?(t,o=n,e={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:"call",expr:o}},t=e):(Co=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n;t=Co,(e=function(){var t,e,n,o;t=Co,"use"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(Pt));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u&&(n=na())!==u?(t,o=n,Li.add(`use::${o}::null`),e={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:"use",db:o}},t=e):(Co=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n,o;t=Co,(e=function(){var t,e,n,o;t=Co,"alter"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(Ft));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u&&Wa()!==u&&Bs()!==u&&(n=bu())!==u&&Bs()!==u&&(o=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Ho())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=Ho())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=Ho())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,t=l(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())!==u?(t,s=o,(a=n)&&a.length>0&&a.forEach(r=>Li.add(`alter::${r.db}::${r.table}`)),e={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:"alter",table:a,expr:s}},t=e):(Co=t,t=u);var a,s;return t}())===u&&(t=function(){var t,e,n,o;t=Co,(e=$a())!==u&&Bs()!==u?((n=function(){var t,e,n,o;t=Co,"global"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Mn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="GLOBAL"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Co,"session"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(Dn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="SESSION"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Co,"local"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(er));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="LOCAL"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Co,"persist"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(Pn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="PERSIST"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Co,"persist_only"===r.substr(Co,12).toLowerCase()?(e=r.substr(Co,12),Co+=12):(e=u,0===So&&Io(Fn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="PERSIST_ONLY"):(Co=t,t=u)):(Co=t,t=u);return t}()),n===u&&(n=null),n!==u&&Bs()!==u&&(o=Js())!==u?(t,a=n,(s=o).keyword=a,e={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:"set",expr:s}},t=e):(Co=t,t=u)):(Co=t,t=u);var a,s;return t}())===u&&(t=function(){var t,e,n;t=Co,(e=function(){var t,e,n,o;t=Co,"lock"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(T));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u&&Ga()!==u&&Bs()!==u&&(n=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=zo())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=zo())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=zo())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,t=di(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())!==u?(t,o=n,e={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:"lock",keyword:"tables",tables:o}},t=e):(Co=t,t=u);var o;return t}())===u&&(t=function(){var t,e;t=Co,(e=function(){var t,e,n,o;t=Co,"unlock"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(re));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u&&Ga()!==u?(t,e={tableList:Array.from(Li),columnList:mi(Ci),ast:{type:"unlock",keyword:"tables"}},t=e):(Co=t,t=u);return t}()),t}function Oo(){var t;return(t=Mo())===u&&(t=function(){var r,t,e,n,o;r=Co,(t=ka())!==u&&Bs()!==u&&(e=bu())!==u&&Bs()!==u&&$a()!==u&&Bs()!==u&&(n=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=ku())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=ku())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=ku())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,t=l(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())!==u&&Bs()!==u?((o=Cu())===u&&(o=null),o!==u?(r,t=function(r,t,e){const n={};return r&&r.forEach(r=>{const{db:t,as:e,table:o,join:u}=r,a=u?"select":"update";t&&(n[o]=t),o&&Li.add(`${a}::${t}::${o}`)}),t&&t.forEach(r=>{if(r.table){const t=hi(r.table);Li.add(`update::${n[t]||null}::${t}`)}Ci.add(`update::${r.table}::${r.column}`)}),{tableList:Array.from(Li),columnList:mi(Ci),ast:{type:"update",table:r,set:t,where:e}}}(e,n,o),r=t):(Co=r,r=u)):(Co=r,r=u);return r}())===u&&(t=function(){var r,t,e,n,o,a,s,i;r=Co,(t=Uu())!==u&&Bs()!==u&&(e=Fa())!==u&&Bs()!==u?((n=Wa())===u&&(n=null),n!==u&&Bs()!==u&&(o=mu())!==u?((a=Ou())===u&&(a=null),a!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u&&(s=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=aa())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=aa())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=aa())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,t=l(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())!==u&&Bs()!==u&&$s()!==u&&Bs()!==u&&(i=Ru())!==u?(r,t=function(r,t,e,n,o,u,a){if(n&&(Li.add(`insert::${n.db}::${n.table}`),n.as=null),u){let r=n&&n.table||null;Array.isArray(a)&&a.forEach((r,t)=>{if(r.value.length!=u.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),u.forEach(t=>Ci.add(`insert::${r}::${t}`))}const s=e?" "+e.toLowerCase():"";return{tableList:Array.from(Li),columnList:mi(Ci),ast:{type:r,prefix:`${t.toLowerCase()}${s}`,table:[n],columns:u,values:a,partition:o}}}(t,e,n,o,a,s,i),r=t):(Co=r,r=u)):(Co=r,r=u)):(Co=r,r=u);return r}())===u&&(t=function(){var t,e,n,o,a,s,i;t=Co,(e=Uu())!==u&&Bs()!==u?((n=Fa())===u&&(n=function(){var t,e,n,o;t=Co,"overwrite"===r.substr(Co,9).toLowerCase()?(e=r.substr(Co,9),Co+=9):(e=u,0===So&&Io(Zt));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="OVERWRITE"):(Co=t,t=u)):(Co=t,t=u);return t}()),n!==u&&Bs()!==u?((o=Wa())===u&&(o=null),o!==u&&Bs()!==u&&(a=mu())!==u&&Bs()!==u?((s=Ou())===u&&(s=null),s!==u&&Bs()!==u&&(i=Ru())!==u?(t,e=function(r,t,e,n,o,u){n&&(Li.add(`insert::${n.db}::${n.table}`),Ci.add(`insert::${n.table}::(.*)`),n.as=null);const a=e?" "+e.toLowerCase():"";return{tableList:Array.from(Li),columnList:mi(Ci),ast:{type:r,prefix:`${t.toLowerCase()}${a}`,table:[n],columns:null,values:u,partition:o}}}(e,n,o,a,s,i),t=e):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var r,t,e,n,o;r=Co,(t=Ua())!==u&&Bs()!==u?((e=bu())===u&&(e=null),e!==u&&Bs()!==u&&(n=cu())!==u&&Bs()!==u?((o=Cu())===u&&(o=null),o!==u?(r,t=function(r,t,e){if(t&&t.forEach(r=>{const{db:t,as:e,table:n,join:o}=r,u=o?"select":"delete";n&&Li.add(`${u}::${t}::${n}`),o||Ci.add(`delete::${n}::(.*)`)}),null===r&&1===t.length){const e=t[0];r=[{db:e.db,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(Li),columnList:mi(Ci),ast:{type:"delete",table:r,from:t,where:e}}}(e,n,o),r=t):(Co=r,r=u)):(Co=r,r=u)):(Co=r,r=u);return r}())===u&&(t=Ro())===u&&(t=function(){var r,t;r=[],t=Zs();for(;t!==u;)r.push(t),t=Zs();return r}()),t}function Uo(){var t,e,n;return t=Co,function(){var t,e,n,o;t=Co,"union"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(be));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u?((e=za())===u&&(e=Za()),e===u&&(e=null),e!==u?(t,t=(n=e)?"union "+n.toLowerCase():"union"):(Co=t,t=u)):(Co=t,t=u),t}function Mo(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Zo())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Uo())!==u&&(s=Bs())!==u&&(i=Zo())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Uo())!==u&&(s=Bs())!==u&&(i=Zo())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u&&(n=Bs())!==u?((o=ju())===u&&(o=null),o!==u&&(a=Bs())!==u?((s=Nu())===u&&(s=null),s!==u?(r,r=t=function(r,t,e,n){t.forEach(r=>r.slice(1,1));let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&(r._limit=n),{tableList:Array.from(Li),columnList:mi(Ci),ast:r}}(t,e,o,s)):(Co=r,r=u)):(Co=r,r=u)):(Co=r,r=u)}else Co=r,r=u;return r}function Do(){var t,e;return t=Co,"if"===r.substr(Co,2).toLowerCase()?(e=r.substr(Co,2),Co+=2):(e=u,0===So&&Io(c)),e!==u&&Bs()!==u&&us()!==u&&Bs()!==u&&os()!==u?(t,t=e="IF NOT EXISTS"):(Co=t,t=u),t}function Po(){var t;return(t=$o())===u&&(t=qo())===u&&(t=Bo())===u&&(t=function(){var t;(t=function(){var t,e,n,o,a,s;t=Co,(e=Yo())===u&&(e=null);e!==u&&Bs()!==u?("primary key"===r.substr(Co,11).toLowerCase()?(n=r.substr(Co,11),Co+=11):(n=u,0===So&&Io(I)),n!==u&&Bs()!==u?((o=fu())===u&&(o=null),o!==u&&Bs()!==u&&(a=tu())!==u&&Bs()!==u?((s=pu())===u&&(s=null),s!==u?(t,c=n,l=o,f=a,p=s,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},t=e):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u);var i,c,l,f,p;return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c;t=Co,(e=Yo())===u&&(e=null);e!==u&&Bs()!==u&&(n=function(){var t,e,n,o;t=Co,"unique"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(p));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="UNIQUE"):(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u?((o=Rs())===u&&(o=Os()),o===u&&(o=null),o!==u&&Bs()!==u?((a=aa())===u&&(a=null),a!==u&&Bs()!==u?((s=fu())===u&&(s=null),s!==u&&Bs()!==u&&(i=tu())!==u&&Bs()!==u?((c=pu())===u&&(c=null),c!==u?(t,f=n,v=o,b=a,d=s,y=i,h=c,e={constraint:(l=e)&&l.constraint,definition:y,constraint_type:v&&`${f.toLowerCase()} ${v.toLowerCase()}`||f.toLowerCase(),keyword:l&&l.keyword,index_type:d,index:b,resource:"constraint",index_options:h},t=e):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u);var l,f,v,b,d,y,h;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=Co,(e=Yo())===u&&(e=null);e!==u&&Bs()!==u?("foreign key"===r.substr(Co,11).toLowerCase()?(n=r.substr(Co,11),Co+=11):(n=u,0===So&&Io(N)),n!==u&&Bs()!==u?((o=aa())===u&&(o=null),o!==u&&Bs()!==u&&(a=tu())!==u&&Bs()!==u?((s=Vo())===u&&(s=null),s!==u?(t,c=n,l=o,f=a,p=s,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},t=e):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u);var i,c,l,f,p;return t}());return t}()),t}function Fo(){var t,e,n,o;return t=Co,(e=function(){var t,e;t=Co,(e=function(){var t,e,n,o;t=Co,"not null"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(Rt));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(t,e={type:"not null",value:"not null"});return t=e}())===u&&(e=ha()),e!==u&&(t,(o=e)&&!o.value&&(o.value="null"),e={nullable:o}),(t=e)===u&&(t=Co,(e=function(){var r,t;r=Co,xa()!==u&&Bs()!==u?((t=ya())===u&&(t=Gu()),t!==u?(r,r={type:"default",value:t}):(Co=r,r=u)):(Co=r,r=u);return r}())!==u&&(t,e={default_val:e}),(t=e)===u&&(t=Co,"auto_increment"===r.substr(Co,14).toLowerCase()?(e=r.substr(Co,14),Co+=14):(e=u,0===So&&Io(f)),e!==u&&(t,e={auto_increment:e.toLowerCase()}),(t=e)===u&&(t=Co,"unique"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(p)),e!==u&&Bs()!==u?("key"===r.substr(Co,3).toLowerCase()?(n=r.substr(Co,3),Co+=3):(n=u,0===So&&Io(v)),n===u&&(n=null),n!==u?(t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,"primary"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(b)),e===u&&(e=null),e!==u&&Bs()!==u?("key"===r.substr(Co,3).toLowerCase()?(n=r.substr(Co,3),Co+=3):(n=u,0===So&&Io(v)),n!==u?(t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,(e=Qs())!==u&&(t,e={comment:e}),(t=e)===u&&(t=Co,(e=function(){var t,e;t=Co,function(){var t,e,n,o;t=Co,"collate"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(G));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="COLLATE"):(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u&&(e=ia())!==u?(t,t={type:"collate",value:e}):(Co=t,t=u);return t}())!==u&&(t,e={collate:e}),(t=e)===u&&(t=Co,(e=function(){var t,e,n;t=Co,"column_format"===r.substr(Co,13).toLowerCase()?(e=r.substr(Co,13),Co+=13):(e=u,0===So&&Io(d));e!==u&&Bs()!==u?("fixed"===r.substr(Co,5).toLowerCase()?(n=r.substr(Co,5),Co+=5):(n=u,0===So&&Io(y)),n===u&&("dynamic"===r.substr(Co,7).toLowerCase()?(n=r.substr(Co,7),Co+=7):(n=u,0===So&&Io(h)),n===u&&("default"===r.substr(Co,7).toLowerCase()?(n=r.substr(Co,7),Co+=7):(n=u,0===So&&Io(m)))),n!==u?(t,e={type:"column_format",value:n.toLowerCase()},t=e):(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(t,e={column_format:e}),(t=e)===u&&(t=Co,(e=function(){var t,e,n;t=Co,"storage"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(w));e!==u&&Bs()!==u?("disk"===r.substr(Co,4).toLowerCase()?(n=r.substr(Co,4),Co+=4):(n=u,0===So&&Io(L)),n===u&&("memory"===r.substr(Co,6).toLowerCase()?(n=r.substr(Co,6),Co+=6):(n=u,0===So&&Io(C))),n!==u?(t,e={type:"storage",value:n.toLowerCase()},t=e):(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(t,e={storage:e}),(t=e)===u&&(t=Co,(e=Vo())!==u&&(t,e={reference_definition:e}),t=e))))))))),t}function $o(){var r,t,e,n,o,a,s;return r=Co,(t=ea())!==u&&Bs()!==u&&(e=ci())!==u&&Bs()!==u?((n=function(){var r,t,e,n,o,a;if(r=Co,(t=Fo())!==u)if(Bs()!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Fo())!==u?n=o=[o,a]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Fo())!==u?n=o=[o,a]:(Co=n,n=u);e!==u?(r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(Co=r,r=u)}else Co=r,r=u;else Co=r,r=u;return r}())===u&&(n=null),n!==u?(r,o=t,a=e,s=n,Ci.add(`create::${o.table}::${o.column}`),r=t={column:o,definition:a,resource:"column",...s||{}}):(Co=r,r=u)):(Co=r,r=u),r}function Ho(){var r;return(r=function(){var r,t,e,n;r=Co,(t=Ns())!==u&&Bs()!==u?((e=ks())===u&&(e=null),e!==u&&Bs()!==u&&(n=$o())!==u?(r,o=e,a=n,t={action:"add",...a,keyword:o,resource:"column",type:"alter"},r=t):(Co=r,r=u)):(Co=r,r=u);var o,a;return r}())===u&&(r=function(){var r,t,e;r=Co,Na()!==u&&Bs()!==u?((t=ks())===u&&(t=null),t!==u&&Bs()!==u&&(e=ea())!==u?(r,r={action:"drop",column:e,keyword:t,resource:"column",type:"alter"}):(Co=r,r=u)):(Co=r,r=u);return r}())===u&&(r=function(){var r,t,e;r=Co,(t=Ns())!==u&&Bs()!==u&&(e=qo())!==u?(r,n=e,t={action:"add",type:"alter",...n},r=t):(Co=r,r=u);var n;return r}())===u&&(r=function(){var r,t,e;r=Co,(t=Ns())!==u&&Bs()!==u&&(e=Bo())!==u?(r,n=e,t={action:"add",type:"alter",...n},r=t):(Co=r,r=u);var n;return r}())===u&&(r=function(){var r,t,e,n;r=Co,(t=Da())!==u&&Bs()!==u?((e=Ia())===u&&(e=Ha()),e===u&&(e=null),e!==u&&Bs()!==u&&(n=na())!==u?(r,a=n,t={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:a},r=t):(Co=r,r=u)):(Co=r,r=u);var o,a;return r}())===u&&(r=Wo())===u&&(r=Go()),r}function Wo(){var t,e,n,o;return t=Co,"algorithm"===r.substr(Co,9).toLowerCase()?(e=r.substr(Co,9),Co+=9):(e=u,0===So&&Io(E)),e!==u&&Bs()!==u?((n=Is())===u&&(n=null),n!==u&&Bs()!==u?("default"===r.substr(Co,7).toLowerCase()?(o=r.substr(Co,7),Co+=7):(o=u,0===So&&Io(m)),o===u&&("instant"===r.substr(Co,7).toLowerCase()?(o=r.substr(Co,7),Co+=7):(o=u,0===So&&Io(A)),o===u&&("inplace"===r.substr(Co,7).toLowerCase()?(o=r.substr(Co,7),Co+=7):(o=u,0===So&&Io(g)),o===u&&("copy"===r.substr(Co,4).toLowerCase()?(o=r.substr(Co,4),Co+=4):(o=u,0===So&&Io(S))))),o!==u?(t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u),t}function Go(){var t,e,n,o;return t=Co,"lock"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(T)),e!==u&&Bs()!==u?((n=Is())===u&&(n=null),n!==u&&Bs()!==u?("default"===r.substr(Co,7).toLowerCase()?(o=r.substr(Co,7),Co+=7):(o=u,0===So&&Io(m)),o===u&&("none"===r.substr(Co,4).toLowerCase()?(o=r.substr(Co,4),Co+=4):(o=u,0===So&&Io(j)),o===u&&("shared"===r.substr(Co,6).toLowerCase()?(o=r.substr(Co,6),Co+=6):(o=u,0===So&&Io(_)),o===u&&("exclusive"===r.substr(Co,9).toLowerCase()?(o=r.substr(Co,9),Co+=9):(o=u,0===So&&Io(x))))),o!==u?(t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u),t}function qo(){var r,t,e,n,o,a,s,i;return r=Co,(t=Rs())===u&&(t=Os()),t!==u&&Bs()!==u?((e=aa())===u&&(e=null),e!==u&&Bs()!==u?((n=fu())===u&&(n=null),n!==u&&Bs()!==u&&(o=tu())!==u&&Bs()!==u?((a=pu())===u&&(a=null),a!==u&&Bs()!==u?(r,s=n,i=a,r=t={index:e,definition:o,keyword:t.toLowerCase(),index_type:s,resource:"index",index_options:i}):(Co=r,r=u)):(Co=r,r=u)):(Co=r,r=u)):(Co=r,r=u),r}function Bo(){var t,e,n,o,a,s,i,c,l;return t=Co,(e=function(){var t,e,n,o;t=Co,"fulltext"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(Xn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="FULLTEXT"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=Co,"spatial"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(Kn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="SPATIAL"):(Co=t,t=u)):(Co=t,t=u);return t}()),e!==u&&Bs()!==u?((n=Rs())===u&&(n=Os()),n===u&&(n=null),n!==u&&Bs()!==u?((o=aa())===u&&(o=null),o!==u&&Bs()!==u&&(a=tu())!==u&&Bs()!==u?((s=pu())===u&&(s=null),s!==u&&Bs()!==u?(t,i=e,l=s,t=e={index:o,definition:a,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"}):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u),t}function Yo(){var t,e,n,o;return t=Co,(e=function(){var t,e,n,o;t=Co,"constraint"===r.substr(Co,10).toLowerCase()?(e=r.substr(Co,10),Co+=10):(e=u,0===So&&Io(Zn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="CONSTRAINT"):(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u?((n=na())===u&&(n=null),n!==u?(t,o=n,t=e={keyword:e.toLowerCase(),constraint:o}):(Co=t,t=u)):(Co=t,t=u),t}function Vo(){var t,e,n,o,a,s,i,c,l,f;return t=Co,(e=function(){var t,e,n,o;t=Co,"references"===r.substr(Co,10).toLowerCase()?(e=r.substr(Co,10),Co+=10):(e=u,0===So&&Io(Jn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="REFERENCES"):(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u&&(n=bu())!==u&&Bs()!==u&&(o=tu())!==u&&Bs()!==u?("match full"===r.substr(Co,10).toLowerCase()?(a=r.substr(Co,10),Co+=10):(a=u,0===So&&Io(k)),a===u&&("match partial"===r.substr(Co,13).toLowerCase()?(a=r.substr(Co,13),Co+=13):(a=u,0===So&&Io(R)),a===u&&("match simple"===r.substr(Co,12).toLowerCase()?(a=r.substr(Co,12),Co+=12):(a=u,0===So&&Io(O)))),a===u&&(a=null),a!==u&&Bs()!==u?((s=Qo())===u&&(s=null),s!==u&&Bs()!==u?((i=Qo())===u&&(i=null),i!==u?(t,c=a,l=s,f=i,t=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,(e=Qo())!==u&&(t,e={on_action:[e]}),t=e),t}function Qo(){var t,e,n,o;return t=Co,qa()!==u&&Bs()!==u?((e=Ua())===u&&(e=ka()),e!==u&&Bs()!==u&&(n=function(){var t,e,n;t=Co,(e=_s())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u?((n=Du())===u&&(n=null),n!==u&&Bs()!==u&&$s()!==u?(t,t=e={type:"function",name:e,args:n}):(Co=t,t=u)):(Co=t,t=u);t===u&&(t=Co,"restrict"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(U)),e===u&&("cascade"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(M)),e===u&&("set null"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(D)),e===u&&("no action"===r.substr(Co,9).toLowerCase()?(e=r.substr(Co,9),Co+=9):(e=u,0===So&&Io(P)),e===u&&("set default"===r.substr(Co,11).toLowerCase()?(e=r.substr(Co,11),Co+=11):(e=u,0===So&&Io(F)),e===u&&(e=_s()))))),e!==u&&(t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==u?(t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(Co=t,t=u)):(Co=t,t=u),t}function Xo(){var t,e,n,o,a,s,i,c,l;return t=Co,(e=xa())===u&&(e=null),e!==u&&Bs()!==u?((n=function(){var t,e,n;return t=Co,"character"===r.substr(Co,9).toLowerCase()?(e=r.substr(Co,9),Co+=9):(e=u,0===So&&Io($)),e!==u&&Bs()!==u?("set"===r.substr(Co,3).toLowerCase()?(n=r.substr(Co,3),Co+=3):(n=u,0===So&&Io(H)),n!==u?(t,t=e="CHARACTER SET"):(Co=t,t=u)):(Co=t,t=u),t}())===u&&("charset"===r.substr(Co,7).toLowerCase()?(n=r.substr(Co,7),Co+=7):(n=u,0===So&&Io(W)),n===u&&("collate"===r.substr(Co,7).toLowerCase()?(n=r.substr(Co,7),Co+=7):(n=u,0===So&&Io(G)))),n!==u&&Bs()!==u?((o=Is())===u&&(o=null),o!==u&&Bs()!==u&&(a=ia())!==u?(t,i=n,c=o,l=a,t=e={keyword:(s=e)&&`${s[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u),t}function Ko(){var t,e,n,o,a,s,i,c,l;return t=Co,"auto_increment"===r.substr(Co,14).toLowerCase()?(e=r.substr(Co,14),Co+=14):(e=u,0===So&&Io(f)),e===u&&("avg_row_length"===r.substr(Co,14).toLowerCase()?(e=r.substr(Co,14),Co+=14):(e=u,0===So&&Io(q)),e===u&&("key_block_size"===r.substr(Co,14).toLowerCase()?(e=r.substr(Co,14),Co+=14):(e=u,0===So&&Io(B)),e===u&&("max_rows"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(Y)),e===u&&("min_rows"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(V)),e===u&&("stats_sample_pages"===r.substr(Co,18).toLowerCase()?(e=r.substr(Co,18),Co+=18):(e=u,0===So&&Io(Q))))))),e!==u&&Bs()!==u?((n=Is())===u&&(n=null),n!==u&&Bs()!==u&&(o=Ea())!==u?(t,c=n,l=o,t=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Xo())===u&&(t=Co,(e=Us())===u&&("connection"===r.substr(Co,10).toLowerCase()?(e=r.substr(Co,10),Co+=10):(e=u,0===So&&Io(X))),e!==u&&Bs()!==u?((n=Is())===u&&(n=null),n!==u&&Bs()!==u&&(o=ma())!==u?(t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,"compression"===r.substr(Co,11).toLowerCase()?(e=r.substr(Co,11),Co+=11):(e=u,0===So&&Io(K)),e!==u&&Bs()!==u?((n=Is())===u&&(n=null),n!==u&&Bs()!==u?(o=Co,39===r.charCodeAt(Co)?(a="'",Co++):(a=u,0===So&&Io(z)),a!==u?("zlib"===r.substr(Co,4).toLowerCase()?(s=r.substr(Co,4),Co+=4):(s=u,0===So&&Io(Z)),s===u&&("lz4"===r.substr(Co,3).toLowerCase()?(s=r.substr(Co,3),Co+=3):(s=u,0===So&&Io(J)),s===u&&("none"===r.substr(Co,4).toLowerCase()?(s=r.substr(Co,4),Co+=4):(s=u,0===So&&Io(j)))),s!==u?(39===r.charCodeAt(Co)?(i="'",Co++):(i=u,0===So&&Io(z)),i!==u?o=a=[a,s,i]:(Co=o,o=u)):(Co=o,o=u)):(Co=o,o=u),o!==u?(t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,"engine"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(rr)),e!==u&&Bs()!==u?((n=Is())===u&&(n=null),n!==u&&Bs()!==u&&(o=ia())!==u?(t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}}(e,n,o)):(Co=t,t=u)):(Co=t,t=u)))),t}function zo(){var t,e,n,o,a;return t=Co,(e=yu())!==u&&Bs()!==u&&(n=function(){var t,e,n;return t=Co,"read"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(tr)),e!==u&&Bs()!==u?("local"===r.substr(Co,5).toLowerCase()?(n=r.substr(Co,5),Co+=5):(n=u,0===So&&Io(er)),n===u&&(n=null),n!==u?(t,t=e={type:"read",suffix:n&&"local"}):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,"low_priority"===r.substr(Co,12).toLowerCase()?(e=r.substr(Co,12),Co+=12):(e=u,0===So&&Io(nr)),e===u&&(e=null),e!==u&&Bs()!==u?("write"===r.substr(Co,5).toLowerCase()?(n=r.substr(Co,5),Co+=5):(n=u,0===So&&Io(or)),n!==u?(t,t=e={type:"write",prefix:e&&"low_priority"}):(Co=t,t=u)):(Co=t,t=u)),t}())!==u?(t,o=e,a=n,Li.add(`lock::${o.db}::${o.table}`),t=e={table:o,lock_type:a}):(Co=t,t=u),t}function Zo(){var t,e,n,o,a,s,i;return(t=eu())===u&&(t=Co,e=Co,40===r.charCodeAt(Co)?(n="(",Co++):(n=u,0===So&&Io(ur)),n!==u&&(o=Bs())!==u&&(a=Zo())!==u&&(s=Bs())!==u?(41===r.charCodeAt(Co)?(i=")",Co++):(i=u,0===So&&Io(ar)),i!==u?e=n=[n,o,a,s,i]:(Co=e,e=u)):(Co=e,e=u),e!==u&&(t,e={...e[2],parentheses_symbol:!0}),t=e),t}function Jo(){var t,e,n,o,a,s,i,c,f;if(t=Co,Xa()!==u)if(Bs()!==u)if((e=ru())!==u){for(n=[],o=Co,(a=Bs())!==u&&(s=Ds())!==u&&(i=Bs())!==u&&(c=ru())!==u?o=a=[a,s,i,c]:(Co=o,o=u);o!==u;)n.push(o),o=Co,(a=Bs())!==u&&(s=Ds())!==u&&(i=Bs())!==u&&(c=ru())!==u?o=a=[a,s,i,c]:(Co=o,o=u);n!==u?(t,t=l(e,n)):(Co=t,t=u)}else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;return t===u&&(t=Co,Bs()!==u&&Xa()!==u&&(e=Bs())!==u&&(n=function(){var t,e,n,o;t=Co,"RECURSIVE"===r.substr(Co,9)?(e="RECURSIVE",Co+=9):(e=u,0===So&&Io(Yt));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(o=Bs())!==u&&(a=ru())!==u?(t,(f=a).recursive=!0,t=[f]):(Co=t,t=u)),t}function ru(){var r,t,e,n,o;return r=Co,(t=ma())===u&&(t=ia()),t!==u&&Bs()!==u?((e=tu())===u&&(e=null),e!==u&&Bs()!==u&&Ha()!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u&&(n=Mo())!==u&&Bs()!==u&&$s()!==u?(r,"string"==typeof(o=t)&&(o={type:"default",value:o}),r=t={name:o,stmt:n,columns:e}):(Co=r,r=u)):(Co=r,r=u),r}function tu(){var r,t;return r=Co,Fs()!==u&&Bs()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=ea())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=ea())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=ea())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,t=l(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())!==u&&Bs()!==u&&$s()!==u?(r,r=t):(Co=r,r=u),r}function eu(){var t,e,n,o,a,s,i,c,l,f,p,v,b,d,y,h,m,w,L,C,E;return t=Co,Bs()!==u?((e=Jo())===u&&(e=null),e!==u&&Bs()!==u&&function(){var t,e,n,o;t=Co,"select"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io($t));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Ys()!==u?((n=function(){var r,t,e,n,o,a;if(r=Co,(t=nu())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=nu())!==u?n=o=[o,a]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=nu())!==u?n=o=[o,a]:(Co=n,n=u);e!==u?(r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())===u&&(n=null),n!==u&&Bs()!==u?((o=Za())===u&&(o=null),o!==u&&Bs()!==u&&(a=ou())!==u&&Bs()!==u?((s=cu())===u&&(s=null),s!==u&&Bs()!==u?((i=Cu())===u&&(i=null),i!==u&&Bs()!==u?((c=function(){var t,e,n;t=Co,(e=function(){var t,e,n,o;t=Co,"group"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(me));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u&&Ka()!==u&&Bs()!==u&&(n=Du())!==u?(t,e=n.value,t=e):(Co=t,t=u);return t}())===u&&(c=null),c!==u&&Bs()!==u?((l=function(){var t,e;t=Co,function(){var t,e,n,o;t=Co,"having"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Ce));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u&&(e=qu())!==u?(t,t=e):(Co=t,t=u);return t}())===u&&(l=null),l!==u&&Bs()!==u?((f=ju())===u&&(f=null),f!==u&&Bs()!==u?((p=Nu())===u&&(p=null),p!==u?(t,v=e,b=n,d=o,y=a,m=i,w=c,L=l,C=f,E=p,(h=s)&&h.forEach(r=>r.table&&Li.add(`select::${r.db}::${r.table}`)),t={with:v,type:"select",options:b,distinct:d,columns:y,from:h,where:m,groupby:w,having:L,orderby:C,limit:E}):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u),t}function nu(){var t,e;return t=Co,(e=function(){var t;"sql_calc_found_rows"===r.substr(Co,19).toLowerCase()?(t=r.substr(Co,19),Co+=19):(t=u,0===So&&Io(ro));return t}())===u&&((e=function(){var t;"sql_cache"===r.substr(Co,9).toLowerCase()?(t=r.substr(Co,9),Co+=9):(t=u,0===So&&Io(to));return t}())===u&&(e=function(){var t;"sql_no_cache"===r.substr(Co,12).toLowerCase()?(t=r.substr(Co,12),Co+=12):(t=u,0===So&&Io(eo));return t}()),e===u&&(e=function(){var t;"sql_big_result"===r.substr(Co,14).toLowerCase()?(t=r.substr(Co,14),Co+=14):(t=u,0===So&&Io(oo));return t}())===u&&(e=function(){var t;"sql_small_result"===r.substr(Co,16).toLowerCase()?(t=r.substr(Co,16),Co+=16):(t=u,0===So&&Io(no));return t}())===u&&(e=function(){var t;"sql_buffer_result"===r.substr(Co,17).toLowerCase()?(t=r.substr(Co,17),Co+=17):(t=u,0===So&&Io(uo));return t}())),e!==u&&(t,e=e),t=e}function ou(){var r,t,e,n,o,a,s,i;if(r=Co,(t=za())===u&&(t=Co,(e=Ps())!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Ps())),t!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=su())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=su())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,r=t=function(r,t){Ci.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return t&&t.length>0?di(e,t):[e]}(0,e)):(Co=r,r=u)}else Co=r,r=u;if(r===u)if(r=Co,(t=su())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=su())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=su())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,r=t=l(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function uu(){var t,e,n,o,a,s,i;return t=Co,Hs()!==u&&Bs()!==u?((e=Ea())===u&&(e=ma()),e!==u&&Bs()!==u&&Ws()!==u?(n=Co,(o=Bs())!==u?(46===r.charCodeAt(Co)?(a=".",Co++):(a=u,0===So&&Io(sr)),a!==u&&(s=Bs())!==u&&(i=na())!==u?n=o=[o,a,s,i]:(Co=n,n=u)):(Co=n,n=u),n===u&&(n=null),n!==u?(t,t=function(r,t){let e;return t&&(e={type:"default",value:t[3]}),{brackets:!0,index:r,property:e}}(e,n)):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u),t}function au(){var r,t,e,n,o;return r=Co,(t=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Gu())!==u){for(e=[],n=Co,(o=Bs())!==u?((a=as())===u&&(a=ss())===u&&(a=qs()),a!==u&&(s=Bs())!==u&&(i=Gu())!==u?n=o=[o,a,s,i]:(Co=n,n=u)):(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u?((a=as())===u&&(a=ss())===u&&(a=qs()),a!==u&&(s=Bs())!==u&&(i=Gu())!==u?n=o=[o,a,s,i]:(Co=n,n=u)):(Co=n,n=u);e!==u?(r,t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=vi(t[e][1],n,o)}return o}(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())!==u&&Bs()!==u?((e=uu())===u&&(e=null),e!==u?(r,n=t,(o=e)&&(n.array_index=o),r=t=n):(Co=r,r=u)):(Co=r,r=u),r}function su(){var r,t,e,n,o;return r=Co,t=Co,(e=na())!==u&&(n=Bs())!==u&&(o=Ms())!==u?t=e=[e,n,o]:(Co=t,t=u),t===u&&(t=null),t!==u&&(e=Bs())!==u&&(n=Ps())!==u?(r,r=t=function(r){const t=r&&r[0]||null;return Ci.add(`select::${t}::(.*)`),{expr:{type:"column_ref",table:t,column:"*"},as:null}}(t)):(Co=r,r=u),r===u&&(r=Co,(t=au())!==u&&(e=Bs())!==u?((n=iu())===u&&(n=null),n!==u?(r,r=t={type:"expr",expr:t,as:n}):(Co=r,r=u)):(Co=r,r=u)),r}function iu(){var r,t,e;return r=Co,(t=Ha())!==u&&Bs()!==u&&(e=function(){var r,t;r=Co,(t=ia())!==u?(Co,(function(r){if(!0===fi[r.toUpperCase()])throw new Error("Error: "+JSON.stringify(r)+" is a reserved word, can not as alias clause");return!1}(t)?u:void 0)!==u?(r,r=t=t):(Co=r,r=u)):(Co=r,r=u);r===u&&(r=Co,(t=oa())!==u&&(r,t=t),r=t);return r}())!==u?(r,r=t=e):(Co=r,r=u),r===u&&(r=Co,(t=Ha())===u&&(t=null),t!==u&&Bs()!==u&&(e=na())!==u?(r,r=t=e):(Co=r,r=u)),r}function cu(){var t,e;return t=Co,function(){var t,e,n,o;t=Co,"from"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Jt));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u&&(e=bu())!==u?(t,t=e):(Co=t,t=u),t}function lu(){var r,t,e;return r=Co,(t=mu())!==u&&Bs()!==u&&Ia()!==u&&Bs()!==u&&(e=mu())!==u?(r,r=t=[t,e]):(Co=r,r=u),r}function fu(){var t,e;return t=Co,Qa()!==u&&Bs()!==u?("btree"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(ir)),e===u&&("hash"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(cr))),e!==u?(t,t={keyword:"using",type:e.toLowerCase()}):(Co=t,t=u)):(Co=t,t=u),t}function pu(){var r,t,e,n,o,a;if(r=Co,(t=vu())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=vu())!==u?n=o=[o,a]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=vu())!==u?n=o=[o,a]:(Co=n,n=u);e!==u?(r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function vu(){var t,e,n,o,a,s;return t=Co,(e=function(){var t,e,n,o;t=Co,"key_block_size"===r.substr(Co,14).toLowerCase()?(e=r.substr(Co,14),Co+=14):(e=u,0===So&&Io(B));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="KEY_BLOCK_SIZE"):(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u?((n=Is())===u&&(n=null),n!==u&&Bs()!==u&&(o=Ea())!==u?(t,a=n,s=o,t=e={type:e.toLowerCase(),symbol:a,expr:s}):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=fu())===u&&(t=Co,"with"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(lr)),e!==u&&Bs()!==u?("parser"===r.substr(Co,6).toLowerCase()?(n=r.substr(Co,6),Co+=6):(n=u,0===So&&Io(fr)),n!==u&&Bs()!==u&&(o=ia())!==u?(t,t=e={type:"with parser",expr:o}):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,"visible"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(pr)),e===u&&("invisible"===r.substr(Co,9).toLowerCase()?(e=r.substr(Co,9),Co+=9):(e=u,0===So&&Io(vr))),e!==u&&(t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===u&&(t=Qs()))),t}function bu(){var r,t,e,n;if(r=Co,(t=yu())!==u){for(e=[],n=du();n!==u;)e.push(n),n=du();e!==u?(r,r=t=br(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function du(){var r,t,e;return r=Co,Bs()!==u&&(t=Ds())!==u&&Bs()!==u&&(e=yu())!==u?(r,r=e):(Co=r,r=u),r===u&&(r=Co,Bs()!==u&&(t=function(){var r,t,e,n,o,a,s,i,c,l,f;if(r=Co,(t=hu())!==u)if(Bs()!==u)if((e=yu())!==u)if(Bs()!==u)if((n=Qa())!==u)if(Bs()!==u)if(Fs()!==u)if(Bs()!==u)if((o=ia())!==u){for(a=[],s=Co,(i=Bs())!==u&&(c=Ds())!==u&&(l=Bs())!==u&&(f=ia())!==u?s=i=[i,c,l,f]:(Co=s,s=u);s!==u;)a.push(s),s=Co,(i=Bs())!==u&&(c=Ds())!==u&&(l=Bs())!==u&&(f=ia())!==u?s=i=[i,c,l,f]:(Co=s,s=u);a!==u&&(s=Bs())!==u&&(i=$s())!==u?(r,p=t,b=o,d=a,(v=e).join=p,v.using=di(b,d),r=t=v):(Co=r,r=u)}else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;var p,v,b,d;r===u&&(r=Co,(t=hu())!==u&&Bs()!==u&&(e=yu())!==u&&Bs()!==u?((n=Lu())===u&&(n=null),n!==u?(r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(Co=r,r=u)):(Co=r,r=u),r===u&&(r=Co,(t=hu())!==u&&Bs()!==u&&(e=Fs())!==u&&Bs()!==u&&(n=Mo())!==u&&Bs()!==u&&$s()!==u&&Bs()!==u?((o=iu())===u&&(o=null),o!==u&&(a=Bs())!==u?((s=Lu())===u&&(s=null),s!==u?(r,t=function(r,t,e,n){return t.parentheses=!0,{expr:t,as:e,join:r,on:n}}(t,n,o,s),r=t):(Co=r,r=u)):(Co=r,r=u)):(Co=r,r=u)));return r}())!==u?(r,r=t):(Co=r,r=u)),r}function yu(){var t,e,n,o,a,s;return t=Co,(e=function(){var t;"dual"===r.substr(Co,4).toLowerCase()?(t=r.substr(Co,4),Co+=4):(t=u,0===So&&Io(Bn));return t}())!==u&&(t,e={type:"dual"}),(t=e)===u&&(t=Co,(e=mu())!==u&&Bs()!==u?((n=iu())===u&&(n=null),n!==u?(t,s=n,t=e="var"===(a=e).type?(a.as=s,a):{db:a.db,table:a.table,as:s}):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,(e=Fs())!==u&&Bs()!==u&&(n=Mo())!==u&&Bs()!==u&&$s()!==u&&Bs()!==u?((o=iu())===u&&(o=null),o!==u?(t,t=e=function(r,t){return r.parentheses=!0,{expr:r,as:t}}(n,o)):(Co=t,t=u)):(Co=t,t=u))),t}function hu(){var t,e,n,o;return t=Co,(e=function(){var t,e,n,o;t=Co,"left"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(se));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(n=Bs())!==u?((o=Ya())===u&&(o=null),o!==u&&Bs()!==u&&Ba()!==u?(t,t=e="LEFT JOIN"):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,(e=function(){var t,e,n,o;t=Co,"right"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(ie));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(n=Bs())!==u?((o=Ya())===u&&(o=null),o!==u&&Bs()!==u&&Ba()!==u?(t,t=e="RIGHT JOIN"):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,(e=function(){var t,e,n,o;t=Co,"full"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(ce));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(n=Bs())!==u?((o=Ya())===u&&(o=null),o!==u&&Bs()!==u&&Ba()!==u?(t,t=e="FULL JOIN"):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,e=Co,(n=function(){var t,e,n,o;t=Co,"inner"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(fe));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(o=Bs())!==u?e=n=[n,o]:(Co=e,e=u),e===u&&(e=null),e!==u&&(n=Ba())!==u?(t,t=e="INNER JOIN"):(Co=t,t=u),t===u&&(t=Co,(e=function(){var t,e,n,o;t=Co,"cross"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(le));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(n=Bs())!==u&&(o=Ba())!==u?(t,t=e="CROSS JOIN"):(Co=t,t=u))))),t}function mu(){var r,t,e,n,o,a,s,i;return r=Co,(t=na())!==u?(e=Co,(n=Bs())!==u&&(o=Ms())!==u&&(a=Bs())!==u&&(s=na())!==u?e=n=[n,o,a,s]:(Co=e,e=u),e===u&&(e=null),e!==u?(r,r=t=function(r,t){const e={db:null,table:r};return null!==t&&(e.db=r,e.table=t[3]),e}(t,e)):(Co=r,r=u)):(Co=r,r=u),r===u&&(r=Co,(t=si())!==u&&(r,(i=t).db=null,i.table=i.name,t=i),r=t),r}function wu(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Gu())!==u){for(e=[],n=Co,(o=Bs())!==u?((a=as())===u&&(a=ss()),a!==u&&(s=Bs())!==u&&(i=Gu())!==u?n=o=[o,a,s,i]:(Co=n,n=u)):(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u?((a=as())===u&&(a=ss()),a!==u&&(s=Bs())!==u&&(i=Gu())!==u?n=o=[o,a,s,i]:(Co=n,n=u)):(Co=n,n=u);e!==u?(r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=vi(t[r][1],n,t[r][3]);return n}(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function Lu(){var r,t;return r=Co,qa()!==u&&Bs()!==u&&(t=qu())!==u?(r,r=t):(Co=r,r=u),r}function Cu(){var t,e;return t=Co,function(){var t,e,n,o;t=Co,"where"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(he));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u&&(e=qu())!==u?(t,t=e):(Co=t,t=u),t}function Eu(){var r,t;return(r=ia())===u&&(r=Co,Fs()!==u&&Bs()!==u?((t=function(){var r,t,e,n;r=Co,(t=_u())===u&&(t=null);t!==u&&Bs()!==u?((e=ju())===u&&(e=null),e!==u&&Bs()!==u?((n=function(){var r,t,e,n,o;r=Co,(t=Ss())!==u&&Bs()!==u?((e=Au())===u&&(e=gu()),e!==u?(r,t="rows "+e.value,r=t):(Co=r,r=u)):(Co=r,r=u);r===u&&(r=Co,(t=Ss())!==u&&Bs()!==u&&(e=Ja())!==u&&Bs()!==u&&(n=gu())!==u&&Bs()!==u&&as()!==u&&Bs()!==u&&(o=Au())!==u?(r,a=o,t=`rows between ${n.value} and ${a.value}`,r=t):(Co=r,r=u));var a;return r}())===u&&(n=null),n!==u?(r,r=t={name:null,partitionby:t,orderby:e,window_frame_clause:n}):(Co=r,r=u)):(Co=r,r=u)):(Co=r,r=u);return r}())===u&&(t=null),t!==u&&Bs()!==u&&$s()!==u?(r,r={window_specification:t||{},parentheses:!0}):(Co=r,r=u)):(Co=r,r=u)),r}function Au(){var t,e,n,o;return t=Co,(e=Tu())!==u&&Bs()!==u?("following"===r.substr(Co,9).toLowerCase()?(n=r.substr(Co,9),Co+=9):(n=u,0===So&&Io(dr)),n!==u?(t,(o=e).value+=" FOLLOWING",t=e=o):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Su()),t}function gu(){var t,e,n,o;return t=Co,(e=Tu())!==u&&Bs()!==u?("preceding"===r.substr(Co,9).toLowerCase()?(n=r.substr(Co,9),Co+=9):(n=u,0===So&&Io(yr)),n!==u?(t,(o=e).value+=" PRECEDING",t=e=o):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Su()),t}function Su(){var t,e,n;return t=Co,"current"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(hr)),e!==u&&Bs()!==u?("row"===r.substr(Co,3).toLowerCase()?(n=r.substr(Co,3),Co+=3):(n=u,0===So&&Io(mr)),n!==u?(t,t=e={type:"single_quote_string",value:"current row"}):(Co=t,t=u)):(Co=t,t=u),t}function Tu(){var t,e;return t=Co,"unbounded"===r.substr(Co,9).toLowerCase()?(e=r.substr(Co,9),Co+=9):(e=u,0===So&&Io(wr)),e!==u&&(t,e={type:"single_quote_string",value:e.toUpperCase()}),(t=e)===u&&(t=Ea()),t}function ju(){var t,e;return t=Co,function(){var t,e,n,o;t=Co,"order"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(Le));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u&&Ka()!==u&&Bs()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=xu())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=xu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=xu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,t=l(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())!==u?(t,t=e):(Co=t,t=u),t}function _u(){var r,t;return r=Co,Pa()!==u&&Bs()!==u&&Ka()!==u&&Bs()!==u&&(t=ou())!==u?(r,r=t):(Co=r,r=u),r}function xu(){var t,e,n;return t=Co,(e=Gu())!==u&&Bs()!==u?((n=function(){var t,e,n,o;t=Co,"desc"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Se));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="DESC"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=Co,"asc"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(ge));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="ASC"):(Co=t,t=u)):(Co=t,t=u);return t}()),n===u&&(n=null),n!==u?(t,t=e={expr:e,type:n}):(Co=t,t=u)):(Co=t,t=u),t}function Iu(){var r;return(r=Ea())===u&&(r=pa()),r}function Nu(){var t,e,n,o,a,s;return t=Co,function(){var t,e,n,o;t=Co,"limit"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(Ee));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u&&(e=Iu())!==u&&Bs()!==u?(n=Co,(o=Ds())===u&&(o=function(){var t,e,n,o;t=Co,"offset"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Ae));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="OFFSET"):(Co=t,t=u)):(Co=t,t=u);return t}()),o!==u&&(a=Bs())!==u&&(s=Iu())!==u?n=o=[o,a,s]:(Co=n,n=u),n===u&&(n=null),n!==u?(t,t=function(r,t){const e=[r];return t&&e.push(t[2]),{seperator:t&&t[0]&&t[0].toLowerCase()||"",value:e}}(e,n)):(Co=t,t=u)):(Co=t,t=u),t}function ku(){var t,e,n,o,a,s,i,c,l;return t=Co,e=Co,(n=na())!==u&&(o=Bs())!==u&&(a=Ms())!==u?e=n=[n,o,a]:(Co=e,e=u),e===u&&(e=null),e!==u&&(n=Bs())!==u&&(o=ua())!==u&&(a=Bs())!==u?(61===r.charCodeAt(Co)?(s="=",Co++):(s=u,0===So&&Io(Lr)),s!==u&&Bs()!==u&&(i=zu())!==u?(t,t=e={column:o,value:i,table:(l=e)&&l[0]}):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,e=Co,(n=na())!==u&&(o=Bs())!==u&&(a=Ms())!==u?e=n=[n,o,a]:(Co=e,e=u),e===u&&(e=null),e!==u&&(n=Bs())!==u&&(o=ua())!==u&&(a=Bs())!==u?(61===r.charCodeAt(Co)?(s="=",Co++):(s=u,0===So&&Io(Lr)),s!==u&&Bs()!==u&&(i=Va())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u&&(c=ea())!==u&&Bs()!==u&&$s()!==u?(t,t=e=function(r,t,e){return{column:t,value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):(Co=t,t=u)):(Co=t,t=u)),t}function Ru(){var r;return(r=function(){var r,t;r=Co,Va()!==u&&Bs()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Mu())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=Mu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=Mu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,t=l(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())!==u?(r,r=t):(Co=r,r=u);return r}())===u&&(r=eu()),r}function Ou(){var r,t,e,n,o,a,s,i,c;if(r=Co,Pa()!==u)if(Bs()!==u)if((t=Fs())!==u)if(Bs()!==u)if((e=ia())!==u){for(n=[],o=Co,(a=Bs())!==u&&(s=Ds())!==u&&(i=Bs())!==u&&(c=ia())!==u?o=a=[a,s,i,c]:(Co=o,o=u);o!==u;)n.push(o),o=Co,(a=Bs())!==u&&(s=Ds())!==u&&(i=Bs())!==u&&(c=ia())!==u?o=a=[a,s,i,c]:(Co=o,o=u);n!==u&&(o=Bs())!==u&&(a=$s())!==u?(r,r=di(e,n)):(Co=r,r=u)}else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;else Co=r,r=u;return r===u&&(r=Co,Pa()!==u&&Bs()!==u&&(t=Mu())!==u?(r,r=t):(Co=r,r=u)),r}function Uu(){var t,e;return t=Co,(e=function(){var t,e,n,o;t=Co,"insert"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Bt));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(t,e="insert"),(t=e)===u&&(t=Co,(e=Ma())!==u&&(t,e="replace"),t=e),t}function Mu(){var r,t;return r=Co,Fs()!==u&&Bs()!==u&&(t=Du())!==u&&Bs()!==u&&$s()!==u?(r,r=t):(Co=r,r=u),r}function Du(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Gu())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=Gu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=Gu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,r=t=function(r,t){const e={type:"expr_list"};return e.value=di(r,t),e}(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function Pu(){var t,e,n;return t=Co,function(){var t,e,n,o;t=Co,"interval"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(gn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="INTERVAL"):(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u&&(e=Gu())!==u&&Bs()!==u&&(n=function(){var t;(t=function(){var t,e,n,o;t=Co,"year"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Sn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="YEAR"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"month"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(Tn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="MONTH"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"day"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(jn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="DAY"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"hour"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(_n));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="HOUR"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"minute"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(xn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="MINUTE"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"second"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(In));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="SECOND"):(Co=t,t=u)):(Co=t,t=u);return t}());return t}())!==u?(t,t={type:"interval",expr:e,unit:n.toLowerCase()}):(Co=t,t=u),t}function Fu(){var r,t,e,n,o,a;if(r=Co,(t=$u())!==u)if(Bs()!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=$u())!==u?n=o=[o,a]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=$u())!==u?n=o=[o,a]:(Co=n,n=u);e!==u?(r,r=t=i(t,e)):(Co=r,r=u)}else Co=r,r=u;else Co=r,r=u;return r}function $u(){var t,e,n;return t=Co,function(){var t,e,n,o;t=Co,"when"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(qe));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u&&(e=qu())!==u&&Bs()!==u&&function(){var t,e,n,o;t=Co,"then"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Be));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u&&(n=Gu())!==u?(t,t={type:"when",cond:e,result:n}):(Co=t,t=u),t}function Hu(){var t,e;return t=Co,function(){var t,e,n,o;t=Co,"else"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Ye));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}()!==u&&Bs()!==u&&(e=Gu())!==u?(t,t={type:"else",result:e}):(Co=t,t=u),t}function Wu(){var r;return(r=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=ta())!==u){if(e=[],n=Co,(o=Bs())!==u&&(a=qs())!==u&&(s=Bs())!==u&&(i=ta())!==u?n=o=[o,a,s,i]:(Co=n,n=u),n!==u)for(;n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=qs())!==u&&(s=Bs())!==u&&(i=ta())!==u?n=o=[o,a,s,i]:(Co=n,n=u);else e=u;e!==u&&(n=Bs())!==u?((o=Qu())===u&&(o=null),o!==u?(r,t=function(r,t,e){const n=yi(r,t);return null===e?n:"arithmetic"===e.type?yi(n,e.tail):vi(e.op,n,e.right)}(t,e,o),r=t):(Co=r,r=u)):(Co=r,r=u)}else Co=r,r=u;return r}())===u&&(r=function(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Bu())!==u){for(e=[],n=Co,(o=Ys())!==u&&(a=ss())!==u&&(s=Bs())!==u&&(i=Bu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Ys())!==u&&(a=ss())!==u&&(s=Bs())!==u&&(i=Bu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,t=Cr(t,e),r=t):(Co=r,r=u)}else Co=r,r=u;return r}())===u&&(r=function(){var r,t,e,n,o,a;if(r=Co,(t=Zu())!==u){if(e=[],n=Co,(o=Bs())!==u&&(a=ta())!==u?n=o=[o,a]:(Co=n,n=u),n!==u)for(;n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=ta())!==u?n=o=[o,a]:(Co=n,n=u);else e=u;e!==u?(r,t=pi(t,e[0][1]),r=t):(Co=r,r=u)}else Co=r,r=u;return r}()),r}function Gu(){var r;return(r=Wu())===u&&(r=Mo()),r}function qu(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Gu())!==u){for(e=[],n=Co,(o=Bs())!==u?((a=as())===u&&(a=ss())===u&&(a=Ds()),a!==u&&(s=Bs())!==u&&(i=Gu())!==u?n=o=[o,a,s,i]:(Co=n,n=u)):(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u?((a=as())===u&&(a=ss())===u&&(a=Ds()),a!==u&&(s=Bs())!==u&&(i=Gu())!==u?n=o=[o,a,s,i]:(Co=n,n=u)):(Co=n,n=u);e!==u?(r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=vi(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=n,r}return n}(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function Bu(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Yu())!==u){for(e=[],n=Co,(o=Ys())!==u&&(a=as())!==u&&(s=Bs())!==u&&(i=Yu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Ys())!==u&&(a=as())!==u&&(s=Bs())!==u&&(i=Yu())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,r=t=yi(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function Yu(){var t,e,n,o,a;return(t=Vu())===u&&(t=function(){var r,t,e;r=Co,(t=function(){var r,t,e,n,o;r=Co,t=Co,(e=us())!==u&&(n=Bs())!==u&&(o=os())!==u?t=e=[e,n,o]:(Co=t,t=u);t!==u&&(r,t=Ar(t));(r=t)===u&&(r=os());return r}())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u&&(e=Mo())!==u&&Bs()!==u&&$s()!==u?(r,n=t,(o=e).parentheses=!0,t=pi(n,o),r=t):(Co=r,r=u);var n,o;return r}())===u&&(t=Co,(e=us())===u&&(e=Co,33===r.charCodeAt(Co)?(n="!",Co++):(n=u,0===So&&Io(Er)),n!==u?(o=Co,So++,61===r.charCodeAt(Co)?(a="=",Co++):(a=u,0===So&&Io(Lr)),So--,a===u?o=void 0:(Co=o,o=u),o!==u?e=n=[n,o]:(Co=e,e=u)):(Co=e,e=u)),e!==u&&(n=Bs())!==u&&(o=Yu())!==u?(t,t=e=pi("NOT",o)):(Co=t,t=u)),t}function Vu(){var r,t,e,n,o;return r=Co,(t=zu())!==u&&Bs()!==u?((e=Qu())===u&&(e=null),e!==u?(r,n=t,r=t=null===(o=e)?n:"arithmetic"===o.type?yi(n,o.tail):vi(o.op,n,o.right)):(Co=r,r=u)):(Co=r,r=u),r===u&&(r=ma())===u&&(r=ea()),r}function Qu(){var r;return(r=function(){var r,t,e,n,o,a,s;r=Co,t=[],e=Co,(n=Bs())!==u&&(o=Xu())!==u&&(a=Bs())!==u&&(s=zu())!==u?e=n=[n,o,a,s]:(Co=e,e=u);if(e!==u)for(;e!==u;)t.push(e),e=Co,(n=Bs())!==u&&(o=Xu())!==u&&(a=Bs())!==u&&(s=zu())!==u?e=n=[n,o,a,s]:(Co=e,e=u);else t=u;t!==u&&(r,t={type:"arithmetic",tail:t});return r=t}())===u&&(r=function(){var r,t,e,n;r=Co,(t=Ku())!==u&&Bs()!==u&&(e=Fs())!==u&&Bs()!==u&&(n=Du())!==u&&Bs()!==u&&$s()!==u?(r,r=t={op:t,right:n}):(Co=r,r=u);r===u&&(r=Co,(t=Ku())!==u&&Bs()!==u?((e=si())===u&&(e=ma()),e!==u?(r,t=function(r,t){return{op:r,right:t}}(t,e),r=t):(Co=r,r=u)):(Co=r,r=u));return r}())===u&&(r=function(){var r,t,e,n;r=Co,(t=function(){var r,t,e,n,o;r=Co,t=Co,(e=us())!==u&&(n=Bs())!==u&&(o=Ja())!==u?t=e=[e,n,o]:(Co=t,t=u);t!==u&&(r,t=Ar(t));(r=t)===u&&(r=Ja());return r}())!==u&&Bs()!==u&&(e=zu())!==u&&Bs()!==u&&as()!==u&&Bs()!==u&&(n=zu())!==u?(r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(Co=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o;r=Co,(t=ts())!==u&&(e=Bs())!==u&&(n=zu())!==u?(r,r=t={op:"IS",right:n}):(Co=r,r=u);r===u&&(r=Co,t=Co,(e=ts())!==u&&(n=Bs())!==u&&(o=us())!==u?t=e=[e,n,o]:(Co=t,t=u),t!==u&&(e=Bs())!==u&&(n=zu())!==u?(r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(Co=r,r=u));return r}())===u&&(r=function(){var r,t,e;r=Co,(t=function(){var r,t,e,n,o;r=Co,t=Co,(e=us())!==u&&(n=Bs())!==u&&(o=es())!==u?t=e=[e,n,o]:(Co=t,t=u);t!==u&&(r,t=Ar(t));(r=t)===u&&(r=es());return r}())!==u&&Bs()!==u?((e=ya())===u&&(e=Vu()),e!==u?(r,t=Nr(t,e),r=t):(Co=r,r=u)):(Co=r,r=u);return r}())===u&&(r=function(){var r,t,e;r=Co,(t=function(){var r,t,e,n,o;r=Co,t=Co,(e=us())!==u&&(n=Bs())!==u&&(o=ns())!==u?t=e=[e,n,o]:(Co=t,t=u);t!==u&&(r,t=Ar(t));(r=t)===u&&(r=ns());return r}())!==u&&Bs()!==u?((e=ya())===u&&(e=Vu()),e!==u?(r,t=Nr(t,e),r=t):(Co=r,r=u)):(Co=r,r=u);return r}()),r}function Xu(){var t;return">="===r.substr(Co,2)?(t=">=",Co+=2):(t=u,0===So&&Io(gr)),t===u&&(62===r.charCodeAt(Co)?(t=">",Co++):(t=u,0===So&&Io(Sr)),t===u&&("<="===r.substr(Co,2)?(t="<=",Co+=2):(t=u,0===So&&Io(Tr)),t===u&&("<>"===r.substr(Co,2)?(t="<>",Co+=2):(t=u,0===So&&Io(jr)),t===u&&(60===r.charCodeAt(Co)?(t="<",Co++):(t=u,0===So&&Io(_r)),t===u&&("=="===r.substr(Co,2)?(t="==",Co+=2):(t=u,0===So&&Io(xr)),t===u&&(61===r.charCodeAt(Co)?(t="=",Co++):(t=u,0===So&&Io(Lr)),t===u&&("!="===r.substr(Co,2)?(t="!=",Co+=2):(t=u,0===So&&Io(Ir))))))))),t}function Ku(){var r,t,e,n,o;return r=Co,t=Co,(e=us())!==u&&(n=Bs())!==u&&(o=rs())!==u?t=e=[e,n,o]:(Co=t,t=u),t!==u&&(r,t=Ar(t)),(r=t)===u&&(r=rs()),r}function zu(){var r,t,e,n,o,a,s,i;if(r=Co,(t=Ju())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Zu())!==u&&(s=Bs())!==u&&(i=Ju())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Zu())!==u&&(s=Bs())!==u&&(i=Ju())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,r=t=Cr(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function Zu(){var t;return 43===r.charCodeAt(Co)?(t="+",Co++):(t=u,0===So&&Io(kr)),t===u&&(45===r.charCodeAt(Co)?(t="-",Co++):(t=u,0===So&&Io(Rr))),t}function Ju(){var r,t,e,n,o,a,s,i;if(r=Co,(t=ta())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=ra())!==u&&(s=Bs())!==u&&(i=ta())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=ra())!==u&&(s=Bs())!==u&&(i=ta())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,r=t=yi(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function ra(){var t;return 42===r.charCodeAt(Co)?(t="*",Co++):(t=u,0===So&&Io(Or)),t===u&&(47===r.charCodeAt(Co)?(t="/",Co++):(t=u,0===So&&Io(Ur)),t===u&&(37===r.charCodeAt(Co)?(t="%",Co++):(t=u,0===So&&Io(Mr)))),t}function ta(){var t,e,n,o,a,s;return(t=function(){var t,e,n,o,a,s,i;t=Co,(e=ls())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u&&(n=Gu())!==u&&Bs()!==u&&Ha()!==u&&Bs()!==u&&(o=ci())!==u&&Bs()!==u&&(a=$s())!==u?(t,c=n,l=o,e={type:"cast",keyword:e.toLowerCase(),expr:c,symbol:"as",target:l},t=e):(Co=t,t=u);var c,l;t===u&&(t=Co,(e=ls())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u&&(n=Gu())!==u&&Bs()!==u&&Ha()!==u&&Bs()!==u&&(o=bs())!==u&&Bs()!==u&&(a=Fs())!==u&&Bs()!==u&&(s=Aa())!==u&&Bs()!==u&&$s()!==u&&Bs()!==u&&(i=$s())!==u?(t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:{dataType:"DECIMAL("+e+")"}}}(e,n,s),t=e):(Co=t,t=u),t===u&&(t=Co,(e=ls())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u&&(n=Gu())!==u&&Bs()!==u&&Ha()!==u&&Bs()!==u&&(o=bs())!==u&&Bs()!==u&&(a=Fs())!==u&&Bs()!==u&&(s=Aa())!==u&&Bs()!==u&&Ds()!==u&&Bs()!==u&&(i=Aa())!==u&&Bs()!==u&&$s()!==u&&Bs()!==u&&$s()!==u?(t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:{dataType:"DECIMAL("+e+", "+n+")"}}}(e,n,s,i),t=e):(Co=t,t=u),t===u&&(t=Co,(e=ls())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u&&(n=Gu())!==u&&Bs()!==u&&Ha()!==u&&Bs()!==u&&(o=function(){var t;(t=function(){var t,e,n,o;t=Co,"signed"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Je));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="SIGNED"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=ds());return t}())!==u&&Bs()!==u?((a=hs())===u&&(a=null),a!==u&&Bs()!==u&&(s=$s())!==u?(t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:{dataType:e+(n?" "+n:"")}}}(e,n,o,a),t=e):(Co=t,t=u)):(Co=t,t=u))));return t}())===u&&(t=ya())===u&&(t=function(){var t;(t=function(){var t,e,n,o;t=Co,(e=function(){var t,e,n,o;t=Co,"count"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(De));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="COUNT"):(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u&&(n=function(){var t,e,n,o,a,s,i,c,l,f;t=Co,(e=function(){var t,e;t=Co,42===r.charCodeAt(Co)?(e="*",Co++):(e=u,0===So&&Io(Or));e!==u&&(t,e={type:"star",value:"*"});return t=e}())!==u&&(t,e={expr:e});if((t=e)===u){if(t=Co,(e=Za())===u&&(e=null),e!==u)if(Bs()!==u)if((n=Fs())!==u)if(Bs()!==u)if((o=Gu())!==u)if(Bs()!==u)if($s()!==u){for(a=[],s=Co,(i=Bs())!==u?((c=as())===u&&(c=ss()),c!==u&&(l=Bs())!==u&&(f=Gu())!==u?s=i=[i,c,l,f]:(Co=s,s=u)):(Co=s,s=u);s!==u;)a.push(s),s=Co,(i=Bs())!==u?((c=as())===u&&(c=ss()),c!==u&&(l=Bs())!==u&&(f=Gu())!==u?s=i=[i,c,l,f]:(Co=s,s=u)):(Co=s,s=u);a!==u&&(s=Bs())!==u?((i=ju())===u&&(i=null),i!==u?(t,e=function(r,t,e,n){const o=e.length;let u=t;u.parentheses=!0;for(let r=0;r<o;++r)u=vi(e[r][1],u,e[r][3]);return{distinct:r,expr:u,orderby:n}}(e,o,a,i),t=e):(Co=t,t=u)):(Co=t,t=u)}else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;t===u&&(t=Co,(e=Za())===u&&(e=null),e!==u&&Bs()!==u&&(n=wu())!==u&&Bs()!==u?((o=ju())===u&&(o=null),o!==u?(t,t=e={distinct:e,expr:n,orderby:o}):(Co=t,t=u)):(Co=t,t=u))}return t}())!==u&&Bs()!==u&&$s()!==u&&Bs()!==u?((o=ba())===u&&(o=null),o!==u?(t,t=e={type:"aggr_func",name:e,args:n,over:o}):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n;t=Co,(e=function(){var t;(t=function(){var t,e,n,o;t=Co,"sum"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io($e));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="SUM"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"max"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(Pe));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="MAX"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"min"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(Fe));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="MIN"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"avg"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(He));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="AVG"):(Co=t,t=u)):(Co=t,t=u);return t}());return t}())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u&&(n=zu())!==u&&Bs()!==u&&$s()!==u?(t,t=e={type:"aggr_func",name:e,args:{expr:n}}):(Co=t,t=u);return t}());return t}())===u&&(t=function(){var t,e,n,o,a;t=Co,(e=function(){var t;(t=da())===u&&(t=function(){var t,e,n,o;t=Co,"current_user"===r.substr(Co,12).toLowerCase()?(e=r.substr(Co,12),Co+=12):(e=u,0===So&&Io(Rn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="CURRENT_USER"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"user"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(En));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="USER"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"session_user"===r.substr(Co,12).toLowerCase()?(e=r.substr(Co,12),Co+=12):(e=u,0===So&&Io(On));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="SESSION_USER"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"system_user"===r.substr(Co,11).toLowerCase()?(e=r.substr(Co,11),Co+=11):(e=u,0===So&&Io(Un));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="SYSTEM_USER"):(Co=t,t=u)):(Co=t,t=u);return t}());return t}())!==u&&Bs()!==u&&(n=Fs())!==u&&Bs()!==u?((o=Du())===u&&(o=null),o!==u&&Bs()!==u&&$s()!==u&&Bs()!==u?((a=ba())===u&&(a=null),a!==u?(t,t=e={type:"function",name:e,args:o||{type:"expr_list",value:[]},over:a}):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u);t===u&&(t=Co,(e=da())!==u&&Bs()!==u?((n=va())===u&&(n=null),n!==u?(t,t=e={type:"function",name:e,over:n}):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,(e=As())===u&&(e=Ts())===u&&(e=js())===u&&("at time zone"===r.substr(Co,12).toLowerCase()?(e=r.substr(Co,12),Co+=12):(e=u,0===So&&Io(et))),e!==u&&Bs()!==u?((n=qu())===u&&(n=null),n!==u&&Bs()!==u?((o=ba())===u&&(o=null),o!==u?(t,e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e,args_parentheses:!1}}(e,n,o),t=e):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=Co,(e=oi())!==u&&Bs()!==u&&(n=Fs())!==u&&Bs()!==u?((o=qu())===u&&(o=null),o!==u&&Bs()!==u&&$s()!==u&&Bs()!==u?((a=ba())===u&&(a=null),a!==u?(t,e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e}}(e,o,a),t=e):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u))));return t}())===u&&(t=function(){var r,t,e,n,o,a,s,i;return r=Co,is()!==u&&Bs()!==u&&(t=Fu())!==u&&Bs()!==u?((e=Hu())===u&&(e=null),e!==u&&Bs()!==u&&(n=cs())!==u&&Bs()!==u?((o=is())===u&&(o=null),o!==u?(r,s=t,(i=e)&&s.push(i),r={type:"case",expr:null,args:s}):(Co=r,r=u)):(Co=r,r=u)):(Co=r,r=u),r===u&&(r=Co,is()!==u&&Bs()!==u&&(t=Gu())!==u&&Bs()!==u&&(e=Fu())!==u&&Bs()!==u?((n=Hu())===u&&(n=null),n!==u&&Bs()!==u&&(o=cs())!==u&&Bs()!==u?((a=is())===u&&(a=null),a!==u?(r,r=function(r,t,e){return e&&t.push(e),{type:"case",expr:r,args:t}}(t,e,n)):(Co=r,r=u)):(Co=r,r=u)):(Co=r,r=u)),r}())===u&&(t=Pu())===u&&(t=Co,(e=ea())!==u&&Bs()!==u&&(n=uu())!==u?(t,s=n,(a=e).array_index=s,t=e=a):(Co=t,t=u),t===u&&(t=ea())===u&&(t=pa())===u&&(t=Co,(e=Fs())!==u&&Bs()!==u&&(n=qu())!==u&&Bs()!==u&&$s()!==u?(t,(o=n).parentheses=!0,t=e=o):(Co=t,t=u),t===u&&(t=si()))),t}function ea(){var r,t,e,n,o;return r=Co,(t=na())!==u&&Bs()!==u&&Ms()!==u&&Bs()!==u&&(e=aa())!==u?(r,n=t,o=e,Ci.add(`select::${n}::${o}`),r=t={type:"column_ref",table:n,column:o}):(Co=r,r=u),r===u&&(r=Co,(t=aa())!==u&&(r,t=function(r){return Ci.add("select::null::"+r),{type:"column_ref",table:null,column:r}}(t)),r=t),r}function na(){var r,t;return r=Co,(t=ia())!==u?(Co,(Dr(t)?u:void 0)!==u?(r,r=t=t):(Co=r,r=u)):(Co=r,r=u),r===u&&(r=Co,(t=oa())!==u&&(r,t=t),r=t),r}function oa(){var t;return(t=function(){var t,e,n,o;t=Co,34===r.charCodeAt(Co)?(e='"',Co++):(e=u,0===So&&Io(Pr));if(e!==u){if(n=[],Fr.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io($r)),o!==u)for(;o!==u;)n.push(o),Fr.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io($r));else n=u;n!==u?(34===r.charCodeAt(Co)?(o='"',Co++):(o=u,0===So&&Io(Pr)),o!==u?(t,e=Hr(n),t=e):(Co=t,t=u)):(Co=t,t=u)}else Co=t,t=u;return t}())===u&&(t=function(){var t,e,n,o;t=Co,39===r.charCodeAt(Co)?(e="'",Co++):(e=u,0===So&&Io(z));if(e!==u){if(n=[],Wr.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io(Gr)),o!==u)for(;o!==u;)n.push(o),Wr.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io(Gr));else n=u;n!==u?(39===r.charCodeAt(Co)?(o="'",Co++):(o=u,0===So&&Io(z)),o!==u?(t,e=Hr(n),t=e):(Co=t,t=u)):(Co=t,t=u)}else Co=t,t=u;return t}())===u&&(t=function(){var t,e,n,o;t=Co,96===r.charCodeAt(Co)?(e="`",Co++):(e=u,0===So&&Io(qr));if(e!==u){if(n=[],Br.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io(Yr)),o!==u)for(;o!==u;)n.push(o),Br.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io(Yr));else n=u;n!==u?(96===r.charCodeAt(Co)?(o="`",Co++):(o=u,0===So&&Io(qr)),o!==u?(t,e=Hr(n),t=e):(Co=t,t=u)):(Co=t,t=u)}else Co=t,t=u;return t}()),t}function ua(){var r,t;return r=Co,(t=sa())!==u&&(r,t=t),(r=t)===u&&(r=oa()),r}function aa(){var r,t;return r=Co,(t=sa())!==u?(Co,(Dr(t)?u:void 0)!==u?(r,r=t=t):(Co=r,r=u)):(Co=r,r=u),r===u&&(r=oa()),r}function sa(){var r,t,e,n;if(r=Co,(t=la())!==u){for(e=[],n=fa();n!==u;)e.push(n),n=fa();e!==u?(r,r=t=Vr(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function ia(){var r,t,e,n;if(r=Co,(t=ca())!==u){for(e=[],n=la();n!==u;)e.push(n),n=la();e!==u?(r,r=t=Vr(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function ca(){var t;return Qr.test(r.charAt(Co))?(t=r.charAt(Co),Co++):(t=u,0===So&&Io(Xr)),t}function la(){var t;return Kr.test(r.charAt(Co))?(t=r.charAt(Co),Co++):(t=u,0===So&&Io(zr)),t}function fa(){var t;return Zr.test(r.charAt(Co))?(t=r.charAt(Co),Co++):(t=u,0===So&&Io(Jr)),t}function pa(){var t,e,n,o;return t=Co,e=Co,58===r.charCodeAt(Co)?(n=":",Co++):(n=u,0===So&&Io(rt)),n!==u&&(o=ia())!==u?e=n=[n,o]:(Co=e,e=u),e!==u&&(t,e={type:"param",value:e[1]}),t=e}function va(){var r,t,e;return r=Co,qa()!==u&&Bs()!==u&&ka()!==u&&Bs()!==u&&(t=_s())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u?((e=Du())===u&&(e=null),e!==u&&Bs()!==u&&$s()!==u?(r,r={type:"on update",keyword:t,parentheses:!0,expr:e}):(Co=r,r=u)):(Co=r,r=u),r===u&&(r=Co,qa()!==u&&Bs()!==u&&ka()!==u&&Bs()!==u&&(t=_s())!==u?(r,r=function(r){return{type:"on update",keyword:r}}(t)):(Co=r,r=u)),r}function ba(){var t,e,n,o,a;return t=Co,"over"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(tt)),e!==u&&Bs()!==u&&(n=Eu())!==u?(t,t=e={type:"window",as_window_specification:n}):(Co=t,t=u),t===u&&(t=Co,"over"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(tt)),e!==u&&Bs()!==u&&(n=Fs())!==u&&Bs()!==u?((o=_u())===u&&(o=null),o!==u&&Bs()!==u?((a=ju())===u&&(a=null),a!==u&&Bs()!==u&&$s()!==u?(t,t=e={partitionby:o,orderby:a}):(Co=t,t=u)):(Co=t,t=u)):(Co=t,t=u),t===u&&(t=va())),t}function da(){var t;return(t=function(){var t,e,n,o;t=Co,"current_date"===r.substr(Co,12).toLowerCase()?(e=r.substr(Co,12),Co+=12):(e=u,0===So&&Io(An));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="CURRENT_DATE"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=Co,"current_time"===r.substr(Co,12).toLowerCase()?(e=r.substr(Co,12),Co+=12):(e=u,0===So&&Io(Nn));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="CURRENT_TIME"):(Co=t,t=u)):(Co=t,t=u);return t}())===u&&(t=_s()),t}function ya(){var t;return(t=ma())===u&&(t=Ea())===u&&(t=function(){var t,e;t=Co,(e=function(){var t,e,n,o;t=Co,"true"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Ot));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(t,e={type:"bool",value:!0});(t=e)===u&&(t=Co,(e=function(){var t,e,n,o;t=Co,"false"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(Mt));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(t,e={type:"bool",value:!1}),t=e);return t}())===u&&(t=ha())===u&&(t=function(){var t,e,n,o,a,s;t=Co,(e=Ts())===u&&(e=As())===u&&(e=js())===u&&(e=gs());if(e!==u)if(Bs()!==u){if(n=Co,39===r.charCodeAt(Co)?(o="'",Co++):(o=u,0===So&&Io(z)),o!==u){for(a=[],s=La();s!==u;)a.push(s),s=La();a!==u?(39===r.charCodeAt(Co)?(s="'",Co++):(s=u,0===So&&Io(z)),s!==u?n=o=[o,a,s]:(Co=n,n=u)):(Co=n,n=u)}else Co=n,n=u;n!==u?(t,e=nt(e,n),t=e):(Co=t,t=u)}else Co=t,t=u;else Co=t,t=u;if(t===u)if(t=Co,(e=Ts())===u&&(e=As())===u&&(e=js())===u&&(e=gs()),e!==u)if(Bs()!==u){if(n=Co,34===r.charCodeAt(Co)?(o='"',Co++):(o=u,0===So&&Io(Pr)),o!==u){for(a=[],s=wa();s!==u;)a.push(s),s=wa();a!==u?(34===r.charCodeAt(Co)?(s='"',Co++):(s=u,0===So&&Io(Pr)),s!==u?n=o=[o,a,s]:(Co=n,n=u)):(Co=n,n=u)}else Co=n,n=u;n!==u?(t,e=nt(e,n),t=e):(Co=t,t=u)}else Co=t,t=u;else Co=t,t=u;return t}()),t}function ha(){var t,e;return t=Co,(e=function(){var t,e,n,o;t=Co,"null"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(kt));e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u);return t}())!==u&&(t,e={type:"null",value:null}),t=e}function ma(){var t,e,n,o,a;if(t=Co,e=Co,39===r.charCodeAt(Co)?(n="'",Co++):(n=u,0===So&&Io(z)),n!==u){for(o=[],a=La();a!==u;)o.push(a),a=La();o!==u?(39===r.charCodeAt(Co)?(a="'",Co++):(a=u,0===So&&Io(z)),a!==u?e=n=[n,o,a]:(Co=e,e=u)):(Co=e,e=u)}else Co=e,e=u;if(e!==u&&(t,e={type:"single_quote_string",value:e[1].join("")}),(t=e)===u){if(t=Co,e=Co,34===r.charCodeAt(Co)?(n='"',Co++):(n=u,0===So&&Io(Pr)),n!==u){for(o=[],a=wa();a!==u;)o.push(a),a=wa();o!==u?(34===r.charCodeAt(Co)?(a='"',Co++):(a=u,0===So&&Io(Pr)),a!==u?e=n=[n,o,a]:(Co=e,e=u)):(Co=e,e=u)}else Co=e,e=u;e!==u&&(t,e=function(r){return{type:"double_quote_string",value:r[1].join("")}}(e)),t=e}return t}function wa(){var t;return ot.test(r.charAt(Co))?(t=r.charAt(Co),Co++):(t=u,0===So&&Io(ut)),t===u&&(t=Ca()),t}function La(){var t;return at.test(r.charAt(Co))?(t=r.charAt(Co),Co++):(t=u,0===So&&Io(st)),t===u&&(t=Ca()),t}function Ca(){var t,e,n,o,a,s,i,c,l,f;return t=Co,"\\'"===r.substr(Co,2)?(e="\\'",Co+=2):(e=u,0===So&&Io(it)),e!==u&&(t,e="\\'"),(t=e)===u&&(t=Co,'\\"'===r.substr(Co,2)?(e='\\"',Co+=2):(e=u,0===So&&Io(ct)),e!==u&&(t,e='\\"'),(t=e)===u&&(t=Co,"\\\\"===r.substr(Co,2)?(e="\\\\",Co+=2):(e=u,0===So&&Io(lt)),e!==u&&(t,e="\\\\"),(t=e)===u&&(t=Co,"\\/"===r.substr(Co,2)?(e="\\/",Co+=2):(e=u,0===So&&Io(ft)),e!==u&&(t,e="\\/"),(t=e)===u&&(t=Co,"\\b"===r.substr(Co,2)?(e="\\b",Co+=2):(e=u,0===So&&Io(pt)),e!==u&&(t,e="\b"),(t=e)===u&&(t=Co,"\\f"===r.substr(Co,2)?(e="\\f",Co+=2):(e=u,0===So&&Io(vt)),e!==u&&(t,e="\f"),(t=e)===u&&(t=Co,"\\n"===r.substr(Co,2)?(e="\\n",Co+=2):(e=u,0===So&&Io(bt)),e!==u&&(t,e="\n"),(t=e)===u&&(t=Co,"\\r"===r.substr(Co,2)?(e="\\r",Co+=2):(e=u,0===So&&Io(dt)),e!==u&&(t,e="\r"),(t=e)===u&&(t=Co,"\\t"===r.substr(Co,2)?(e="\\t",Co+=2):(e=u,0===So&&Io(yt)),e!==u&&(t,e="\t"),(t=e)===u&&(t=Co,"\\u"===r.substr(Co,2)?(e="\\u",Co+=2):(e=u,0===So&&Io(ht)),e!==u&&(n=_a())!==u&&(o=_a())!==u&&(a=_a())!==u&&(s=_a())!==u?(t,i=n,c=o,l=a,f=s,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(Co=t,t=u),t===u&&(t=Co,92===r.charCodeAt(Co)?(e="\\",Co++):(e=u,0===So&&Io(mt)),e!==u&&(t,e="\\"),(t=e)===u&&(t=Co,"''"===r.substr(Co,2)?(e="''",Co+=2):(e=u,0===So&&Io(wt)),e!==u&&(t,e="''"),(t=e)===u&&(t=Co,'""'===r.substr(Co,2)?(e='""',Co+=2):(e=u,0===So&&Io(Lt)),e!==u&&(t,e='""'),(t=e)===u&&(t=Co,"``"===r.substr(Co,2)?(e="``",Co+=2):(e=u,0===So&&Io(Ct)),e!==u&&(t,e="``"),t=e))))))))))))),t}function Ea(){var r,t,e;return r=Co,(t=function(){var r,t,e,n;r=Co,(t=Aa())!==u&&(e=ga())!==u&&(n=Sa())!==u?(r,r=t={type:"bigint",value:t+e+n}):(Co=r,r=u);r===u&&(r=Co,(t=Aa())!==u&&(e=ga())!==u?(r,t=function(r,t){const e=r+t;return bi(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e),r=t):(Co=r,r=u),r===u&&(r=Co,(t=Aa())!==u&&(e=Sa())!==u?(r,t=function(r,t){return{type:"bigint",value:r+t}}(t,e),r=t):(Co=r,r=u),r===u&&(r=Co,(t=Aa())!==u&&(r,t=function(r){return bi(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t)));return r}())!==u&&(r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function Aa(){var t,e,n;return(t=Ta())===u&&(t=ja())===u&&(t=Co,45===r.charCodeAt(Co)?(e="-",Co++):(e=u,0===So&&Io(Rr)),e===u&&(43===r.charCodeAt(Co)?(e="+",Co++):(e=u,0===So&&Io(kr))),e!==u&&(n=Ta())!==u?(t,t=e=e+n):(Co=t,t=u),t===u&&(t=Co,45===r.charCodeAt(Co)?(e="-",Co++):(e=u,0===So&&Io(Rr)),e===u&&(43===r.charCodeAt(Co)?(e="+",Co++):(e=u,0===So&&Io(kr))),e!==u&&(n=ja())!==u?(t,t=e=function(r,t){return r+t}(e,n)):(Co=t,t=u))),t}function ga(){var t,e,n;return t=Co,46===r.charCodeAt(Co)?(e=".",Co++):(e=u,0===So&&Io(sr)),e!==u&&(n=Ta())!==u?(t,t=e="."+n):(Co=t,t=u),t}function Sa(){var t,e,n;return t=Co,(e=function(){var t,e,n;t=Co,_t.test(r.charAt(Co))?(e=r.charAt(Co),Co++):(e=u,0===So&&Io(xt));e!==u?(It.test(r.charAt(Co))?(n=r.charAt(Co),Co++):(n=u,0===So&&Io(Nt)),n===u&&(n=null),n!==u?(t,t=e=e+(null!==(o=n)?o:"")):(Co=t,t=u)):(Co=t,t=u);var o;return t}())!==u&&(n=Ta())!==u?(t,t=e=e+n):(Co=t,t=u),t}function Ta(){var r,t,e;if(r=Co,t=[],(e=ja())!==u)for(;e!==u;)t.push(e),e=ja();else t=u;return t!==u&&(r,t=t.join("")),r=t}function ja(){var t;return gt.test(r.charAt(Co))?(t=r.charAt(Co),Co++):(t=u,0===So&&Io(St)),t}function _a(){var t;return Tt.test(r.charAt(Co))?(t=r.charAt(Co),Co++):(t=u,0===So&&Io(jt)),t}function xa(){var t,e,n,o;return t=Co,"default"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(m)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Ia(){var t,e,n,o;return t=Co,"to"===r.substr(Co,2).toLowerCase()?(e=r.substr(Co,2),Co+=2):(e=u,0===So&&Io(Ut)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Na(){var t,e,n,o;return t=Co,"drop"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Dt)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="DROP"):(Co=t,t=u)):(Co=t,t=u),t}function ka(){var t,e,n,o;return t=Co,"update"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Ht)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Ra(){var t,e,n,o;return t=Co,"create"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Wt)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Oa(){var t,e,n,o;return t=Co,"temporary"===r.substr(Co,9).toLowerCase()?(e=r.substr(Co,9),Co+=9):(e=u,0===So&&Io(Gt)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Ua(){var t,e,n,o;return t=Co,"delete"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(qt)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Ma(){var t,e,n,o;return t=Co,"replace"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(Vt)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Da(){var t,e,n,o;return t=Co,"rename"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Qt)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Pa(){var t,e,n,o;return t=Co,"partition"===r.substr(Co,9).toLowerCase()?(e=r.substr(Co,9),Co+=9):(e=u,0===So&&Io(Kt)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="PARTITION"):(Co=t,t=u)):(Co=t,t=u),t}function Fa(){var t,e,n,o;return t=Co,"into"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(zt)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="INTO"):(Co=t,t=u)):(Co=t,t=u),t}function $a(){var t,e,n,o;return t=Co,"set"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(H)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="SET"):(Co=t,t=u)):(Co=t,t=u),t}function Ha(){var t,e,n,o;return t=Co,"as"===r.substr(Co,2).toLowerCase()?(e=r.substr(Co,2),Co+=2):(e=u,0===So&&Io(te)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Wa(){var t,e,n,o;return t=Co,"table"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(ee)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="TABLE"):(Co=t,t=u)):(Co=t,t=u),t}function Ga(){var t,e,n,o;return t=Co,"tables"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(ne)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="TABLES"):(Co=t,t=u)):(Co=t,t=u),t}function qa(){var t,e,n,o;return t=Co,"on"===r.substr(Co,2).toLowerCase()?(e=r.substr(Co,2),Co+=2):(e=u,0===So&&Io(ae)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Ba(){var t,e,n,o;return t=Co,"join"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(pe)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Ya(){var t,e,n,o;return t=Co,"outer"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(ve)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Va(){var t,e,n,o;return t=Co,"values"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(de)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Qa(){var t,e,n,o;return t=Co,"using"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(ye)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Xa(){var t,e,n,o;return t=Co,"with"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(lr)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function Ka(){var t,e,n,o;return t=Co,"by"===r.substr(Co,2).toLowerCase()?(e=r.substr(Co,2),Co+=2):(e=u,0===So&&Io(we)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function za(){var t,e,n,o;return t=Co,"all"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(Te)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="ALL"):(Co=t,t=u)):(Co=t,t=u),t}function Za(){var t,e,n,o;return t=Co,"distinct"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(je)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="DISTINCT"):(Co=t,t=u)):(Co=t,t=u),t}function Ja(){var t,e,n,o;return t=Co,"between"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(_e)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="BETWEEN"):(Co=t,t=u)):(Co=t,t=u),t}function rs(){var t,e,n,o;return t=Co,"in"===r.substr(Co,2).toLowerCase()?(e=r.substr(Co,2),Co+=2):(e=u,0===So&&Io(xe)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="IN"):(Co=t,t=u)):(Co=t,t=u),t}function ts(){var t,e,n,o;return t=Co,"is"===r.substr(Co,2).toLowerCase()?(e=r.substr(Co,2),Co+=2):(e=u,0===So&&Io(Ie)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="IS"):(Co=t,t=u)):(Co=t,t=u),t}function es(){var t,e,n,o;return t=Co,"like"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Ne)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="LIKE"):(Co=t,t=u)):(Co=t,t=u),t}function ns(){var t,e,n,o;return t=Co,"rlike"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(ke)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="RLIKE"):(Co=t,t=u)):(Co=t,t=u),t}function os(){var t,e,n,o;return t=Co,"exists"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Re)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="EXISTS"):(Co=t,t=u)):(Co=t,t=u),t}function us(){var t,e,n,o;return t=Co,"not"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(Oe)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="NOT"):(Co=t,t=u)):(Co=t,t=u),t}function as(){var t,e,n,o;return t=Co,"and"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(Ue)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="AND"):(Co=t,t=u)):(Co=t,t=u),t}function ss(){var t,e,n,o;return t=Co,"or"===r.substr(Co,2).toLowerCase()?(e=r.substr(Co,2),Co+=2):(e=u,0===So&&Io(Me)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="OR"):(Co=t,t=u)):(Co=t,t=u),t}function is(){var t,e,n,o;return t=Co,"case"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Ge)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function cs(){var t,e,n,o;return t=Co,"end"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(Ve)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?t=e=[e,n]:(Co=t,t=u)):(Co=t,t=u),t}function ls(){var t,e,n,o;return t=Co,"cast"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Qe)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="CAST"):(Co=t,t=u)):(Co=t,t=u),t}function fs(){var t,e,n,o;return t=Co,"char"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(Xe)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="CHAR"):(Co=t,t=u)):(Co=t,t=u),t}function ps(){var t,e,n,o;return t=Co,"varchar"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(Ke)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="VARCHAR"):(Co=t,t=u)):(Co=t,t=u),t}function vs(){var t,e,n,o;return t=Co,"numeric"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(ze)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="NUMERIC"):(Co=t,t=u)):(Co=t,t=u),t}function bs(){var t,e,n,o;return t=Co,"decimal"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(Ze)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="DECIMAL"):(Co=t,t=u)):(Co=t,t=u),t}function ds(){var t,e,n,o;return t=Co,"unsigned"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(tn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="UNSIGNED"):(Co=t,t=u)):(Co=t,t=u),t}function ys(){var t,e,n,o;return t=Co,"int"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(en)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="INT"):(Co=t,t=u)):(Co=t,t=u),t}function hs(){var t,e,n,o;return t=Co,"integer"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(on)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="INTEGER"):(Co=t,t=u)):(Co=t,t=u),t}function ms(){var t,e,n,o;return t=Co,"smallint"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(an)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="SMALLINT"):(Co=t,t=u)):(Co=t,t=u),t}function ws(){var t,e,n,o;return t=Co,"tinyint"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(sn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="TINYINT"):(Co=t,t=u)):(Co=t,t=u),t}function Ls(){var t,e,n,o;return t=Co,"bigint"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(vn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="BIGINT"):(Co=t,t=u)):(Co=t,t=u),t}function Cs(){var t,e,n,o;return t=Co,"float"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(bn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="FLOAT"):(Co=t,t=u)):(Co=t,t=u),t}function Es(){var t,e,n,o;return t=Co,"double"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(dn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="DOUBLE"):(Co=t,t=u)):(Co=t,t=u),t}function As(){var t,e,n,o;return t=Co,"date"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(yn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="DATE"):(Co=t,t=u)):(Co=t,t=u),t}function gs(){var t,e,n,o;return t=Co,"datetime"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(hn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="DATETIME"):(Co=t,t=u)):(Co=t,t=u),t}function Ss(){var t,e,n,o;return t=Co,"rows"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(mn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="ROWS"):(Co=t,t=u)):(Co=t,t=u),t}function Ts(){var t,e,n,o;return t=Co,"time"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(wn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="TIME"):(Co=t,t=u)):(Co=t,t=u),t}function js(){var t,e,n,o;return t=Co,"timestamp"===r.substr(Co,9).toLowerCase()?(e=r.substr(Co,9),Co+=9):(e=u,0===So&&Io(Ln)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="TIMESTAMP"):(Co=t,t=u)):(Co=t,t=u),t}function _s(){var t,e,n,o;return t=Co,"current_timestamp"===r.substr(Co,17).toLowerCase()?(e=r.substr(Co,17),Co+=17):(e=u,0===So&&Io(kn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="CURRENT_TIMESTAMP"):(Co=t,t=u)):(Co=t,t=u),t}function xs(){var t;return(t=function(){var t;return"@@"===r.substr(Co,2)?(t="@@",Co+=2):(t=u,0===So&&Io(Hn)),t}())===u&&(t=function(){var t;return 64===r.charCodeAt(Co)?(t="@",Co++):(t=u,0===So&&Io($n)),t}())===u&&(t=function(){var t;return 36===r.charCodeAt(Co)?(t="$",Co++):(t=u,0===So&&Io(Wn)),t}()),t}function Is(){var t;return 61===r.charCodeAt(Co)?(t="=",Co++):(t=u,0===So&&Io(Lr)),t}function Ns(){var t,e,n,o;return t=Co,"add"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(Yn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="ADD"):(Co=t,t=u)):(Co=t,t=u),t}function ks(){var t,e,n,o;return t=Co,"column"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(Vn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="COLUMN"):(Co=t,t=u)):(Co=t,t=u),t}function Rs(){var t,e,n,o;return t=Co,"index"===r.substr(Co,5).toLowerCase()?(e=r.substr(Co,5),Co+=5):(e=u,0===So&&Io(Qn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="INDEX"):(Co=t,t=u)):(Co=t,t=u),t}function Os(){var t,e,n,o;return t=Co,"key"===r.substr(Co,3).toLowerCase()?(e=r.substr(Co,3),Co+=3):(e=u,0===So&&Io(v)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="KEY"):(Co=t,t=u)):(Co=t,t=u),t}function Us(){var t,e,n,o;return t=Co,"comment"===r.substr(Co,7).toLowerCase()?(e=r.substr(Co,7),Co+=7):(e=u,0===So&&Io(zn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="COMMENT"):(Co=t,t=u)):(Co=t,t=u),t}function Ms(){var t;return 46===r.charCodeAt(Co)?(t=".",Co++):(t=u,0===So&&Io(sr)),t}function Ds(){var t;return 44===r.charCodeAt(Co)?(t=",",Co++):(t=u,0===So&&Io(ao)),t}function Ps(){var t;return 42===r.charCodeAt(Co)?(t="*",Co++):(t=u,0===So&&Io(Or)),t}function Fs(){var t;return 40===r.charCodeAt(Co)?(t="(",Co++):(t=u,0===So&&Io(ur)),t}function $s(){var t;return 41===r.charCodeAt(Co)?(t=")",Co++):(t=u,0===So&&Io(ar)),t}function Hs(){var t;return 91===r.charCodeAt(Co)?(t="[",Co++):(t=u,0===So&&Io(so)),t}function Ws(){var t;return 93===r.charCodeAt(Co)?(t="]",Co++):(t=u,0===So&&Io(io)),t}function Gs(){var t;return 59===r.charCodeAt(Co)?(t=";",Co++):(t=u,0===So&&Io(co)),t}function qs(){var t;return(t=function(){var t;return"||"===r.substr(Co,2)?(t="||",Co+=2):(t=u,0===So&&Io(lo)),t}())===u&&(t=function(){var t;return"&&"===r.substr(Co,2)?(t="&&",Co+=2):(t=u,0===So&&Io(fo)),t}()),t}function Bs(){var r,t;for(r=[],(t=Ks())===u&&(t=Vs());t!==u;)r.push(t),(t=Ks())===u&&(t=Vs());return r}function Ys(){var r,t;if(r=[],(t=Ks())===u&&(t=Vs()),t!==u)for(;t!==u;)r.push(t),(t=Ks())===u&&(t=Vs());else r=u;return r}function Vs(){var t;return(t=function(){var t,e,n,o,a,s;t=Co,"/*"===r.substr(Co,2)?(e="/*",Co+=2):(e=u,0===So&&Io(po));if(e!==u){for(n=[],o=Co,a=Co,So++,"*/"===r.substr(Co,2)?(s="*/",Co+=2):(s=u,0===So&&Io(vo)),So--,s===u?a=void 0:(Co=a,a=u),a!==u&&(s=Xs())!==u?o=a=[a,s]:(Co=o,o=u);o!==u;)n.push(o),o=Co,a=Co,So++,"*/"===r.substr(Co,2)?(s="*/",Co+=2):(s=u,0===So&&Io(vo)),So--,s===u?a=void 0:(Co=a,a=u),a!==u&&(s=Xs())!==u?o=a=[a,s]:(Co=o,o=u);n!==u?("*/"===r.substr(Co,2)?(o="*/",Co+=2):(o=u,0===So&&Io(vo)),o!==u?t=e=[e,n,o]:(Co=t,t=u)):(Co=t,t=u)}else Co=t,t=u;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=Co,"--"===r.substr(Co,2)?(e="--",Co+=2):(e=u,0===So&&Io(bo));if(e!==u){for(n=[],o=Co,a=Co,So++,s=zs(),So--,s===u?a=void 0:(Co=a,a=u),a!==u&&(s=Xs())!==u?o=a=[a,s]:(Co=o,o=u);o!==u;)n.push(o),o=Co,a=Co,So++,s=zs(),So--,s===u?a=void 0:(Co=a,a=u),a!==u&&(s=Xs())!==u?o=a=[a,s]:(Co=o,o=u);n!==u?t=e=[e,n]:(Co=t,t=u)}else Co=t,t=u;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=Co,35===r.charCodeAt(Co)?(e="#",Co++):(e=u,0===So&&Io(yo));if(e!==u){for(n=[],o=Co,a=Co,So++,s=zs(),So--,s===u?a=void 0:(Co=a,a=u),a!==u&&(s=Xs())!==u?o=a=[a,s]:(Co=o,o=u);o!==u;)n.push(o),o=Co,a=Co,So++,s=zs(),So--,s===u?a=void 0:(Co=a,a=u),a!==u&&(s=Xs())!==u?o=a=[a,s]:(Co=o,o=u);n!==u?t=e=[e,n]:(Co=t,t=u)}else Co=t,t=u;return t}()),t}function Qs(){var r,t,e,n,o,a,s;return r=Co,(t=Us())!==u&&Bs()!==u?((e=Is())===u&&(e=null),e!==u&&Bs()!==u&&(n=ma())!==u?(r,a=e,s=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:a,value:s}):(Co=r,r=u)):(Co=r,r=u),r}function Xs(){var t;return r.length>Co?(t=r.charAt(Co),Co++):(t=u,0===So&&Io(ho)),t}function Ks(){var t;return mo.test(r.charAt(Co))?(t=r.charAt(Co),Co++):(t=u,0===So&&Io(wo)),t}function zs(){var t,e;if((t=function(){var t,e;t=Co,So++,r.length>Co?(e=r.charAt(Co),Co++):(e=u,0===So&&Io(ho));So--,e===u?t=void 0:(Co=t,t=u);return t}())===u)if(t=[],Et.test(r.charAt(Co))?(e=r.charAt(Co),Co++):(e=u,0===So&&Io(At)),e!==u)for(;e!==u;)t.push(e),Et.test(r.charAt(Co))?(e=r.charAt(Co),Co++):(e=u,0===So&&Io(At));else t=u;return t}function Zs(){var t,e;return t=Co,Co,wi=[],(!0?void 0:u)!==u&&Bs()!==u?((e=Js())===u&&(e=function(){var t,e;t=Co,function(){var t;return"return"===r.substr(Co,6).toLowerCase()?(t=r.substr(Co,6),Co+=6):(t=u,0===So&&Io(Gn)),t}()!==u&&Bs()!==u&&(e=ri())!==u?(t,t={type:"return",expr:e}):(Co=t,t=u);return t}()),e!==u?(t,t={stmt:e,vars:wi}):(Co=t,t=u)):(Co=t,t=u),t}function Js(){var t,e,n,o;return t=Co,(e=si())===u&&(e=ii()),e!==u&&Bs()!==u?((n=function(){var t;return":="===r.substr(Co,2)?(t=":=",Co+=2):(t=u,0===So&&Io(qn)),t}())===u&&(n=Is()),n!==u&&Bs()!==u&&(o=ri())!==u?(t,t=e={type:"assign",left:e,symbol:n,right:o}):(Co=t,t=u)):(Co=t,t=u),t}function ri(){var r;return(r=Zo())===u&&(r=function(){var r,t,e,n,o;r=Co,(t=si())!==u&&Bs()!==u&&(e=hu())!==u&&Bs()!==u&&(n=si())!==u&&Bs()!==u&&(o=Lu())!==u?(r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(Co=r,r=u);return r}())===u&&(r=ti())===u&&(r=function(){var r,t;r=Co,Hs()!==u&&Bs()!==u&&(t=ai())!==u&&Bs()!==u&&Ws()!==u?(r,r={type:"array",value:t}):(Co=r,r=u);return r}()),r}function ti(){var r,t,e,n,o,a,s,i;if(r=Co,(t=ei())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Zu())!==u&&(s=Bs())!==u&&(i=ei())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Zu())!==u&&(s=Bs())!==u&&(i=ei())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,r=t=Cr(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function ei(){var r,t,e,n,o,a,s,i;if(r=Co,(t=ni())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=ra())!==u&&(s=Bs())!==u&&(i=ni())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=ra())!==u&&(s=Bs())!==u&&(i=ni())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,r=t=Cr(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function ni(){var r,t,e;return(r=ya())===u&&(r=si())===u&&(r=ui())===u&&(r=pa())===u&&(r=Co,Fs()!==u&&Bs()!==u&&(t=ti())!==u&&Bs()!==u&&$s()!==u?(r,(e=t).parentheses=!0,r=e):(Co=r,r=u)),r}function oi(){var r,t,e,n,o,a,s;return r=Co,(t=ia())!==u?(e=Co,(n=Bs())!==u&&(o=Ms())!==u&&(a=Bs())!==u&&(s=ia())!==u?e=n=[n,o,a,s]:(Co=e,e=u),e===u&&(e=null),e!==u?(r,r=t=function(r,t){let e=r;return null!==t&&(e=`${r}.${t[3]}`),e}(t,e)):(Co=r,r=u)):(Co=r,r=u),r}function ui(){var r,t,e;return r=Co,(t=oi())!==u&&Bs()!==u&&Fs()!==u&&Bs()!==u?((e=ai())===u&&(e=null),e!==u&&Bs()!==u&&$s()!==u?(r,r=t={type:"function",name:t,args:{type:"expr_list",value:e}}):(Co=r,r=u)):(Co=r,r=u),r===u&&(r=Co,(t=oi())!==u&&(r,t=function(r){return{type:"function",name:r,args:null}}(t)),r=t),r}function ai(){var r,t,e,n,o,a,s,i;if(r=Co,(t=ni())!==u){for(e=[],n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=ni())!==u?n=o=[o,a,s,i]:(Co=n,n=u);n!==u;)e.push(n),n=Co,(o=Bs())!==u&&(a=Ds())!==u&&(s=Bs())!==u&&(i=ni())!==u?n=o=[o,a,s,i]:(Co=n,n=u);e!==u?(r,r=t=l(t,e)):(Co=r,r=u)}else Co=r,r=u;return r}function si(){var r,t,e,n,o;return r=Co,(t=xs())!==u&&(e=ii())!==u?(r,n=t,o=e,r=t={type:"var",...o,prefix:n}):(Co=r,r=u),r}function ii(){var t,e,n,o,a;return t=Co,(e=ia())!==u&&(n=function(){var t,e,n,o,a;t=Co,e=[],n=Co,46===r.charCodeAt(Co)?(o=".",Co++):(o=u,0===So&&Io(sr));o!==u&&(a=ia())!==u?n=o=[o,a]:(Co=n,n=u);for(;n!==u;)e.push(n),n=Co,46===r.charCodeAt(Co)?(o=".",Co++):(o=u,0===So&&Io(sr)),o!==u&&(a=ia())!==u?n=o=[o,a]:(Co=n,n=u);e!==u&&(t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==u?(t,o=e,a=n,wi.push(o),t=e={type:"var",name:o,members:a,prefix:null}):(Co=t,t=u),t===u&&(t=Co,(e=Ea())!==u&&(t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function ci(){var t;return(t=function(){var t,e,n,o;t=Co,(e=fs())===u&&(e=ps());if(e!==u)if(Bs()!==u)if(Fs()!==u)if(Bs()!==u){if(n=[],gt.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io(St)),o!==u)for(;o!==u;)n.push(o),gt.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io(St));else n=u;n!==u&&(o=Bs())!==u&&$s()!==u?(t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(Co=t,t=u)}else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;t===u&&(t=Co,(e=fs())===u&&(e=ps())===u&&(e=function(){var t,e,n,o;return t=Co,"string"===r.substr(Co,6).toLowerCase()?(e=r.substr(Co,6),Co+=6):(e=u,0===So&&Io(rn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="STRING"):(Co=t,t=u)):(Co=t,t=u),t}()),e!==u&&(t,e=Lo(e)),t=e);return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c,l,f,p,v;t=Co,(e=vs())===u&&(e=bs())===u&&(e=ys())===u&&(e=hs())===u&&(e=ms())===u&&(e=ws())===u&&(e=Ls())===u&&(e=Cs())===u&&(e=Es());if(e!==u)if((n=Bs())!==u)if((o=Fs())!==u)if((a=Bs())!==u){if(s=[],gt.test(r.charAt(Co))?(i=r.charAt(Co),Co++):(i=u,0===So&&Io(St)),i!==u)for(;i!==u;)s.push(i),gt.test(r.charAt(Co))?(i=r.charAt(Co),Co++):(i=u,0===So&&Io(St));else s=u;if(s!==u)if((i=Bs())!==u){if(c=Co,(l=Ds())!==u)if((f=Bs())!==u){if(p=[],gt.test(r.charAt(Co))?(v=r.charAt(Co),Co++):(v=u,0===So&&Io(St)),v!==u)for(;v!==u;)p.push(v),gt.test(r.charAt(Co))?(v=r.charAt(Co),Co++):(v=u,0===So&&Io(St));else p=u;p!==u?c=l=[l,f,p]:(Co=c,c=u)}else Co=c,c=u;else Co=c,c=u;c===u&&(c=null),c!==u&&(l=Bs())!==u&&(f=$s())!==u&&(p=Bs())!==u?((v=li())===u&&(v=null),v!==u?(t,b=c,d=v,e={dataType:e,length:parseInt(s.join(""),10),scale:b&&parseInt(b[2].join(""),10),parentheses:!0,suffix:d},t=e):(Co=t,t=u)):(Co=t,t=u)}else Co=t,t=u;else Co=t,t=u}else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;var b,d;if(t===u){if(t=Co,(e=vs())===u&&(e=bs())===u&&(e=ys())===u&&(e=hs())===u&&(e=ms())===u&&(e=ws())===u&&(e=Ls())===u&&(e=Cs())===u&&(e=Es()),e!==u){if(n=[],gt.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io(St)),o!==u)for(;o!==u;)n.push(o),gt.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io(St));else n=u;n!==u&&(o=Bs())!==u?((a=li())===u&&(a=null),a!==u?(t,e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,a),t=e):(Co=t,t=u)):(Co=t,t=u)}else Co=t,t=u;t===u&&(t=Co,(e=vs())===u&&(e=bs())===u&&(e=ys())===u&&(e=hs())===u&&(e=ms())===u&&(e=ws())===u&&(e=Ls())===u&&(e=Cs())===u&&(e=Es()),e!==u&&(n=Bs())!==u?((o=li())===u&&(o=null),o!==u&&(a=Bs())!==u?(t,e=function(r,t){return{dataType:r,suffix:t}}(e,o),t=e):(Co=t,t=u)):(Co=t,t=u))}return t}())===u&&(t=function(){var t,e,n,o;t=Co,(e=As())===u&&(e=gs())===u&&(e=Ts())===u&&(e=js());if(e!==u)if(Bs()!==u)if(Fs()!==u)if(Bs()!==u){if(n=[],gt.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io(St)),o!==u)for(;o!==u;)n.push(o),gt.test(r.charAt(Co))?(o=r.charAt(Co),Co++):(o=u,0===So&&Io(St));else n=u;n!==u&&(o=Bs())!==u&&$s()!==u?(t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(Co=t,t=u)}else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;else Co=t,t=u;t===u&&(t=Co,(e=As())===u&&(e=gs())===u&&(e=Ts())===u&&(e=js()),e!==u&&(t,e=function(r){return{dataType:r}}(e)),t=e);return t}())===u&&(t=function(){var t,e;t=Co,(e=function(){var t,e,n,o;return t=Co,"json"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(un)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="JSON"):(Co=t,t=u)):(Co=t,t=u),t}())!==u&&(t,e=Lo(e));return t=e}())===u&&(t=function(){var t,e;t=Co,(e=function(){var t,e,n,o;return t=Co,"tinytext"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(cn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="TINYTEXT"):(Co=t,t=u)):(Co=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Co,"text"===r.substr(Co,4).toLowerCase()?(e=r.substr(Co,4),Co+=4):(e=u,0===So&&Io(ln)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="TEXT"):(Co=t,t=u)):(Co=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Co,"mediumtext"===r.substr(Co,10).toLowerCase()?(e=r.substr(Co,10),Co+=10):(e=u,0===So&&Io(fn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="MEDIUMTEXT"):(Co=t,t=u)):(Co=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=Co,"longtext"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(pn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="LONGTEXT"):(Co=t,t=u)):(Co=t,t=u),t}());e!==u&&(t,e={dataType:e});return t=e}()),t}function li(){var t,e,n;return t=Co,(e=ds())===u&&(e=null),e!==u&&Bs()!==u?((n=function(){var t,e,n,o;return t=Co,"zerofill"===r.substr(Co,8).toLowerCase()?(e=r.substr(Co,8),Co+=8):(e=u,0===So&&Io(nn)),e!==u?(n=Co,So++,o=ca(),So--,o===u?n=void 0:(Co=n,n=u),n!==u?(t,t=e="ZEROFILL"):(Co=t,t=u)):(Co=t,t=u),t}())===u&&(n=null),n!==u?(t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):(Co=t,t=u)):(Co=t,t=u),t}const fi={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CROSS:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,FALSE:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,KEY:!0,LEFT:!0,LIKE:!0,LIMIT:!0,LOW_PRIORITY:!0,NOT:!0,NULL:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,READ:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,UNION:!0,UPDATE:!0,USING:!0,VALUES:!0,WITH:!0,WHEN:!0,WHERE:!0,WRITE:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0};function pi(r,t){return{type:"unary_expr",operator:r,expr:t}}function vi(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e}}function bi(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function di(r,t,e=3){const n=[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function yi(r,t){let e=r;for(let r=0;r<t.length;r++)e=vi(t[r][1],e,t[r][3]);return e}function hi(r){const t=Ei[r];return t||(r||null)}function mi(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=hi(r[1])),t.add(r.join("::"))}return Array.from(t)}let wi=[];const Li=new Set,Ci=new Set,Ei={};if((e=s())!==u&&Co===r.length)return e;throw e!==u&&Co<r.length&&Io({type:"end"}),No(go,Ao<r.length?r.charAt(Ao):null,Ao<r.length?xo(Ao,Ao+1):xo(Ao,Ao))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Lt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return function(r){if(Array.isArray(r))return u(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return u(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return u(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function a(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),vr(e)]}function s(r){if(r){var t=r.type,e=r.expr,n=r.symbol,u=t.toUpperCase(),s=[];switch(s.push(u),u){case"KEY_BLOCK_SIZE":n&&s.push(n),s.push(dr(e));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,o(a(r)));break;case"WITH PARSER":s.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(Lr(r));break;case"DATA_COMPRESSION":s.push(n,vr(e.value),hr(e.on));break;default:s.push(n,dr(e))}return s.filter(br).join(" ")}}function i(r){return r?r.map(s):[]}function c(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,u=void 0===n?[]:n,s=r.definition,c=r.on,l=r.with,f=[];if(f.push.apply(f,o(a(e))),s&&s.length){var p="CHECK"===vr(t)?"(".concat(ut(s[0]),")"):"(".concat(s.map((function(r){return ut(r)})).join(", "),")");f.push(p)}return f.push(i(u).join(" ")),l&&f.push("WITH (".concat(i(l).join(", "),")")),c&&f.push("ON [".concat(c,"]")),f}function l(r){return function(r){if(Array.isArray(r))return f(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return f(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return f(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function p(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,u=r.keyword,a=r.reference_definition,s=[],i=sr().database;s.push(vr(u)),s.push(fr(t));var f=vr(e);return"sqlite"===i&&"UNIQUE KEY"===f&&(f="UNIQUE"),s.push(f),s.push("sqlite"!==i&&fr(o)),s.push.apply(s,l(c(r))),s.push.apply(s,l(V(a))),s.push(vr(n)),s.filter(br).join(" ")}}function v(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,st(e,"partition by"),st(n,"order by"),vr(o)].filter(br).join(" ")}(t),")")}function b(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(v(e))}function d(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,u=t?ut(t).join(", "):"",a=function(r){switch(vr(r)){case"NTH_VALUE":case"LEAD":case"LAG":return!1;default:return!0}}(e);return[e,"(",u,!a&&")",o&&" ",o,a&&")"].filter(br).join("")}function y(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,u=r.parentheses,a=vr(o);if("WINDOW"===a)return"OVER ".concat(v(t));if("ON UPDATE"===a){var s="".concat(vr(o)," ").concat(vr(n)),i=ut(e)||[];return u&&(s="".concat(s,"(").concat(i.join(", "),")")),s}throw new Error("unknown over type")}}function h(r){var t=r.arrows,e=void 0===t?[]:t,n=r.collate,o=r.target,u=r.expr,a=r.keyword,s=r.symbol,i=r.as,c=r.properties,l=void 0===c?[]:c,f=o.length,p=o.dataType,v=o.parentheses,b=o.quoted,d=o.scale,y=o.suffix,h="";null!=f&&(h=d?"".concat(f,", ").concat(d):f),v&&(h="(".concat(h,")")),y&&y.length&&(h+=" ".concat(y.join(" ")));var m=ut(u),w="::",L="";return"as"===s&&(m="".concat(vr(a),"(").concat(m),L=")",w=" ".concat(s.toUpperCase()," ")),L+=e.map((function(r,t){return er(r,dr,l[t])})).join(" "),i&&(L+=" AS ".concat(fr(i))),n&&(L+=" ".concat(pr(n).join(" "))),[m,w,b,p,b,function(r){if(!r||!r.array)return"";switch(r.array){case"one":return"[]";case"two":return"[][]"}}(o),h,L].filter(br).join("")}function m(r){var t=r.args,e=r.name,n=r.args_parentheses,o=r.parentheses,u=r.over,a=r.collate,s=r.suffix,i=pr(a).join(" "),c=y(u),l=ut(s);if(!t)return[e,c].filter(br).join(" ");var f=r.separator||", ";"TRIM"===vr(e)&&(f=" ");var p=[e];return p.push(!1===n?" ":"("),p.push(ut(t).join(f)),!1!==n&&p.push(")"),p=[p.join(""),l].filter(br).join(" "),[o?"(".concat(p,")"):p,i,c].filter(br).join(" ")}function w(r){var t=r.operator||r.op,e=ut(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},u=[ut(r.left),t,e,vr(o.type),ut(o.value)].filter(br).join(" ");return r.parentheses?"(".concat(u,")"):u}function L(r){return function(r){if(Array.isArray(r))return C(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return C(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return C(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function E(r){return r?[vr(r.prefix),ut(r.value),vr(r.suffix)]:[]}function A(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(L(E(n)),L(E(e))).filter(br).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?nr("OFFSET",ut(e[0])):nr("LIMIT",e.map(ut).join("".concat("offset"===t?" ":"").concat(vr(t)," ")))}(r):"";var t,e,n}function g(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(B).join(", "),")"):"";return"".concat("default"===t.type?fr(t.value):dr(t)).concat(o," AS (").concat(ut(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function S(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=vr(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?fr(e):ut(e))}return n.filter(br).join(" ")}}function T(r){var t=r.as_struct_val,e=r.columns,n=r.distinct,o=r.for,u=r.from,a=r.for_sys_time_as_of,s=void 0===a?{}:a,i=r.locking_read,c=r.groupby,l=r.having,f=r.into,p=void 0===f?{}:f,v=r.limit,b=r.options,d=r.orderby,y=r.parentheses_symbol,h=r.qualify,m=r.top,w=r.window,L=r.with,C=r.where,E=[g(L),"SELECT",vr(t)];E.push(cr(m)),Array.isArray(b)&&E.push(b.join(" ")),E.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[vr(t)];return e&&n.push("(".concat(e.map(B).join(", "),")")),n.filter(br).join(" ")}}(n),Z(e,u));var T=p.position,j="";T&&(j=er("INTO",S,p)),"column"===T&&E.push(j),E.push(er("FROM",H,u)),"from"===T&&E.push(j);var _=s||{},x=_.keyword,I=_.expr;E.push(er(x,ut,I)),E.push(er("WHERE",ut,C)),E.push(nr("GROUP BY",at(c).join(", "))),E.push(er("HAVING",ut,l)),E.push(er("QUALIFY",ut,h)),E.push(er("WINDOW",ut,w)),E.push(st(d,"order by")),E.push(A(v)),E.push(vr(i)),"end"===T&&E.push(j),E.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[vr(r.type),vr(e)];return t?"".concat(n.join(" "),"(").concat(ut(t),")"):n.join(" ")}}(o));var N=E.filter(br).join(" ");return y?"(".concat(N,")"):N}function j(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(!r)return;if("string"==typeof r)return _(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return _(r,t)}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function _(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function x(r){if(!r||0===r.length)return"";var t,e=[],n=j(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u=o.table,a=o.column,s=o.value,i=[[u,a].filter(br).map((function(r){return fr(r)})).join(".")],c="";s&&(c=ut(s),i.push("=",c)),e.push(i.filter(br).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function I(r){if("select"===r.type)return T(r);var t=r.map(ut);return"(".concat(t.join("), ("),")")}function N(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(fr).join(", "));else{var e=r.value;t.push(e.map(ut).join(", "))}return t.push(")"),t.filter(br).join("")}function k(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(B).join(", "),")")}}function R(r){var t=r.expr,e=r.keyword,n=t.type,o=[vr(e)];switch(n){case"origin":o.push(dr(t));break;case"update":o.push("UPDATE",er("SET",x,t.set),er("WHERE",ut,t.where))}return o.filter(br).join(" ")}function O(r){if(!r)return"";var t=r.action;return[k(r.target),R(t)].filter(br).join(" ")}function U(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,u=r.columns,a=r.conflict,s=r.values,i=r.where,c=r.on_duplicate_update,l=r.partition,f=r.returning,p=r.set,v=c||{},b=v.keyword,d=v.set,y=[vr(e),vr(o),H(t),N(l)];return Array.isArray(u)&&y.push("(".concat(u.map(fr).join(", "),")")),y.push(er(Array.isArray(s)?"VALUES":"",I,s)),y.push(er("ON CONFLICT",O,a)),y.push(er("SET",x,p)),y.push(er("WHERE",ut,i)),y.push(Er(f)),y.push(er(b,x,d)),y.filter(br).join(" ")}function M(r){var t=r.expr,e=r.unit;return["INTERVAL",ut(t),vr(e)].filter(br).join(" ")}function D(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(vr(t),"(").concat(n&&ut(n)||"",")"),er("AS",fr,e),er(vr(o&&o.keyword),fr,o&&o.as)].filter(br).join(" ")}function P(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,u=r.type,a=[ut(n),"FOR",B(e),w(o)],s=["".concat(vr(u),"(").concat(a.join(" "),")")];return t&&s.push("AS",fr(t)),s.join(" ")}(r);default:return""}}function F(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,u=r.parentheses,a=r.prefix,s=[];switch(t.toLowerCase()){case"forceseek":s.push(vr(t),"(".concat(fr(n)),"(".concat(o.map(ut).filter(br).join(", "),"))"));break;case"spatial_window_max_cells":s.push(vr(t),"=",ut(e));break;case"index":s.push(vr(a),vr(t),u?"(".concat(e.map(fr).join(", "),")"):"= ".concat(fr(e)));break;default:s.push(ut(e))}return s.filter(br).join(" ")}}function $(r){if("UNNEST"===vr(r.type))return D(r);var t=r.table,e=r.db,n=r.as,o=r.expr,u=r.operator,a=r.prefix,s=r.schema,i=r.server,c=r.tablesample,l=r.table_hint,f=fr(i),p=fr(e),v=fr(s),b=t&&fr(t);if(o)switch(o.type){case"values":var d=o.parentheses,y=o.values,h=o.prefix,m=[d&&"(","",d&&")"],w=I(y);h&&(w=w.split("(").slice(1).map((function(r){return"".concat(vr(h),"(").concat(r)})).join("")),m[1]="VALUES ".concat(w),b=m.filter(br).join("");break;case"tumble":b=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.size;return["TABLE(TUMBLE(TABLE",[fr(t.db),fr(t.table)].filter(br).join("."),"DESCRIPTOR(".concat(B(e),")"),"".concat(M(n),"))")].filter(br).join(" ")}(o);break;default:b=ut(o)}var L=[f,p,v,b=[vr(a),b].filter(br).join(" ")].filter(br).join(".");r.parentheses&&(L="(".concat(L,")"));var C=[L];if(c){var E=["TABLESAMPLE",ut(c.expr),dr(c.repeatable)].filter(br).join(" ");C.push(E)}return C.push(er("AS",fr,n),P(u)),l&&C.push(vr(l.keyword),"(".concat(l.expr.map(F).filter(br).join(", "),")")),C.filter(br).join(" ")}function H(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=H(t);return e?"(".concat(n,")"):n}var o=r[0],u=[];if("dual"===o.type)return"DUAL";u.push($(o));for(var a=1;a<r.length;++a){var s=r[a],i=s.on,c=s.using,l=s.join,f=[];f.push(l?" ".concat(vr(l)):","),f.push($(s)),f.push(er("ON",ut,i)),c&&f.push("USING (".concat(c.map(fr).join(", "),")")),u.push(f.filter(br).join(" "))}return u.filter(br).join("")}function W(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var u=n;switch(t){case"partition by":case"default collate":u=ut(n);break;case"options":u="(".concat(n.map((function(r){return[r.keyword,r.symbol,ut(r.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(ut).join(", ")}return o.push(u),o.join(" ")}function G(r){return function(r){if(Array.isArray(r))return q(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return q(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return q(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function q(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function B(r){var t=r.array_index,e=r.arrows,n=void 0===e?[]:e,o=r.as,u=r.collate,a=r.column,s=r.db,i=r.isDual,c=r.schema,l=r.table,f=r.parentheses,p=r.properties,v=r.suffix,b=r.order_by,d=r.subFields,y=void 0===d?[]:d,h="*"===a?"*":function(r,t){if("string"==typeof r)return fr(r,t);var e=r.expr,n=r.offset,o=r.suffix,u=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),dr(r.value),"".concat(r.name?")":""),"]"].filter(br).join("")})).join("");return[ut(e),u,o].filter(br).join("")}(a,i),m=[c,s,l].filter(br).map((function(r){return"".concat(fr(r))})).join(".");m&&(h="".concat(m,".").concat(h)),t&&(h="".concat(h,"[").concat(dr(t.index),"]"),t.property&&(h="".concat(h,".").concat(dr(t.property))));var w=[h=[h].concat(G(y)).join("."),er("AS",ut,o),n.map((function(r,t){return er(r,dr,p[t])})).join(" ")];u&&w.push(pr(u).join(" ")),w.push(vr(v)),w.push(vr(b));var L=w.filter(br).join(" ");return f?"(".concat(L,")"):L}function Y(r){var t=r||{},e=t.dataType,n=t.length,o=t.suffix,u=t.scale,a=t.expr,s=e;return null!=n&&(s+="(".concat([n,u].filter((function(r){return null!=r})).join(", "),")")),o&&o.length&&(s+=" ".concat(o.join(" "))),a&&(s+=ut(a)),s}function V(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,u=r.table,a=r.on_action;return t.push(vr(n)),t.push(H(u)),t.push(e&&"(".concat(e.map((function(r){return ut(r)})).join(", "),")")),t.push(vr(o)),a.map((function(r){return t.push(vr(r.type),ut(r.value))})),t.filter(br)}function Q(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,u=r.order_by;return[ut("string"==typeof t?{type:"column_ref",table:r.table,column:t}:r),er(e&&e.type,fr,e&&e.value),o,vr(u),vr(n)].filter(br).join(" ")}function X(r){var t=[],e=B(r.column),n=Y(r.definition);t.push(e),t.push(n);var o=function(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,u=r.comment,a=r.collate,s=r.storage,i=r.default_val,c=r.auto_increment,l=r.unique,f=r.primary_key,v=r.column_format,b=r.reference_definition;if(t.push(vr(e&&e.value)),i){var d=i.type,y=i.value;t.push(d.toUpperCase(),ut(y))}var h=sr().database;return t.push(p(o)),t.push(gr(c),vr(f),vr(l),Lr(u)),t.push.apply(t,G(pr(n))),"sqlite"!==h&&t.push.apply(t,G(pr(a))),t.push.apply(t,G(pr(v))),t.push.apply(t,G(pr(s))),t.push.apply(t,G(V(b))),t.filter(br).join(" ")}(r);t.push(o);var u=function(r){if(r)return[vr(r.value),"(".concat(ut(r.expr),")"),vr(r.storage_type)].filter(br).join(" ")}(r.generated);return t.push(u),t.filter(br).join(" ")}function K(r){return r?["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?fr(r):lr(r)].join(" "):""}function z(r,t){var e=r.expr,n=r.type;if("cast"===n)return h(r);t&&(e.isDual=t);var o=ut(e),u=r.expr_list;if(u){var a=[o],s=u.map((function(r){return z(r,t)})).join(", ");return a.push([vr(n),n&&"(",s,n&&")"].filter(br).join("")),a.filter(br).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o,"[").concat(dr(e.array_index.index),"]")),[o,K(r.as)].filter(br).join(" ")}function Z(r,t){if(!r||"*"===r)return r;var e=function(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}(t);return r.map((function(r){return z(r,e)})).join(", ")}function J(r){return(J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return wr})),e.d(n,"autoIncrementToSQL",(function(){return gr})),e.d(n,"columnOrderListToSQL",(function(){return Sr})),e.d(n,"commonKeywordArgsToSQL",(function(){return Ar})),e.d(n,"commonOptionConnector",(function(){return er})),e.d(n,"connector",(function(){return nr})),e.d(n,"commonTypeValue",(function(){return pr})),e.d(n,"commentToSQL",(function(){return Lr})),e.d(n,"createBinaryExpr",(function(){return ur})),e.d(n,"createValueExpr",(function(){return or})),e.d(n,"dataTypeToSQL",(function(){return mr})),e.d(n,"DEFAULT_OPT",(function(){return rr})),e.d(n,"escape",(function(){return ar})),e.d(n,"literalToSQL",(function(){return dr})),e.d(n,"columnIdentifierToSql",(function(){return lr})),e.d(n,"getParserOpt",(function(){return sr})),e.d(n,"identifierToSql",(function(){return fr})),e.d(n,"onPartitionsToSQL",(function(){return hr})),e.d(n,"replaceParams",(function(){return yr})),e.d(n,"returningToSQL",(function(){return Er})),e.d(n,"hasVal",(function(){return br})),e.d(n,"setParserOpt",(function(){return ir})),e.d(n,"toUpper",(function(){return vr})),e.d(n,"topToSQL",(function(){return cr})),e.d(n,"triggerEventToSQL",(function(){return Cr}));var rr={database:"hive",type:"table",parseOptions:{}},tr=rr;function er(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function nr(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function or(r){var t=J(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(or)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function ur(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:or(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[or(e[0]),or(e[1])]},n):(n.right=e.type?e:or(e),n)}function ar(r){return r}function sr(){return tr}function ir(r){tr=r}function cr(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function lr(r){var t=sr().database;if(r)switch(t&&t.toLowerCase()){case"postgresql":case"db2":case"snowflake":case"noql":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function fr(r,t){var e=sr().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":case"sqlite":return"`".concat(r,"`");case"postgresql":case"snowflake":case"noql":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function pr(r){var t=[];if(!r)return t;var e=r.type,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(o.toUpperCase()),t}function vr(r){if(r)return r.toUpperCase()}function br(r){return r}function dr(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,o=r.suffix,u=r.value,a="string"==typeof r?r:u;switch(e){case"backticks_quote_string":a="`".concat(u,"`");break;case"string":a="'".concat(u,"'");break;case"regex_string":a='r"'.concat(u,'"');break;case"hex_string":a="X'".concat(u,"'");break;case"full_hex_string":a="0x".concat(u);break;case"natural_string":a="N'".concat(u,"'");break;case"bit_string":a="b'".concat(u,"'");break;case"double_quote_string":a='"'.concat(u,'"');break;case"single_quote_string":a="'".concat(u,"'");break;case"boolean":case"bool":a=u?"TRUE":"FALSE";break;case"null":a="NULL";break;case"star":a="*";break;case"param":a="".concat(t||":").concat(u),t=null;break;case"origin":a=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":a="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":a="N'".concat(u,"'")}var s=[];return t&&s.push(vr(t)),s.push(a),o&&s.push("object"===J(o)&&o.collate?pr(o.collate).join(" "):vr(o)),a=s.join(" "),n?"(".concat(a,")"):a}}function yr(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===J(e)&&null!==e})).forEach((function(n){var o=t[n];if("object"!==J(o)||"param"!==o.type)return r(o,e);if(void 0===e[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return t[n]=or(e[o.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function hr(r){var t=r.type,e=r.partitions;return[vr(t),"(".concat(e.map((function(r){if("range"!==r.type)return dr(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(dr(t)," ").concat(vr(n)," ").concat(dr(e))})).join(", "),")")].join(" ")}function mr(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,u=r.suffix,a="";return null!=e&&(a=o?"".concat(e,", ").concat(o):e),n&&(a="(".concat(a,")")),u&&u.length&&(a+=" ".concat(u.join(" "))),"".concat(t).concat(a)}function wr(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=vr(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var u=e&&e.map((function(r){return[r.field_name,wr(r.field_type)].filter(br).join(" ")})).join(", ");return n?"".concat(o,"<").concat(u,">"):"".concat(o," ").concat(u)}}function Lr(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(dr(o)),t.join(" ")}}function Cr(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[vr(t)];if(e){var o=e.keyword,u=e.columns;n.push(vr(o),u.map(B).join(", "))}return n.join(" ")})).join(" OR ")}function Er(r){return r?["RETURNING",r.columns.map(z).filter(br).join(", ")].join(" "):""}function Ar(r){return r?[vr(r.keyword),vr(r.args)]:[]}function gr(r){if(r){if("string"==typeof r){var t=sr().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,u=r.parentheses,a=vr(e);return u&&(a+="(".concat(dr(n),", ").concat(dr(o),")")),a}}function Sr(r){if(r)return r.map(Q).filter(br).join(", ")}function Tr(r){var t=r.name,e=r.value;return["@".concat(t),"=",ut(e)].filter(br).join(" ")}function jr(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var u=ut(t),a=ut(e);return"".concat(u," ").concat(n," ").concat(a)}function _r(r){var t,e,n,o,u=r.keyword,a=r.suffix,s="";switch(vr(u)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,s=[er("IN",dr,e&&e.right),er("FROM",H,n),A(o)].filter(br).join(" ");break;case"CHARACTER":case"COLLATION":s=function(r){var t=r.expr;if(t)return"LIKE"===vr(t.op)?er("LIKE",dr,t.right):er("WHERE",ut,t)}(r);break;case"COLUMNS":case"INDEXES":s=er("FROM",H,r.from);break;case"GRANTS":s=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,u="'".concat(e,"'");return n&&(u+="@'".concat(n,"'")),["FOR",u,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(br).join(" ")}}(r);break;case"CREATE":s=er("",$,r[a]);break;case"VAR":s=ot(r.var),u=""}return["SHOW",vr(u),vr(a),s].filter(br).join(" ")}var xr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,u=r.type,a=t.expr,s=t.orderby;return[vr(u),vr(n),[[fr(o.schema),fr(o.name)].filter(br).join("."),"(".concat(a.map(Zr).join(", ")).concat(s?[" ORDER","BY",s.map(Zr).join(", ")].join(" "):"",")")].filter(br).join(""),zr(e)].filter(br).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.expr,o=void 0===n?[]:n,u=vr(t),a=H(e),s=o.map(ut);return[u,"TABLE",a,s.join(", ")].filter(br).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[vr(r.type),vr(e),fr(n),zr(t)].filter(br).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[vr(r.type),vr(e),[fr(n.schema),fr(n.name)].filter(br).join("."),zr(t)].filter(br).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[vr(r.type),vr(n),[[fr(o.schema),fr(o.name)].filter(br).join("."),t&&"(".concat(t.expr?t.expr.map(Zr).join(", "):"",")")].filter(br).join(""),zr(e)].filter(br).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,u=r.view,a=r.with,s=vr(t),i=$(u),c=[s,"VIEW",i];e&&c.push("(".concat(e.map(B).join(", "),")"));n&&c.push("WITH ".concat(n.map(vr).join(", ")));c.push("AS",T(o)),a&&c.push(vr(a));return c.filter(br).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[vr(t),$(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,u=r.schema;return[vr(t),vr(e),ut(n),vr(o),fr(u)].filter(br).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,a=r.options,s=[vr(t),vr(e),vr(n)],i=[fr(o.schema),o.name].filter(br).join("."),c="".concat(u.expr.map(Zr).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(Zr).join(", ")].join(" "):"");return s.push("".concat(i,"(").concat(c,")"),"(".concat(a.map(Kr).join(", "),")")),s.filter(br).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,u=r.as,a=r.temporary,s=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.or_replace,p=r.query_expr,v=[vr(t),vr(f),vr(a),vr(e),vr(s),H(n)];if(o){var b=o.type,d=H(o.table);return v.push(vr(b),d),v.filter(br).join(" ")}i&&v.push("(".concat(i.map(Vr).join(", "),")"));c&&v.push(c.map(W).join(" "));v.push(vr(l),vr(u)),p&&v.push(Ir(p));return v.filter(br).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,u=r.execute,a=r.for_each,s=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,v=r.when,b=[vr(f),vr(l),vr(e),vr(c),fr(t),vr(i)],d=Cr(o);b.push(d,"ON",$(p)),s&&b.push("FROM",$(s));b.push.apply(b,qr(Ar(n)).concat(qr(Ar(a)))),v&&b.push(vr(v.type),ut(v.cond));return b.push(vr(u.keyword),m(u.expr)),b.filter(br).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,u=r.type,a=r.table,s=r.if_not_exists,i=r.temporary,c=r.trigger,l=r.events,f=r.order,p=r.time,v=r.when,b=[vr(u),vr(i),t,vr(n),vr(s),$(c),vr(p),l.map((function(r){var t=[vr(r.keyword)],e=r.args;return e&&t.push(vr(e.keyword),e.columns.map(B).join(", ")),t.join(" ")})),"ON",$(a),vr(e&&e.keyword),vr(e&&e.args),f&&"".concat(vr(f.keyword)," ").concat(fr(f.trigger)),er("WHEN",ut,v),vr(o.prefix)];switch(o.type){case"set":b.push(er("SET",x,o.expr));break;case"multiple":b.push(Nr(o.expr.ast))}return b.push(vr(o.suffix)),b.filter(br).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,u=r.schema,a=r.type,s=r.with,i=r.version;return[vr(a),vr(o),vr(n),dr(t),vr(s),er("SCHEMA",dr,u),er("VERSION",dr,i),er("FROM",dr,e)].filter(br).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,a=r.returns,s=r.options,i=r.last,c=[vr(t),vr(e),vr(n)],l=[fr(o.schema),o.name].filter(br).join("."),f=u.map(Zr).filter(br).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[vr(t),vr(e),Array.isArray(n)?"(".concat(n.map(X).join(", "),")"):Qr(n)].filter(br).join(" ")}(a),s.map(Xr).join(" "),i),c.filter(br).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.include,u=r.index_columns,s=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,v=r.algorithm_option,b=r.lock_option,d=r.on_kw,y=r.table,h=r.tablespace,m=r.type,w=r.where,L=r.with,C=r.with_before_where,E=L&&"WITH (".concat(i(L).join(", "),")"),A=o&&"".concat(vr(o.keyword)," (").concat(o.columns.map((function(r){return fr(r)})).join(", "),")"),g=[vr(m),vr(s),vr(n),vr(t),fr(l),vr(d),$(y)].concat(qr(a(c)),["(".concat(Sr(u),")"),A,i(p).join(" "),zr(v),zr(b),er("TABLESPACE",dr,h)]);C?g.push(E,er("WHERE",ut,w)):g.push(er("WHERE",ut,w),E);return g.push(er("ON",ut,f),er("FILESTREAM_ON",dr,e)),g.filter(br).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,u=r.if_not_exists,a=r.create_definitions,s=[vr(t),vr(o),vr(e),vr(u),H(n)];a&&s.push(a.map(Vr).join(" "));return s.filter(br).join(" ")}(r);break;case"database":e=function(r){var t=r.type,e=r.keyword,n=r.database,o=r.if_not_exists,u=r.create_definitions,a=[vr(t),vr(e),vr(o),lr(n)];u&&a.push(u.map(W).join(" "));return a.filter(br).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,u=r.keyword,a=r.recursive,s=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,v=r.with,b=r.with_options,d=p.db,y=p.view,h=[fr(d),fr(y)].filter(br).join(".");return[vr(f),vr(s),vr(l),vr(a),t&&"ALGORITHM = ".concat(vr(t)),n,c&&"SQL SECURITY ".concat(vr(c)),vr(u),vr(o),h,e&&"(".concat(e.map(lr).join(", "),")"),b&&["WITH","(".concat(b.map((function(r){return pr(r).join(" ")})).join(", "),")")].join(" "),"AS",Ir(i),vr(v)].filter(br).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,u=r.target,a=r.create_definitions,s=[vr(n),vr(o),[fr(e.schema),fr(e.name)].filter(br).join("."),vr(t),mr(u)];if(a&&a.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Br(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}(a);try{for(l.s();!(i=l.n()).done;){var f=i.value,v=f.type;switch(v){case"collate":c.push(pr(f).join(" "));break;case"default":c.push(vr(v),ut(f.value));break;case"constraint":c.push(p(f))}}}catch(r){l.e(r)}finally{l.f()}s.push(c.filter(br).join(" "))}return s.filter(br).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,u=r.resource,a=[vr(r.type),vr(n),[fr(o.schema),fr(o.name)].filter(br).join("."),vr(t),vr(u)];if(e){var s=[];switch(u){case"enum":s.push(ut(e))}a.push(s.filter(br).join(" "))}return a.filter(br).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,u=r.keyword,a=r.lock_option,s=r.password_options,i=r.require,c=r.resource_options,l=r.type,f=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[Wr(t)];return e&&n.push(vr(e.keyword),e.auth_plugin,dr(e.value)),n.filter(br).join(" ")})).join(", "),p=[vr(l),vr(u),vr(o),f];n&&p.push(vr(n.keyword),n.value.map(Wr).join(", "));p.push(er(i&&i.keyword,ut,i&&i.value)),c&&p.push(vr(c.keyword),c.value.map((function(r){return ut(r)})).join(" "));s&&s.forEach((function(r){return p.push(er(r.keyword,ut,r.value))}));return p.push(dr(a),Lr(e),dr(t)),p.filter(br).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},select:T,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[vr(t),vr(e),ut(n)].filter(br).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,u=r.orderby,a=r.with,s=r.limit,i=[g(a),"DELETE"],c=Z(t,e);return i.push(c),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||i.push(H(n))),i.push(er("FROM",H,e)),i.push(er("WHERE",ut,o)),i.push(st(u,"order by")),i.push(A(s)),i.filter(br).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[vr(t),$(e),(n||[]).map(Tr).filter(br).join(", ")].filter(br).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[vr(t)],u=[e];n&&u.push("(".concat(ut(n).join(", "),")"));return o.push(u.join("")),o.filter(br).join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,u=r.stmts;return[e,vr(t),n,"IN",Nr([o]),"LOOP",Nr(u),"END LOOP",e].filter(br).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,u=r.orderby,a=r.with,s=r.limit,i=r.returning;return[g(a),"UPDATE",H(e),er("SET",x,n),er("FROM",H,t),er("WHERE",ut,o),st(u,"order by"),A(s),Er(i)].filter(br).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,u=r.prefix,a=r.go,s=r.semicolons,i=r.suffix,c=[vr(r.type),ut(t),dr(u),"".concat(Ur(o.ast||o)).concat(s[0]),vr(a)];n&&c.push(n.map((function(r){return[vr(r.type),ut(r.boolean_expr),"THEN",Ur(r.then.ast||r.then),r.semicolon].filter(br).join(" ")})).join(" "));e&&c.push("ELSE","".concat(Ur(e.ast||e)).concat(s[1]));return c.push(dr(i)),c.filter(br).join(" ")},insert:U,drop:$r,truncate:$r,replace:U,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[vr(t)],u=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,u=r.datatype,a=r.not_null,s=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(br).join(""),vr(n),vr(o)];switch(c){case"variable":l.push.apply(l,[Y(u)].concat(Dr(pr(r.collate)),[vr(a)])),i&&l.push(vr(i.keyword),ut(i.value));break;case"cursor":l.push(vr(s));break;case"table":l.push(vr(s),"(".concat(i.map(Vr).join(", "),")"))}return l.filter(br).join(" ")})).join("".concat(n," "));return o.push(u),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=vr(t),o=fr(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var u,a=Mr(e);try{for(a.s();!(u=a.n()).done;){var s=u.value.map($);n.push(s.join(" TO "))}}catch(r){a.e(r)}finally{a.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=ut(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=vr(t);return"".concat(n," ").concat(fr(e))},set:function(r){var t=r.expr,e=ut(t);return"".concat("SET"," ").concat(e)},lock:Hr,unlock:Hr,show:_r,grant:Gr,revoke:Gr,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return jr(t);case"return":return function(r){var t=r.type,e=r.expr;return[vr(t),ut(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,u=[vr(t),vr(e)];n&&u.push([dr(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(br).join(""),n.expr.map((function(r){return ut(r)})).join(", "));o&&u.push(vr(o.type),vr(o.option),o.symbol,o.expr.map((function(r){return ut(r)})).join(", "));return u.filter(br).join(" ")},transaction:function(r){return ut(r.expr)}};function Ir(r){if(!r)return"";for(var t=xr[r.type],e=r,n=e._parentheses,o=e._orderby,u=e._limit,a=[n&&"(",t(r)];r._next;){var s=xr[r._next.type],i=vr(r.set_op);a.push(i,s(r._next)),r=r._next}return a.push(n&&")",st(o,"order by"),A(u)),a.filter(br).join(" ")}function Nr(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],u=Ir(o);e===n-1&&"transaction"===o.type&&(u="".concat(u," ;")),t.push(u)}return t.join(" ; ")}var kr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction"];function Rr(r){var t=r&&r.ast?r.ast:r;if(!kr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function Or(r){return Array.isArray(r)?(r.forEach(Rr),Nr(r)):(Rr(r),Ir(r))}function Ur(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[Or(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):Or(r)}function Mr(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Pr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function Dr(r){return function(r){if(Array.isArray(r))return Fr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Pr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pr(r,t){if(r){if("string"==typeof r)return Fr(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Fr(r,t):void 0}}function Fr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function $r(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,u=[vr(t),vr(e),vr(o)];switch(e){case"table":u.push(H(n));break;case"trigger":u.push([n[0].schema?"".concat(fr(n[0].schema),"."):"",fr(n[0].trigger)].filter(br).join(""));break;case"database":case"schema":case"procedure":u.push(fr(n));break;case"view":u.push(H(n),r.options&&r.options.map(ut).filter(br).join(" "));break;case"index":u.push.apply(u,[B(n)].concat(Dr(r.table?["ON",$(r.table)]:[]),[r.options&&r.options.map(ut).filter(br).join(" ")]))}return u.filter(br).join(" ")}function Hr(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),vr(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var u,a=[],s=Mr(n);try{var i=function(){var r=u.value,t=r.table,e=r.lock_type,n=[$(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return vr(e[r])})).filter(br).join(" "))}a.push(n.join(" "))};for(s.s();!(u=s.n()).done;)i()}catch(r){s.e(r)}finally{s.f()}return o.push.apply(o,[a.join(", ")].concat(Dr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(br).join(" ")}function Wr(r){var t=r.name,e=r.host,n=[dr(t)];return e&&n.push("@",dr(e)),n.join("")}function Gr(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,u=r.on,a=r.to_from,s=r.user_or_roles,i=r.with,c=[vr(t),dr(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[ut(t)];return e&&n.push("(".concat(e.map(B).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),u)switch(c.push("ON"),n){case"priv":c.push(dr(u.object_type),u.priv_level.map((function(r){return[fr(r.prefix),fr(r.name)].filter(br).join(".")})).join(", "));break;case"proxy":c.push(Wr(u))}return c.push(vr(a),s.map(Wr).join(", ")),c.push(dr(i)),c.filter(br).join(" ")}function qr(r){return function(r){if(Array.isArray(r))return Yr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Br(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Br(r,t){if(r){if("string"==typeof r)return Yr(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Yr(r,t):void 0}}function Yr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function Vr(r){if(!r)return[];var t,e,n,u,a=r.resource;switch(a){case"column":return X(r);case"index":return e=[],n=(t=r).keyword,u=t.index,e.push(vr(n)),e.push(u),e.push.apply(e,o(c(t))),e.filter(br).join(" ");case"constraint":return p(r);case"sequence":return[vr(r.prefix),ut(r.value)].filter(br).join(" ");default:throw new Error("unknown resource = ".concat(a," type"))}}function Qr(r){return r.dataType?mr(r):[fr(r.db),fr(r.schema),fr(r.table)].filter(br).join(".")}function Xr(r){var t=r.type;switch(t){case"as":return[vr(t),r.symbol,Ir(r.declare),vr(r.begin),Nr(r.expr),vr(r.end),r.symbol].filter(br).join(" ");case"set":return[vr(t),r.parameter,vr(r.value&&r.value.prefix),r.value&&r.value.expr.map(ut).join(", ")].filter(br).join(" ");default:return ut(r)}}function Kr(r){var t=r.type,e=r.symbol,n=r.value,o=[vr(t),e];switch(vr(t)){case"SFUNC":o.push([fr(n.schema),n.name].filter(br).join("."));break;case"STYPE":case"MSTYPE":o.push(mr(n));break;default:o.push(ut(n))}return o.filter(br).join(" ")}function zr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.first_after,o=r.if_not_exists,u=r.keyword,a=r.old_column,s=r.prefix,i=r.resource,l=r.symbol,f="",p=[];switch(i){case"column":p=[X(r)];break;case"index":p=c(r),f=r[i];break;case"table":case"schema":f=fr(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=fr(r[i]);break;case"algorithm":case"lock":case"table-option":f=[l,vr(r[i])].filter(br).join(" ");break;case"constraint":f=fr(r[i]),p=[Vr(e)];break;case"key":f=fr(r[i]);break;default:f=[l,r[i]].filter((function(r){return null!==r})).join(" ")}return[vr(t),vr(u),vr(o),a&&B(a),vr(s),f&&f.trim(),p.filter(br).join(" "),n&&"".concat(vr(n.keyword)," ").concat(B(n.column))].filter(br).join(" ")}function Zr(r){var t=r.default&&[vr(r.default.keyword),ut(r.default.value)].join(" ");return[vr(r.mode),r.name,mr(r.type),t].filter(br).join(" ")}function Jr(r){return(Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(vr(r.type)){case"STRUCT":return"(".concat(Z(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(Z(t),"]");var u=Array.isArray(n)?n.map((function(r){return"(".concat(Z(r),")")})).filter(br).join(", "):ut(n);return e?"[".concat(u,"]"):o?"(".concat(u,")"):u}(r);default:return""}}function tt(r){var t=r.definition,e=[vr(r.keyword)];return t&&"object"===Jr(t)&&(e.length=0,e.push(wr(t))),e.push(rt(r)),e.filter(br).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:zr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,u=ut(t.expr),a=r.name,s=y(n);t.distinct&&(u=["DISTINCT",u].join(" ")),t.orderby&&(u="".concat(u," ").concat(st(t.orderby,"order by"))),t.separator&&(u=[u,vr(t.separator.keyword),dr(t.separator.value)].filter(br).join(" "));var i=o?"WITHIN GROUP (".concat(st(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(ut(e.where),")"):"";return["".concat(a,"(").concat(u,")"),i,s,c].filter(br).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,u=t.having,a="".concat(vr(e),"(").concat(ut(o));return u&&(a="".concat(a," HAVING ").concat(vr(u.prefix)," ").concat(ut(u.expr))),[a="".concat(a,")"),y(n)].filter(br).join(" ")},window_func:function(r){var t=r.over;return[d(r),y(t)].filter(br).join(" ")},array:tt,assign:jr,binary_expr:w,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(ut(n));for(var u=0,a=e.length;u<a;++u)t.push(e[u].type.toUpperCase()),e[u].cond&&(t.push(ut(e[u].cond)),t.push("THEN")),t.push(ut(e[u].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:h,column_ref:B,column_definition:X,datatype:mr,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,u=t.source,a=["".concat(vr(e),"(").concat(vr(n)),"FROM",vr(o),ut(u)];return"".concat(a.filter(br).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[vr(t),e,ut(n)].filter(br).join(" ")}(t[r])})).filter(br).join(", ");return"".concat(vr(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,u=r.mode;return[[vr(o),"(".concat(n.map((function(r){return B(r)})).join(", "),")")].join(" "),[vr(t),["(",ut(r.expr),u&&" ".concat(dr(u)),")"].filter(br).join("")].join(" "),K(e)].filter(br).join(" ")},function:m,insert:Ir,interval:M,json:function(r){var t=r.keyword,e=r.expr_list;return[vr(t),e.map((function(r){return ut(r)})).join(", ")].join(" ")},show:_r,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args;return["".concat(e,"(").concat(ut(n).join(", "),")"),"AS",m(t)].join(" ")},tables:H,unnest:D,window:function(r){return r.expr.map(b).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,u=r.keyword,a=r.quoted,s=r.suffix,i=[];u&&i.push(u);var c=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,l="".concat(e||"").concat(c);return s&&(l+=s),i.push(l),[a,i.join(" "),a].filter(br).join("")}function ut(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var u=o[n];t[u]=e[u]}}return nt[t.type]?nt[t.type](t):dr(t)}}function at(r){return r?r.map(ut):[]}function st(r,t){if(!Array.isArray(r))return"";var e=[],n=vr(t);switch(n){case"ORDER BY":e=r.map((function(r){return[ut(r.expr),r.type||"ASC",vr(r.nulls)].filter(br).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return ut(r.expr)}))}return nr(n,e.join(", "))}nt.var=ot,nt.expr_list=function(r){var t=at(r.value);return r.parentheses?"(".concat(t.join(", "),")"):t},nt.select=function(r){var t="object"===et(r._next)?Ir(r):T(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",u="".concat(t).concat(o).concat(ut(n));return e?"(".concat(u,")"):u};var it=e(0);function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var lt,ft,pt,vt,bt=(lt={},ft="hive",pt=it.parse,vt=function(r,t){if("object"!=ct(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=ct(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(ft,"string"),(ft="symbol"==ct(vt)?vt:String(vt))in lt?Object.defineProperty(lt,ft,{value:pt,enumerable:!0,configurable:!0,writable:!0}):lt[ft]=pt,lt);function dt(r){return(dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function yt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(!r)return;if("string"==typeof r)return ht(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return ht(r,t)}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function ht(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function mt(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,wt(n.key),n)}}function wt(r){var t=function(r,t){if("object"!=dt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=dt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==dt(t)?t:String(t)}var Lt=function(){function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}var t,e,n;return t=r,(e=[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr;return ir(t),Ur(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr;return ir(t),ut(r)}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr,e=t.database,n=void 0===e?"hive":e;ir(t);var o=n.toLowerCase();if(bt[o])return bt[o](r.trim(),t.parseOptions||rr.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:rr;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var u,a=this["".concat(o,"List")].bind(this),s=a(r,e),i=!0,c="",l=yt(s);try{for(l.s();!(u=l.n()).done;){var f,p=u.value,v=!1,b=yt(t);try{for(b.s();!(f=b.n()).done;){var d=f.value,y=new RegExp(d,"i");if(y.test(p)){v=!0;break}}}catch(r){b.e(r)}finally{b.f()}if(!v){c=p,i=!1;break}}}catch(r){l.e(r)}finally{l.f()}if(!i)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])&&mt(t.prototype,e),n&&mt(t,n),Object.defineProperty(t,"prototype",{writable:!1}),r}();function Ct(r){return(Ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Ct(self))&&self&&(self.NodeSQLParser={Parser:Lt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Ct(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Ct(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Lt,util:n})}]));
//# sourceMappingURL=hive.js.map