!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?u(r.parts[t][0])+"-"+u(r.parts[t][1]):u(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function u(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,u=new Array(r.length);for(t=0;t<r.length;t++)u[t]=(o=r[t],e[o.type](o));if(u.sort(),u.length>0){for(t=1,n=1;t<u.length;t++)u[t-1]!==u[t]&&(u[n]=u[t],n++);u.length=n}switch(u.length){case 1:return u[0];case 2:return u[0]+" or "+u[1];default:return u.slice(0,-1).join(", ")+", or "+u[u.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,u={},a={start:ko},s=ko,i=function(r,t){return fi(r,t,1)},c=go("IF",!0),l=function(r,t){return fi(r,t)},f=go("AUTO_INCREMENT",!0),p=go("UNIQUE",!0),v=go("KEY",!0),b=go("PRIMARY",!0),d=go("COLUMN_FORMAT",!0),y=go("FIXED",!0),h=go("DYNAMIC",!0),m=go("DEFAULT",!0),w=go("STORAGE",!0),L=go("DISK",!0),C=go("MEMORY",!0),E=go("ALGORITHM",!0),A=go("INSTANT",!0),g=go("INPLACE",!0),T=go("COPY",!0),j=go("LOCK",!0),S=go("NONE",!0),_=go("SHARED",!0),x=go("EXCLUSIVE",!0),k=go("CHECK",!0),I=go("NOCHECK",!0),N=go("PRIMARY KEY",!0),R=go("NOT",!0),O=go("FOR",!0),U=go("REPLICATION",!0),M=go("FOREIGN KEY",!0),D=go("MATCH FULL",!0),P=go("MATCH PARTIAL",!0),F=go("MATCH SIMPLE",!0),H=go("RESTRICT",!0),$=go("CASCADE",!0),B=go("SET NULL",!0),q=go("NO ACTION",!0),W=go("SET DEFAULT",!0),Y=go("CHARACTER",!0),G=go("SET",!0),V=go("CHARSET",!0),Q=go("COLLATE",!0),X=go("AVG_ROW_LENGTH",!0),K=go("KEY_BLOCK_SIZE",!0),z=go("MAX_ROWS",!0),Z=go("MIN_ROWS",!0),J=go("STATS_SAMPLE_PAGES",!0),rr=go("CONNECTION",!0),tr=go("COMPRESSION",!0),er=go("'",!1),nr=go("ZLIB",!0),or=go("LZ4",!0),ur=go("ENGINE",!0),ar=go("READ",!0),sr=go("LOCAL",!0),ir=go("LOW_PRIORITY",!0),cr=go("WRITE",!0),lr=go("(",!1),fr=go(")",!1),pr=go("BTREE",!0),vr=go("HASH",!0),br=go("WITH",!0),dr=go("PARSER",!0),yr=go("VISIBLE",!0),hr=go("INVISIBLE",!0),mr=function(r,t){return t.unshift(r),t.forEach(r=>{const{table:t,as:e}=r;mi[t]=t,e&&(mi[e]=t),function(r){const t=bi(r);r.clear(),t.forEach(t=>r.add(t))}(hi)}),t},wr=go("FIRST",!0),Lr=go("ROWS",!0),Cr=go("ONLY",!0),Er=go("NEXT",!0),Ar=go("=",!1),gr=go("DUPLICATE",!0),Tr=function(r,t){return pi(r,t)},jr=go("!",!1),Sr=function(r){return r[0]+" "+r[2]},_r=go(">=",!1),xr=go(">",!1),kr=go("<=",!1),Ir=go("<>",!1),Nr=go("<",!1),Rr=go("!=",!1),Or=go("+",!1),Ur=go("-",!1),Mr=go("*",!1),Dr=go("/",!1),Pr=go("%",!1),Fr=function(r){return!0===si[r.toUpperCase()]},Hr=go('"',!1),$r=/^[^"]/,Br=To(['"'],!0,!1),qr=function(r){return r.join("")},Wr=/^[^']/,Yr=To(["'"],!0,!1),Gr=go("`",!1),Vr=/^[^`]/,Qr=To(["`"],!0,!1),Xr=function(r,t){return r+t.join("")},Kr=/^[A-Za-z_]/,zr=To([["A","Z"],["a","z"],"_"],!1,!1),Zr=/^[A-Za-z0-9_]/,Jr=To([["A","Z"],["a","z"],["0","9"],"_"],!1,!1),rt=/^[A-Za-z0-9_:]/,tt=To([["A","Z"],["a","z"],["0","9"],"_",":"],!1,!1),et=go(":",!1),nt=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}},ot=/^[^"\\\0-\x1F\x7F]/,ut=To(['"',"\\",["\0",""],""],!0,!1),at=/^[^'\\]/,st=To(["'","\\"],!0,!1),it=go("\\'",!1),ct=go('\\"',!1),lt=go("\\\\",!1),ft=go("\\/",!1),pt=go("\\b",!1),vt=go("\\f",!1),bt=go("\\n",!1),dt=go("\\r",!1),yt=go("\\t",!1),ht=go("\\u",!1),mt=go("\\",!1),wt=go("''",!1),Lt=go('""',!1),Ct=go("``",!1),Et=/^[\n\r]/,At=To(["\n","\r"],!1,!1),gt=go(".",!1),Tt=/^[0-9]/,jt=To([["0","9"]],!1,!1),St=/^[0-9a-fA-F]/,_t=To([["0","9"],["a","f"],["A","F"]],!1,!1),xt=/^[eE]/,kt=To(["e","E"],!1,!1),It=/^[+\-]/,Nt=To(["+","-"],!1,!1),Rt=go("NULL",!0),Ot=go("NOT NULL",!0),Ut=go("TRUE",!0),Mt=go("TO",!0),Dt=go("FALSE",!0),Pt=(go("SHOW",!0),go("DROP",!0)),Ft=go("USE",!0),Ht=go("ALTER",!0),$t=go("SELECT",!0),Bt=go("UPDATE",!0),qt=go("CREATE",!0),Wt=go("TEMPORARY",!0),Yt=go("DELETE",!0),Gt=go("INSERT",!0),Vt=go("RECURSIVE",!1),Qt=go("REPLACE",!0),Xt=go("RENAME",!0),Kt=go("IGNORE",!0),zt=(go("EXPLAIN",!0),go("PARTITION",!0)),Zt=go("INTO",!0),Jt=go("FROM",!0),re=go("UNLOCK",!0),te=go("AS",!0),ee=go("TABLE",!0),ne=go("TABLES",!0),oe=go("DATABASE",!0),ue=go("SCHEME",!0),ae=go("ON",!0),se=go("LEFT",!0),ie=go("RIGHT",!0),ce=go("FULL",!0),le=go("INNER",!0),fe=go("JOIN",!0),pe=go("OUTER",!0),ve=go("OVER",!0),be=go("UNION",!0),de=go("VALUES",!0),ye=go("USING",!0),he=go("WHERE",!0),me=go("GROUP",!0),we=go("BY",!0),Le=go("ORDER",!0),Ce=go("HAVING",!0),Ee=go("FETCH",!0),Ae=go("OFFSET",!0),ge=go("ASC",!0),Te=go("DESC",!0),je=go("ALL",!0),Se=go("DISTINCT",!0),_e=go("BETWEEN",!0),xe=go("IN",!0),ke=go("IS",!0),Ie=go("LIKE",!0),Ne=go("EXISTS",!0),Re=go("AND",!0),Oe=go("OR",!0),Ue=go("COUNT",!0),Me=go("MAX",!0),De=go("MIN",!0),Pe=go("SUM",!0),Fe=go("AVG",!0),He=go("CALL",!0),$e=go("CASE",!0),Be=go("WHEN",!0),qe=go("THEN",!0),We=go("ELSE",!0),Ye=go("END",!0),Ge=go("CAST",!0),Ve=go("CHAR",!0),Qe=go("VARCHAR",!0),Xe=go("NUMERIC",!0),Ke=go("DECIMAL",!0),ze=go("SIGNED",!0),Ze=go("UNSIGNED",!0),Je=go("INT",!0),rn=go("ZEROFILL",!0),tn=go("INTEGER",!0),en=go("JSON",!0),nn=go("SMALLINT",!0),on=go("TINYINT",!0),un=go("TINYTEXT",!0),an=go("TEXT",!0),sn=go("MEDIUMTEXT",!0),cn=go("LONGTEXT",!0),ln=go("BIGINT",!0),fn=go("FLOAT",!0),pn=go("DOUBLE",!0),vn=go("DATE",!0),bn=go("DATETIME",!0),dn=go("TIME",!0),yn=go("TIMESTAMP",!0),hn=go("TRUNCATE",!0),mn=go("USER",!0),wn=go("CURRENT_DATE",!0),Ln=(go("ADDDATE",!0),go("INTERVAL",!0)),Cn=go("YEAR",!0),En=go("MONTH",!0),An=go("DAY",!0),gn=go("HOUR",!0),Tn=go("MINUTE",!0),jn=go("SECOND",!0),Sn=go("CURRENT_TIME",!0),_n=go("CURRENT_TIMESTAMP",!0),xn=go("CURRENT_USER",!0),kn=go("SESSION_USER",!0),In=go("SYSTEM_USER",!0),Nn=go("GLOBAL",!0),Rn=go("SESSION",!0),On=go("PERSIST",!0),Un=go("PERSIST_ONLY",!0),Mn=go("@",!1),Dn=go("@@",!1),Pn=go("$",!1),Fn=go("return",!0),Hn=go(":=",!1),$n=go("DUAL",!0),Bn=go("ADD",!0),qn=go("COLUMN",!0),Wn=go("INDEX",!0),Yn=go("FULLTEXT",!0),Gn=go("SPATIAL",!0),Vn=go("COMMENT",!0),Qn=go("CONSTRAINT",!0),Xn=go("REFERENCES",!0),Kn=go("SQL_CALC_FOUND_ROWS",!0),zn=go("SQL_CACHE",!0),Zn=go("SQL_NO_CACHE",!0),Jn=go("SQL_SMALL_RESULT",!0),ro=go("SQL_BIG_RESULT",!0),to=go("SQL_BUFFER_RESULT",!0),eo=go(",",!1),no=go("[",!1),oo=go("]",!1),uo=go(";",!1),ao=go("->",!1),so=go("->>",!1),io=go("||",!1),co=go("&&",!1),lo=go("/*",!1),fo=go("*/",!1),po=go("--",!1),vo=go("#",!1),bo={type:"any"},yo=/^[ \t\n\r]/,ho=To([" ","\t","\n","\r"],!1,!1),mo=function(r){return{dataType:r}},wo=0,Lo=[{line:1,column:1}],Co=0,Eo=[],Ao=0;if("startRule"in t){if(!(t.startRule in a))throw new Error("Can't start parsing from rule \""+t.startRule+'".');s=a[t.startRule]}function go(r,t){return{type:"literal",text:r,ignoreCase:t}}function To(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function jo(t){var e,n=Lo[t];if(n)return n;for(e=t-1;!Lo[e];)e--;for(n={line:(n=Lo[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Lo[t]=n,n}function So(r,t){var e=jo(r),n=jo(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function _o(r){wo<Co||(wo>Co&&(Co=wo,Eo=[]),Eo.push(r))}function xo(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function ko(){var r,t;return r=wo,$s()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=No())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Ds())!==u&&(s=$s())!==u&&(i=No())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Ds())!==u&&(s=$s())!==u&&(i=No())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(yi),columnList:bi(hi),ast:n}}(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())!==u?(r,r=t):(wo=r,r=u),r}function Io(){var t;return(t=function(){var r,t,e,n,o,a;r=wo,(t=ja())!==u&&$s()!==u&&(e=Pa())!==u&&$s()!==u&&(n=pu())!==u?(r,s=t,c=e,(l=n)&&l.forEach(r=>yi.add(`${s}::${r.db}::${r.table}`)),t={tableList:Array.from(yi),columnList:bi(hi),ast:{type:s.toLowerCase(),keyword:c.toLowerCase(),name:l}},r=t):(wo=r,r=u);var s,c,l;r===u&&(r=wo,(t=ja())!==u&&$s()!==u&&(e=_s())!==u&&$s()!==u&&(n=zu())!==u&&$s()!==u&&Ha()!==u&&$s()!==u&&(o=yu())!==u&&$s()!==u?((a=function(){var r,t,e,n,o,a;r=wo,(t=$o())===u&&(t=Bo());if(t!==u){for(e=[],n=wo,(o=$s())!==u?((a=$o())===u&&(a=Bo()),a!==u?n=o=[o,a]:(wo=n,n=u)):(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u?((a=$o())===u&&(a=Bo()),a!==u?n=o=[o,a]:(wo=n,n=u)):(wo=n,n=u);e!==u?(r,t=i(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())===u&&(a=null),a!==u&&$s()!==u?(r,t=function(r,t,e,n,o){return{tableList:Array.from(yi),columnList:bi(hi),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),name:e,table:n,options:o}}}(t,e,n,o,a),r=t):(wo=r,r=u)):(wo=r,r=u));return r}())===u&&(t=function(){var t;(t=function(){var r,t,e,n,o,a,s,i,c,f;r=wo,(t=_a())!==u&&$s()!==u?((e=xa())===u&&(e=null),e!==u&&$s()!==u&&Pa()!==u&&$s()!==u?((n=Uo())===u&&(n=null),n!==u&&$s()!==u&&(o=pu())!==u&&$s()!==u&&(a=function(){var r,t,e,n,o,a,s,i,c;if(r=wo,(t=Us())!==u)if($s()!==u)if((e=Mo())!==u){for(n=[],o=wo,(a=$s())!==u&&(s=Rs())!==u&&(i=$s())!==u&&(c=Mo())!==u?o=a=[a,s,i,c]:(wo=o,o=u);o!==u;)n.push(o),o=wo,(a=$s())!==u&&(s=Rs())!==u&&(i=$s())!==u&&(c=Mo())!==u?o=a=[a,s,i,c]:(wo=o,o=u);n!==u&&(o=$s())!==u&&(a=Ms())!==u?(r,t=l(e,n),r=t):(wo=r,r=u)}else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;return r}())!==u&&$s()!==u?((s=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Ko())!==u){for(e=[],n=wo,(o=$s())!==u?((a=Rs())===u&&(a=null),a!==u&&(s=$s())!==u&&(i=Ko())!==u?n=o=[o,a,s,i]:(wo=n,n=u)):(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u?((a=Rs())===u&&(a=null),a!==u&&(s=$s())!==u&&(i=Ko())!==u?n=o=[o,a,s,i]:(wo=n,n=u)):(wo=n,n=u);e!==u?(r,t=fi(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())===u&&(s=null),s!==u&&$s()!==u?((i=Ra())===u&&(i=Ia()),i===u&&(i=null),i!==u&&$s()!==u?((c=Da())===u&&(c=null),c!==u&&$s()!==u?((f=Oo())===u&&(f=null),f!==u?(r,p=t,v=e,b=n,y=a,h=s,m=i,w=c,L=f,(d=o)&&d.forEach(r=>yi.add(`create::${r.db}::${r.table}`)),t={tableList:Array.from(yi),columnList:bi(hi),ast:{type:p[0].toLowerCase(),keyword:"table",temporary:v&&v[0].toLowerCase(),if_not_exists:b,table:d,ignore_replace:m&&m[0].toLowerCase(),as:w&&w[0].toLowerCase(),query_expr:L&&L.ast,create_definitions:y,table_options:h}},r=t):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u);var p,v,b,d,y,h,m,w,L;r===u&&(r=wo,(t=_a())!==u&&$s()!==u?((e=xa())===u&&(e=null),e!==u&&$s()!==u&&Pa()!==u&&$s()!==u?((n=Uo())===u&&(n=null),n!==u&&$s()!==u&&(o=pu())!==u&&$s()!==u&&(a=function r(){var t,e;(t=function(){var r,t;r=wo,Ja()!==u&&$s()!==u&&(t=pu())!==u?(r,r={type:"like",table:t}):(wo=r,r=u);return r}())===u&&(t=wo,Us()!==u&&$s()!==u&&(e=r())!==u&&$s()!==u&&Ms()!==u?(t,(n=e).parentheses=!0,t=n):(wo=t,t=u));var n;return t}())!==u?(r,t=function(r,t,e,n,o){return n&&n.forEach(r=>yi.add(`create::${r.db}::${r.table}`)),{tableList:Array.from(yi),columnList:bi(hi),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(t,e,n,o,a),r=t):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u));return r}())===u&&(t=function(){var t,e,n,o,a,s;t=wo,(e=_a())!==u&&$s()!==u?((n=function(){var t,e,n,o;t=wo,"database"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(oe));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="DATABASE"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=wo,"scheme"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(ue));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="SCHEME"):(wo=t,t=u)):(wo=t,t=u);return t}()),n!==u&&$s()!==u?((o=Uo())===u&&(o=null),o!==u&&$s()!==u&&(a=na())!==u&&$s()!==u?((s=function(){var r,t,e,n,o,a;if(r=wo,(t=Xo())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Xo())!==u?n=o=[o,a]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Xo())!==u?n=o=[o,a]:(wo=n,n=u);e!==u?(r,t=i(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())===u&&(s=null),s!==u?(t,c=e,l=o,f=a,p=s,e={tableList:Array.from(yi),columnList:bi(hi),ast:{type:c[0].toLowerCase(),keyword:"database",if_not_exists:l,database:f,create_definitions:p}},t=e):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u);var c,l,f,p;return t}());return t}())===u&&(t=function(){var t,e,n,o;t=wo,(e=function(){var t,e,n,o;t=wo,"truncate"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(hn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="TRUNCATE"):(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u?((n=Pa())===u&&(n=null),n!==u&&$s()!==u&&(o=pu())!==u?(t,a=e,s=n,(i=o)&&i.forEach(r=>yi.add(`${a}::${r.db}::${r.table}`)),e={tableList:Array.from(yi),columnList:bi(hi),ast:{type:a.toLowerCase(),keyword:s&&s.toLowerCase()||"table",name:i}},t=e):(wo=t,t=u)):(wo=t,t=u);var a,s,i;return t}())===u&&(t=function(){var r,t,e;r=wo,(t=Na())!==u&&$s()!==u&&Pa()!==u&&$s()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=iu())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=iu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=iu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,t=l(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())!==u?(r,(n=e).forEach(r=>r.forEach(r=>r.table&&yi.add(`rename::${r.db}::${r.table}`))),t={tableList:Array.from(yi),columnList:bi(hi),ast:{type:"rename",table:n}},r=t):(wo=r,r=u);var n;return r}())===u&&(t=function(){var t,e,n;t=wo,(e=function(){var t,e,n,o;t=wo,"call"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(He));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="CALL"):(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u&&(n=ti())!==u?(t,o=n,e={tableList:Array.from(yi),columnList:bi(hi),ast:{type:"call",expr:o}},t=e):(wo=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n;t=wo,(e=function(){var t,e,n,o;t=wo,"use"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(Ft));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u&&(n=Zu())!==u?(t,o=n,yi.add(`use::${o}::null`),e={tableList:Array.from(yi),columnList:bi(hi),ast:{type:"use",db:o}},t=e):(wo=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n,o;t=wo,(e=function(){var t,e,n,o;t=wo,"alter"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(Ht));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u&&Pa()!==u&&$s()!==u&&(n=pu())!==u&&$s()!==u&&(o=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Ho())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=Ho())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=Ho())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,t=l(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())!==u?(t,s=o,(a=n)&&a.length>0&&a.forEach(r=>yi.add(`alter::${r.db}::${r.table}`)),e={tableList:Array.from(yi),columnList:bi(hi),ast:{type:"alter",table:a,expr:s}},t=e):(wo=t,t=u);var a,s;return t}())===u&&(t=function(){var t,e,n,o;t=wo,(e=Ma())!==u&&$s()!==u?((n=function(){var t,e,n,o;t=wo,"global"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(Nn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="GLOBAL"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=wo,"session"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(Rn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="SESSION"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=wo,"local"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(sr));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="LOCAL"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=wo,"persist"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(On));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="PERSIST"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=wo,"persist_only"===r.substr(wo,12).toLowerCase()?(e=r.substr(wo,12),wo+=12):(e=u,0===Ao&&_o(Un));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="PERSIST_ONLY"):(wo=t,t=u)):(wo=t,t=u);return t}()),n===u&&(n=null),n!==u&&$s()!==u&&(o=Xs())!==u?(t,a=n,(s=o).keyword=a,e={tableList:Array.from(yi),columnList:bi(hi),ast:{type:"set",expr:s}},t=e):(wo=t,t=u)):(wo=t,t=u);var a,s;return t}())===u&&(t=function(){var t,e,n;t=wo,(e=function(){var t,e,n,o;t=wo,"lock"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(j));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u&&Fa()!==u&&$s()!==u&&(n=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=zo())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=zo())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=zo())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,t=fi(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())!==u?(t,o=n,e={tableList:Array.from(yi),columnList:bi(hi),ast:{type:"lock",keyword:"tables",tables:o}},t=e):(wo=t,t=u);var o;return t}())===u&&(t=function(){var t,e;t=wo,(e=function(){var t,e,n,o;t=wo,"unlock"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(re));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u&&Fa()!==u?(t,e={tableList:Array.from(yi),columnList:bi(hi),ast:{type:"unlock",keyword:"tables"}},t=e):(wo=t,t=u);return t}()),t}function No(){var r;return(r=Oo())===u&&(r=function(){var r,t,e,n,o;r=wo,(t=Sa())!==u&&$s()!==u&&(e=pu())!==u&&$s()!==u&&Ma()!==u&&$s()!==u&&(n=Tu())!==u&&$s()!==u?((o=wu())===u&&(o=null),o!==u?(r,t=function(r,t,e){const n={};return r&&r.forEach(r=>{const{db:t,as:e,table:o,join:u}=r,a=u?"select":"update";t&&(n[o]=t),o&&yi.add(`${a}::${t}::${o}`)}),t&&t.forEach(r=>{if(r.table){const t=vi(r.table);yi.add(`update::${n[t]||null}::${t}`)}hi.add(`update::${r.table}::${r.column}`)}),{tableList:Array.from(yi),columnList:bi(hi),ast:{type:"update",table:r,set:t,where:e}}}(e,n,o),r=t):(wo=r,r=u)):(wo=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o,a,s,i;r=wo,(t=ku())!==u&&$s()!==u?((e=Ua())===u&&(e=null),e!==u&&$s()!==u&&(n=yu())!==u&&$s()!==u?((o=_u())===u&&(o=null),o!==u&&$s()!==u&&Us()!==u&&$s()!==u&&(a=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=ta())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=ta())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=ta())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,t=l(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())!==u&&$s()!==u&&Ms()!==u&&$s()!==u&&(s=Su())!==u&&$s()!==u?((i=xu())===u&&(i=null),i!==u?(r,t=function(r,t,e,n,o,u){if(t&&(yi.add(`insert::${t.db}::${t.table}`),t.as=null),n){let r=t&&t.table||null;Array.isArray(o)&&o.forEach((r,t)=>{if(r.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),n.forEach(t=>hi.add(`insert::${r}::${t}`))}return{tableList:Array.from(yi),columnList:bi(hi),ast:{type:r,table:[t],columns:n,values:o,partition:e,on_duplicate_update:u}}}(t,n,o,a,s,i),r=t):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o,a,s,i;r=wo,(t=ku())!==u&&$s()!==u?((e=Ra())===u&&(e=null),e!==u&&$s()!==u?((n=Ua())===u&&(n=null),n!==u&&$s()!==u&&(o=yu())!==u&&$s()!==u?((a=_u())===u&&(a=null),a!==u&&$s()!==u&&(s=Su())!==u&&$s()!==u?((i=xu())===u&&(i=null),i!==u?(r,t=function(r,t,e,n,o,u,a){n&&(yi.add(`insert::${n.db}::${n.table}`),hi.add(`insert::${n.table}::(.*)`),n.as=null);const s=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(yi),columnList:bi(hi),ast:{type:r,table:[n],columns:null,values:u,partition:o,prefix:s,on_duplicate_update:a}}}(t,e,n,o,a,s,i),r=t):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o,a;r=wo,(t=ku())!==u&&$s()!==u&&Ua()!==u&&$s()!==u&&(e=yu())!==u&&$s()!==u?((n=_u())===u&&(n=null),n!==u&&$s()!==u&&Ma()!==u&&$s()!==u&&(o=Tu())!==u&&$s()!==u?((a=xu())===u&&(a=null),a!==u?(r,s=t,c=n,l=o,f=a,(i=e)&&(yi.add(`insert::${i.db}::${i.table}`),hi.add(`insert::${i.table}::(.*)`),i.as=null),t={tableList:Array.from(yi),columnList:bi(hi),ast:{type:s,table:[i],columns:null,partition:c,set:l,on_duplicate_update:f}},r=t):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u);var s,i,c,l,f;return r}())===u&&(r=function(){var r,t,e,n,o;r=wo,(t=ka())!==u&&$s()!==u?((e=pu())===u&&(e=null),e!==u&&$s()!==u&&(n=su())!==u&&$s()!==u?((o=wu())===u&&(o=null),o!==u?(r,t=function(r,t,e){if(t&&t.forEach(r=>{const{db:t,as:e,table:n,join:o}=r,u=o?"select":"delete";n&&yi.add(`${u}::${t}::${n}`),o||hi.add(`delete::${n}::(.*)`)}),null===r&&1===t.length){const e=t[0];r=[{db:e.db,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(yi),columnList:bi(hi),ast:{type:"delete",table:r,from:t,where:e}}}(e,n,o),r=t):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u);return r}())===u&&(r=Io())===u&&(r=function(){var r,t;r=[],t=Qs();for(;t!==u;)r.push(t),t=Qs();return r}()),r}function Ro(){var t,e,n;return t=wo,function(){var t,e,n,o;t=wo,"union"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(be));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u?((e=Qa())===u&&(e=Xa()),e===u&&(e=null),e!==u?(t,t=(n=e)?"union "+n.toLowerCase():"union"):(wo=t,t=u)):(wo=t,t=u),t}function Oo(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Zo())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Ro())!==u&&(s=$s())!==u&&(i=Zo())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Ro())!==u&&(s=$s())!==u&&(i=Zo())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u&&(n=$s())!==u?((o=Cu())===u&&(o=null),o!==u&&(a=$s())!==u?((s=gu())===u&&(s=null),s!==u?(r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&(r._limit=n),{tableList:Array.from(yi),columnList:bi(hi),ast:r}}(t,e,o,s)):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)}else wo=r,r=u;return r}function Uo(){var t,e;return t=wo,"if"===r.substr(wo,2).toLowerCase()?(e=r.substr(wo,2),wo+=2):(e=u,0===Ao&&_o(c)),e!==u&&$s()!==u&&ts()!==u&&$s()!==u&&rs()!==u?(t,t=e="IF NOT EXISTS"):(wo=t,t=u),t}function Mo(){var r;return(r=Yo())===u&&(r=Po())===u&&(r=qo())===u&&(r=Wo()),r}function Do(){var t,e,n,o;return t=wo,(e=function(){var t,e;t=wo,(e=function(){var t,e,n,o;t=wo,"not null"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(Ot));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(t,e={type:"not null",value:"not null"});return t=e}())===u&&(e=pa()),e!==u&&(t,(o=e)&&!o.value&&(o.value="null"),e={nullable:o}),(t=e)===u&&(t=wo,(e=function(){var r,t;r=wo,ga()!==u&&$s()!==u?((t=fa())===u&&(t=Pu()),t!==u?(r,r={type:"default",value:t}):(wo=r,r=u)):(wo=r,r=u);return r}())!==u&&(t,e={default_val:e}),(t=e)===u&&(t=wo,"auto_increment"===r.substr(wo,14).toLowerCase()?(e=r.substr(wo,14),wo+=14):(e=u,0===Ao&&_o(f)),e!==u&&(t,e={auto_increment:e.toLowerCase()}),(t=e)===u&&(t=wo,"unique"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(p)),e!==u&&$s()!==u?("key"===r.substr(wo,3).toLowerCase()?(n=r.substr(wo,3),wo+=3):(n=u,0===Ao&&_o(v)),n===u&&(n=null),n!==u?(t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,"primary"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(b)),e===u&&(e=null),e!==u&&$s()!==u?("key"===r.substr(wo,3).toLowerCase()?(n=r.substr(wo,3),wo+=3):(n=u,0===Ao&&_o(v)),n!==u?(t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,(e=Ws())!==u&&(t,e={comment:e}),(t=e)===u&&(t=wo,(e=Fo())!==u&&(t,e={collate:e}),(t=e)===u&&(t=wo,(e=function(){var t,e,n;t=wo,"column_format"===r.substr(wo,13).toLowerCase()?(e=r.substr(wo,13),wo+=13):(e=u,0===Ao&&_o(d));e!==u&&$s()!==u?("fixed"===r.substr(wo,5).toLowerCase()?(n=r.substr(wo,5),wo+=5):(n=u,0===Ao&&_o(y)),n===u&&("dynamic"===r.substr(wo,7).toLowerCase()?(n=r.substr(wo,7),wo+=7):(n=u,0===Ao&&_o(h)),n===u&&("default"===r.substr(wo,7).toLowerCase()?(n=r.substr(wo,7),wo+=7):(n=u,0===Ao&&_o(m)))),n!==u?(t,e={type:"column_format",value:n.toLowerCase()},t=e):(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(t,e={column_format:e}),(t=e)===u&&(t=wo,(e=function(){var t,e,n;t=wo,"storage"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(w));e!==u&&$s()!==u?("disk"===r.substr(wo,4).toLowerCase()?(n=r.substr(wo,4),wo+=4):(n=u,0===Ao&&_o(L)),n===u&&("memory"===r.substr(wo,6).toLowerCase()?(n=r.substr(wo,6),wo+=6):(n=u,0===Ao&&_o(C))),n!==u?(t,e={type:"storage",value:n.toLowerCase()},t=e):(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(t,e={storage:e}),(t=e)===u&&(t=wo,(e=Vo())!==u&&(t,e={reference_definition:e}),t=e))))))))),t}function Po(){var r,t,e,n,o,a,s;return r=wo,(t=zu())!==u&&$s()!==u&&(e=ui())!==u&&$s()!==u?((n=function(){var r,t,e,n,o,a;if(r=wo,(t=Do())!==u)if($s()!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Do())!==u?n=o=[o,a]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Do())!==u?n=o=[o,a]:(wo=n,n=u);e!==u?(r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):(wo=r,r=u)}else wo=r,r=u;else wo=r,r=u;return r}())===u&&(n=null),n!==u?(r,o=t,a=e,s=n,hi.add(`create::${o.table}::${o.column}`),r=t={column:o,definition:a,resource:"column",...s||{}}):(wo=r,r=u)):(wo=r,r=u),r}function Fo(){var t,e;return t=wo,function(){var t,e,n,o;t=wo,"collate"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(Q));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="COLLATE"):(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&(e=na())!==u?(t,t={type:"collate",value:e}):(wo=t,t=u),t}function Ho(){var t;return(t=function(){var r,t;r=wo,js()!==u&&$s()!==u&&(t=Yo())!==u?(r,r={action:"add",create_definitions:t,resource:"constraint",type:"alter"}):(wo=r,r=u);return r}())===u&&(t=function(){var t,e,n,o;t=wo,(e=ja())!==u&&$s()!==u?("check"===r.substr(wo,5).toLowerCase()?(n=r.substr(wo,5),wo+=5):(n=u,0===Ao&&_o(k)),n!==u&&$s()!==u&&(o=na())!==u?(t,e={action:"drop",constraint:o,keyword:n.toLowerCase(),resource:"constraint",type:"alter"},t=e):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,Ya()!==u&&$s()!==u?("check"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(k)),e!==u&&$s()!==u?("check"===r.substr(wo,5).toLowerCase()?(n=r.substr(wo,5),wo+=5):(n=u,0===Ao&&_o(k)),n!==u&&$s()!==u&&Is()!==u&&$s()!==u&&(o=na())!==u?(t,t={action:"with",constraint:o,keyword:"check check",resource:"constraint",type:"alter"}):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n;t=wo,"nocheck"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(I));e!==u&&$s()!==u&&Is()!==u&&$s()!==u&&(n=na())!==u?(t,t=e={action:"nocheck",constraint:n,resource:"constraint",type:"alter"}):(wo=t,t=u);return t}())===u&&(t=function(){var r,t,e,n;r=wo,(t=js())!==u&&$s()!==u?((e=Ss())===u&&(e=null),e!==u&&$s()!==u&&(n=Po())!==u?(r,o=e,a=n,t={action:"add",...a,keyword:o,resource:"column",type:"alter"},r=t):(wo=r,r=u)):(wo=r,r=u);var o,a;return r}())===u&&(t=function(){var r,t,e;r=wo,ja()!==u&&$s()!==u?((t=Ss())===u&&(t=null),t!==u&&$s()!==u&&(e=zu())!==u?(r,r={action:"drop",column:e,keyword:t,resource:"column",type:"alter"}):(wo=r,r=u)):(wo=r,r=u);return r}())===u&&(t=function(){var r,t,e;r=wo,(t=js())!==u&&$s()!==u&&(e=qo())!==u?(r,n=e,t={action:"add",type:"alter",...n},r=t):(wo=r,r=u);var n;return r}())===u&&(t=function(){var r,t,e;r=wo,(t=js())!==u&&$s()!==u&&(e=Wo())!==u?(r,n=e,t={action:"add",type:"alter",...n},r=t):(wo=r,r=u);var n;return r}())===u&&(t=function(){var r,t,e,n;r=wo,(t=Na())!==u&&$s()!==u?((e=Ta())===u&&(e=Da()),e===u&&(e=null),e!==u&&$s()!==u&&(n=Zu())!==u?(r,a=n,t={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:a},r=t):(wo=r,r=u)):(wo=r,r=u);var o,a;return r}())===u&&(t=$o())===u&&(t=Bo()),t}function $o(){var t,e,n,o;return t=wo,"algorithm"===r.substr(wo,9).toLowerCase()?(e=r.substr(wo,9),wo+=9):(e=u,0===Ao&&_o(E)),e!==u&&$s()!==u?((n=Ts())===u&&(n=null),n!==u&&$s()!==u?("default"===r.substr(wo,7).toLowerCase()?(o=r.substr(wo,7),wo+=7):(o=u,0===Ao&&_o(m)),o===u&&("instant"===r.substr(wo,7).toLowerCase()?(o=r.substr(wo,7),wo+=7):(o=u,0===Ao&&_o(A)),o===u&&("inplace"===r.substr(wo,7).toLowerCase()?(o=r.substr(wo,7),wo+=7):(o=u,0===Ao&&_o(g)),o===u&&("copy"===r.substr(wo,4).toLowerCase()?(o=r.substr(wo,4),wo+=4):(o=u,0===Ao&&_o(T))))),o!==u?(t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u),t}function Bo(){var t,e,n,o;return t=wo,"lock"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(j)),e!==u&&$s()!==u?((n=Ts())===u&&(n=null),n!==u&&$s()!==u?("default"===r.substr(wo,7).toLowerCase()?(o=r.substr(wo,7),wo+=7):(o=u,0===Ao&&_o(m)),o===u&&("none"===r.substr(wo,4).toLowerCase()?(o=r.substr(wo,4),wo+=4):(o=u,0===Ao&&_o(S)),o===u&&("shared"===r.substr(wo,6).toLowerCase()?(o=r.substr(wo,6),wo+=6):(o=u,0===Ao&&_o(_)),o===u&&("exclusive"===r.substr(wo,9).toLowerCase()?(o=r.substr(wo,9),wo+=9):(o=u,0===Ao&&_o(x))))),o!==u?(t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u),t}function qo(){var r,t,e,n,o,a,s,i;return r=wo,(t=_s())===u&&(t=xs()),t!==u&&$s()!==u?((e=ta())===u&&(e=null),e!==u&&$s()!==u?((n=cu())===u&&(n=null),n!==u&&$s()!==u&&(o=tu())!==u&&$s()!==u?((a=lu())===u&&(a=null),a!==u&&$s()!==u?(r,s=n,i=a,r=t={index:e,definition:o,keyword:t.toLowerCase(),index_type:s,resource:"index",index_options:i}):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u),r}function Wo(){var t,e,n,o,a,s,i,c,l;return t=wo,(e=function(){var t,e,n,o;t=wo,"fulltext"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(Yn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="FULLTEXT"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=wo,"spatial"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(Gn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="SPATIAL"):(wo=t,t=u)):(wo=t,t=u);return t}()),e!==u&&$s()!==u?((n=_s())===u&&(n=xs()),n===u&&(n=null),n!==u&&$s()!==u?((o=ta())===u&&(o=null),o!==u&&$s()!==u&&(a=tu())!==u&&$s()!==u?((s=lu())===u&&(s=null),s!==u&&$s()!==u?(t,i=e,l=s,t=e={index:o,definition:a,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"}):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u),t}function Yo(){var t;return(t=function(){var t,e,n,o,a,s;t=wo,(e=Go())===u&&(e=null);e!==u&&$s()!==u?("primary key"===r.substr(wo,11).toLowerCase()?(n=r.substr(wo,11),wo+=11):(n=u,0===Ao&&_o(N)),n!==u&&$s()!==u?((o=cu())===u&&(o=null),o!==u&&$s()!==u&&(a=tu())!==u&&$s()!==u?((s=lu())===u&&(s=null),s!==u?(t,c=n,l=o,f=a,p=s,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},t=e):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u);var i,c,l,f,p;return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c;t=wo,(e=Go())===u&&(e=null);e!==u&&$s()!==u&&(n=function(){var t,e,n,o;t=wo,"unique"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(p));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="UNIQUE"):(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u?((o=_s())===u&&(o=xs()),o===u&&(o=null),o!==u&&$s()!==u?((a=ta())===u&&(a=null),a!==u&&$s()!==u?((s=cu())===u&&(s=null),s!==u&&$s()!==u&&(i=tu())!==u&&$s()!==u?((c=lu())===u&&(c=null),c!==u?(t,f=n,v=o,b=a,d=s,y=i,h=c,e={constraint:(l=e)&&l.constraint,definition:y,constraint_type:v&&`${f.toLowerCase()} ${v.toLowerCase()}`||f.toLowerCase(),keyword:l&&l.keyword,index_type:d,index:b,resource:"constraint",index_options:h},t=e):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u);var l,f,v,b,d,y,h;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=wo,(e=Go())===u&&(e=null);e!==u&&$s()!==u?("foreign key"===r.substr(wo,11).toLowerCase()?(n=r.substr(wo,11),wo+=11):(n=u,0===Ao&&_o(M)),n!==u&&$s()!==u?((o=ta())===u&&(o=null),o!==u&&$s()!==u&&(a=tu())!==u&&$s()!==u?((s=Vo())===u&&(s=null),s!==u?(t,c=n,l=o,f=a,p=s,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},t=e):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u);var i,c,l,f,p;return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c,l,f;t=wo,(e=Go())===u&&(e=null);e!==u&&$s()!==u?("check"===r.substr(wo,5).toLowerCase()?(n=r.substr(wo,5),wo+=5):(n=u,0===Ao&&_o(k)),n!==u&&$s()!==u?(o=wo,"not"===r.substr(wo,3).toLowerCase()?(a=r.substr(wo,3),wo+=3):(a=u,0===Ao&&_o(R)),a!==u&&(s=$s())!==u?("for"===r.substr(wo,3).toLowerCase()?(i=r.substr(wo,3),wo+=3):(i=u,0===Ao&&_o(O)),i!==u&&(c=$s())!==u?("replication"===r.substr(wo,11).toLowerCase()?(l=r.substr(wo,11),wo+=11):(l=u,0===Ao&&_o(U)),l!==u&&(f=$s())!==u?o=a=[a,s,i,c,l,f]:(wo=o,o=u)):(wo=o,o=u)):(wo=o,o=u),o===u&&(o=null),o!==u&&(a=Us())!==u&&(s=$s())!==u&&(i=Pu())!==u&&(c=$s())!==u&&(l=Ms())!==u?(t,p=e,v=o,b=i,e={constraint_type:n.toLowerCase(),keyword:p&&p.keyword,constraint:p&&p.constraint,index_type:v&&{keyword:"not for replication"},definition:[b],resource:"constraint"},t=e):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u);var p,v,b;return t}()),t}function Go(){var r,t,e,n;return r=wo,(t=Is())!==u&&$s()!==u?((e=Zu())===u&&(e=null),e!==u?(r,n=e,r=t={keyword:t.toLowerCase(),constraint:n}):(wo=r,r=u)):(wo=r,r=u),r}function Vo(){var t,e,n,o,a,s,i,c,l,f;return t=wo,(e=function(){var t,e,n,o;t=wo,"references"===r.substr(wo,10).toLowerCase()?(e=r.substr(wo,10),wo+=10):(e=u,0===Ao&&_o(Xn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="REFERENCES"):(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u&&(n=pu())!==u&&$s()!==u&&(o=tu())!==u&&$s()!==u?("match full"===r.substr(wo,10).toLowerCase()?(a=r.substr(wo,10),wo+=10):(a=u,0===Ao&&_o(D)),a===u&&("match partial"===r.substr(wo,13).toLowerCase()?(a=r.substr(wo,13),wo+=13):(a=u,0===Ao&&_o(P)),a===u&&("match simple"===r.substr(wo,12).toLowerCase()?(a=r.substr(wo,12),wo+=12):(a=u,0===Ao&&_o(F)))),a===u&&(a=null),a!==u&&$s()!==u?((s=Qo())===u&&(s=null),s!==u&&$s()!==u?((i=Qo())===u&&(i=null),i!==u?(t,c=a,l=s,f=i,t=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,(e=Qo())!==u&&(t,e={on_action:[e]}),t=e),t}function Qo(){var t,e,n,o;return t=wo,Ha()!==u&&$s()!==u?((e=ka())===u&&(e=Sa()),e!==u&&$s()!==u&&(n=function(){var t,e,n;t=wo,(e=As())!==u&&$s()!==u&&Us()!==u&&$s()!==u?((n=Nu())===u&&(n=null),n!==u&&$s()!==u&&Ms()!==u?(t,t=e={type:"function",name:e,args:n}):(wo=t,t=u)):(wo=t,t=u);t===u&&(t=wo,"restrict"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(H)),e===u&&("cascade"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o($)),e===u&&("set null"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(B)),e===u&&("no action"===r.substr(wo,9).toLowerCase()?(e=r.substr(wo,9),wo+=9):(e=u,0===Ao&&_o(q)),e===u&&("set default"===r.substr(wo,11).toLowerCase()?(e=r.substr(wo,11),wo+=11):(e=u,0===Ao&&_o(W)),e===u&&(e=As()))))),e!==u&&(t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==u?(t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):(wo=t,t=u)):(wo=t,t=u),t}function Xo(){var t,e,n,o,a,s,i,c,l;return t=wo,(e=ga())===u&&(e=null),e!==u&&$s()!==u?((n=function(){var t,e,n;return t=wo,"character"===r.substr(wo,9).toLowerCase()?(e=r.substr(wo,9),wo+=9):(e=u,0===Ao&&_o(Y)),e!==u&&$s()!==u?("set"===r.substr(wo,3).toLowerCase()?(n=r.substr(wo,3),wo+=3):(n=u,0===Ao&&_o(G)),n!==u?(t,t=e="CHARACTER SET"):(wo=t,t=u)):(wo=t,t=u),t}())===u&&("charset"===r.substr(wo,7).toLowerCase()?(n=r.substr(wo,7),wo+=7):(n=u,0===Ao&&_o(V)),n===u&&("collate"===r.substr(wo,7).toLowerCase()?(n=r.substr(wo,7),wo+=7):(n=u,0===Ao&&_o(Q)))),n!==u&&$s()!==u?((o=Ts())===u&&(o=null),o!==u&&$s()!==u&&(a=na())!==u?(t,i=n,c=o,l=a,t=e={keyword:(s=e)&&`${s[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u),t}function Ko(){var t,e,n,o,a,s,i,c,l;return t=wo,"auto_increment"===r.substr(wo,14).toLowerCase()?(e=r.substr(wo,14),wo+=14):(e=u,0===Ao&&_o(f)),e===u&&("avg_row_length"===r.substr(wo,14).toLowerCase()?(e=r.substr(wo,14),wo+=14):(e=u,0===Ao&&_o(X)),e===u&&("key_block_size"===r.substr(wo,14).toLowerCase()?(e=r.substr(wo,14),wo+=14):(e=u,0===Ao&&_o(K)),e===u&&("max_rows"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(z)),e===u&&("min_rows"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(Z)),e===u&&("stats_sample_pages"===r.substr(wo,18).toLowerCase()?(e=r.substr(wo,18),wo+=18):(e=u,0===Ao&&_o(J))))))),e!==u&&$s()!==u?((n=Ts())===u&&(n=null),n!==u&&$s()!==u&&(o=ha())!==u?(t,c=n,l=o,t=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=Xo())===u&&(t=wo,(e=ks())===u&&("connection"===r.substr(wo,10).toLowerCase()?(e=r.substr(wo,10),wo+=10):(e=u,0===Ao&&_o(rr))),e!==u&&$s()!==u?((n=Ts())===u&&(n=null),n!==u&&$s()!==u&&(o=va())!==u?(t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,"compression"===r.substr(wo,11).toLowerCase()?(e=r.substr(wo,11),wo+=11):(e=u,0===Ao&&_o(tr)),e!==u&&$s()!==u?((n=Ts())===u&&(n=null),n!==u&&$s()!==u?(o=wo,39===r.charCodeAt(wo)?(a="'",wo++):(a=u,0===Ao&&_o(er)),a!==u?("zlib"===r.substr(wo,4).toLowerCase()?(s=r.substr(wo,4),wo+=4):(s=u,0===Ao&&_o(nr)),s===u&&("lz4"===r.substr(wo,3).toLowerCase()?(s=r.substr(wo,3),wo+=3):(s=u,0===Ao&&_o(or)),s===u&&("none"===r.substr(wo,4).toLowerCase()?(s=r.substr(wo,4),wo+=4):(s=u,0===Ao&&_o(S)))),s!==u?(39===r.charCodeAt(wo)?(i="'",wo++):(i=u,0===Ao&&_o(er)),i!==u?o=a=[a,s,i]:(wo=o,o=u)):(wo=o,o=u)):(wo=o,o=u),o!==u?(t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,"engine"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(ur)),e!==u&&$s()!==u?((n=Ts())===u&&(n=null),n!==u&&$s()!==u&&(o=na())!==u?(t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}}(e,n,o)):(wo=t,t=u)):(wo=t,t=u)))),t}function zo(){var t,e,n,o,a;return t=wo,(e=bu())!==u&&$s()!==u&&(n=function(){var t,e,n;return t=wo,"read"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(ar)),e!==u&&$s()!==u?("local"===r.substr(wo,5).toLowerCase()?(n=r.substr(wo,5),wo+=5):(n=u,0===Ao&&_o(sr)),n===u&&(n=null),n!==u?(t,t=e={type:"read",suffix:n&&"local"}):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,"low_priority"===r.substr(wo,12).toLowerCase()?(e=r.substr(wo,12),wo+=12):(e=u,0===Ao&&_o(ir)),e===u&&(e=null),e!==u&&$s()!==u?("write"===r.substr(wo,5).toLowerCase()?(n=r.substr(wo,5),wo+=5):(n=u,0===Ao&&_o(cr)),n!==u?(t,t=e={type:"write",prefix:e&&"low_priority"}):(wo=t,t=u)):(wo=t,t=u)),t}())!==u?(t,o=e,a=n,yi.add(`lock::${o.db}::${o.table}`),t=e={table:o,lock_type:a}):(wo=t,t=u),t}function Zo(){var t,e,n,o,a,s,i;return(t=eu())===u&&(t=wo,e=wo,40===r.charCodeAt(wo)?(n="(",wo++):(n=u,0===Ao&&_o(lr)),n!==u&&(o=$s())!==u&&(a=Zo())!==u&&(s=$s())!==u?(41===r.charCodeAt(wo)?(i=")",wo++):(i=u,0===Ao&&_o(fr)),i!==u?e=n=[n,o,a,s,i]:(wo=e,e=u)):(wo=e,e=u),e!==u&&(t,e={...e[2],parentheses_symbol:!0}),t=e),t}function Jo(){var t,e,n,o,a,s,i,c,f;if(t=wo,Ya()!==u)if($s()!==u)if((e=ru())!==u){for(n=[],o=wo,(a=$s())!==u&&(s=Rs())!==u&&(i=$s())!==u&&(c=ru())!==u?o=a=[a,s,i,c]:(wo=o,o=u);o!==u;)n.push(o),o=wo,(a=$s())!==u&&(s=Rs())!==u&&(i=$s())!==u&&(c=ru())!==u?o=a=[a,s,i,c]:(wo=o,o=u);n!==u?(t,t=l(e,n)):(wo=t,t=u)}else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;return t===u&&(t=wo,$s()!==u&&Ya()!==u&&(e=$s())!==u&&(n=function(){var t,e,n,o;t=wo,"RECURSIVE"===r.substr(wo,9)?(e="RECURSIVE",wo+=9):(e=u,0===Ao&&_o(Vt));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(o=$s())!==u&&(a=ru())!==u?(t,(f=a).recursive=!0,t=[f]):(wo=t,t=u)),t}function ru(){var r,t,e,n,o;return r=wo,(t=va())===u&&(t=na()),t!==u&&$s()!==u?((e=tu())===u&&(e=null),e!==u&&$s()!==u&&Da()!==u&&$s()!==u&&Us()!==u&&$s()!==u&&(n=Oo())!==u&&$s()!==u&&Ms()!==u?(r,"string"==typeof(o=t)&&(o={type:"default",value:o}),r=t={name:o,stmt:n,columns:e}):(wo=r,r=u)):(wo=r,r=u),r}function tu(){var r,t;return r=wo,Us()!==u&&$s()!==u&&(t=Lu())!==u&&$s()!==u&&Ms()!==u?(r,r=t):(wo=r,r=u),r}function eu(){var t,e,n,o,a,s,i,c,l,f,p,v,b,d,y,h,m,w,L,C,E;return t=wo,$s()!==u?((e=Jo())===u&&(e=null),e!==u&&$s()!==u&&function(){var t,e,n,o;t=wo,"select"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o($t));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&Bs()!==u?((n=function(){var r,t,e,n,o,a;if(r=wo,(t=nu())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=nu())!==u?n=o=[o,a]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=nu())!==u?n=o=[o,a]:(wo=n,n=u);e!==u?(r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())===u&&(n=null),n!==u&&$s()!==u?((o=Xa())===u&&(o=null),o!==u&&$s()!==u&&(a=ou())!==u&&$s()!==u?((s=su())===u&&(s=null),s!==u&&$s()!==u?((i=wu())===u&&(i=null),i!==u&&$s()!==u?((c=function(){var t,e,n;t=wo,(e=function(){var t,e,n,o;t=wo,"group"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(me));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u&&Ga()!==u&&$s()!==u&&(n=Nu())!==u?(t,e=n.value,t=e):(wo=t,t=u);return t}())===u&&(c=null),c!==u&&$s()!==u?((l=function(){var t,e;t=wo,function(){var t,e,n,o;t=wo,"having"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(Ce));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&(e=Fu())!==u?(t,t=e):(wo=t,t=u);return t}())===u&&(l=null),l!==u&&$s()!==u?((f=Cu())===u&&(f=null),f!==u&&$s()!==u?((p=gu())===u&&(p=null),p!==u?(t,v=e,b=n,d=o,y=a,m=i,w=c,L=l,C=f,E=p,(h=s)&&h.forEach(r=>r.table&&yi.add(`select::${r.db}::${r.table}`)),t={with:v,type:"select",options:b,distinct:d,columns:y,from:h,where:m,groupby:w,having:L,orderby:C,limit:E}):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u),t}function nu(){var t,e;return t=wo,(e=function(){var t;"sql_calc_found_rows"===r.substr(wo,19).toLowerCase()?(t=r.substr(wo,19),wo+=19):(t=u,0===Ao&&_o(Kn));return t}())===u&&((e=function(){var t;"sql_cache"===r.substr(wo,9).toLowerCase()?(t=r.substr(wo,9),wo+=9):(t=u,0===Ao&&_o(zn));return t}())===u&&(e=function(){var t;"sql_no_cache"===r.substr(wo,12).toLowerCase()?(t=r.substr(wo,12),wo+=12):(t=u,0===Ao&&_o(Zn));return t}()),e===u&&(e=function(){var t;"sql_big_result"===r.substr(wo,14).toLowerCase()?(t=r.substr(wo,14),wo+=14):(t=u,0===Ao&&_o(ro));return t}())===u&&(e=function(){var t;"sql_small_result"===r.substr(wo,16).toLowerCase()?(t=r.substr(wo,16),wo+=16):(t=u,0===Ao&&_o(Jn));return t}())===u&&(e=function(){var t;"sql_buffer_result"===r.substr(wo,17).toLowerCase()?(t=r.substr(wo,17),wo+=17):(t=u,0===Ao&&_o(to));return t}())),e!==u&&(t,e=e),t=e}function ou(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Qa())===u&&(t=wo,(e=Os())!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t===u&&(t=Os())),t!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=uu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=uu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=function(r,t){hi.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return t&&t.length>0?fi(e,t):[e]}(0,e)):(wo=r,r=u)}else wo=r,r=u;if(r===u)if(r=wo,(t=uu())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=uu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=uu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=l(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function uu(){var r,t,e,n,o;return r=wo,t=wo,(e=Zu())!==u&&(n=$s())!==u&&(o=Ns())!==u?t=e=[e,n,o]:(wo=t,t=u),t===u&&(t=null),t!==u&&(e=$s())!==u&&(n=Os())!==u?(r,r=t=function(r){const t=r&&r[0]||null;return hi.add(`select::${t}::(.*)`),{expr:{type:"column_ref",table:t,column:"*"},as:null}}(t)):(wo=r,r=u),r===u&&(r=wo,(t=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Pu())!==u){for(e=[],n=wo,(o=$s())!==u?((a=es())===u&&(a=ns())===u&&(a=Hs()),a!==u&&(s=$s())!==u&&(i=Pu())!==u?n=o=[o,a,s,i]:(wo=n,n=u)):(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u?((a=es())===u&&(a=ns())===u&&(a=Hs()),a!==u&&(s=$s())!==u&&(i=Pu())!==u?n=o=[o,a,s,i]:(wo=n,n=u)):(wo=n,n=u);e!==u?(r,t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=ci(t[e][1],n,o)}return o}(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())!==u&&(e=$s())!==u?((n=au())===u&&(n=null),n!==u?(r,r=t={expr:t,as:n}):(wo=r,r=u)):(wo=r,r=u)),r}function au(){var r,t,e;return r=wo,(t=Da())!==u&&$s()!==u&&(e=function(){var r,t;r=wo,(t=na())!==u?(wo,(function(r){if(!0===si[r.toUpperCase()])throw new Error("Error: "+JSON.stringify(r)+" is a reserved word, can not as alias clause");return!1}(t)?u:void 0)!==u?(r,r=t=t):(wo=r,r=u)):(wo=r,r=u);r===u&&(r=wo,(t=Ju())!==u&&(r,t=t),r=t);return r}())!==u?(r,r=t=e):(wo=r,r=u),r===u&&(r=wo,(t=Da())===u&&(t=null),t!==u&&$s()!==u&&(e=Zu())!==u?(r,r=t=e):(wo=r,r=u)),r}function su(){var t,e;return t=wo,function(){var t,e,n,o;t=wo,"from"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Jt));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&(e=pu())!==u?(t,t=e):(wo=t,t=u),t}function iu(){var r,t,e;return r=wo,(t=yu())!==u&&$s()!==u&&Ta()!==u&&$s()!==u&&(e=yu())!==u?(r,r=t=[t,e]):(wo=r,r=u),r}function cu(){var t,e;return t=wo,Wa()!==u&&$s()!==u?("btree"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(pr)),e===u&&("hash"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(vr))),e!==u?(t,t={keyword:"using",type:e.toLowerCase()}):(wo=t,t=u)):(wo=t,t=u),t}function lu(){var r,t,e,n,o,a;if(r=wo,(t=fu())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=fu())!==u?n=o=[o,a]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=fu())!==u?n=o=[o,a]:(wo=n,n=u);e!==u?(r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function fu(){var t,e,n,o,a,s;return t=wo,(e=function(){var t,e,n,o;t=wo,"key_block_size"===r.substr(wo,14).toLowerCase()?(e=r.substr(wo,14),wo+=14):(e=u,0===Ao&&_o(K));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="KEY_BLOCK_SIZE"):(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u?((n=Ts())===u&&(n=null),n!==u&&$s()!==u&&(o=ha())!==u?(t,a=n,s=o,t=e={type:e.toLowerCase(),symbol:a,expr:s}):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=cu())===u&&(t=wo,"with"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(br)),e!==u&&$s()!==u?("parser"===r.substr(wo,6).toLowerCase()?(n=r.substr(wo,6),wo+=6):(n=u,0===Ao&&_o(dr)),n!==u&&$s()!==u&&(o=na())!==u?(t,t=e={type:"with parser",expr:o}):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,"visible"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(yr)),e===u&&("invisible"===r.substr(wo,9).toLowerCase()?(e=r.substr(wo,9),wo+=9):(e=u,0===Ao&&_o(hr))),e!==u&&(t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===u&&(t=Ws()))),t}function pu(){var r,t,e,n;if(r=wo,(t=bu())!==u){for(e=[],n=vu();n!==u;)e.push(n),n=vu();e!==u?(r,r=t=mr(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function vu(){var r,t,e;return r=wo,$s()!==u&&(t=Rs())!==u&&$s()!==u&&(e=bu())!==u?(r,r=e):(wo=r,r=u),r===u&&(r=wo,$s()!==u&&(t=function(){var r,t,e,n,o,a,s,i,c,l,f;if(r=wo,(t=du())!==u)if($s()!==u)if((e=bu())!==u)if($s()!==u)if((n=Wa())!==u)if($s()!==u)if(Us()!==u)if($s()!==u)if((o=na())!==u){for(a=[],s=wo,(i=$s())!==u&&(c=Rs())!==u&&(l=$s())!==u&&(f=na())!==u?s=i=[i,c,l,f]:(wo=s,s=u);s!==u;)a.push(s),s=wo,(i=$s())!==u&&(c=Rs())!==u&&(l=$s())!==u&&(f=na())!==u?s=i=[i,c,l,f]:(wo=s,s=u);a!==u&&(s=$s())!==u&&(i=Ms())!==u?(r,p=t,b=o,d=a,(v=e).join=p,v.using=fi(b,d),r=t=v):(wo=r,r=u)}else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;var p,v,b,d;r===u&&(r=wo,(t=du())!==u&&$s()!==u&&(e=bu())!==u&&$s()!==u?((n=mu())===u&&(n=null),n!==u?(r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):(wo=r,r=u)):(wo=r,r=u),r===u&&(r=wo,(t=du())!==u&&$s()!==u&&(e=Us())!==u&&$s()!==u&&(n=Oo())!==u&&$s()!==u&&Ms()!==u&&$s()!==u?((o=au())===u&&(o=null),o!==u&&(a=$s())!==u?((s=mu())===u&&(s=null),s!==u?(r,t=function(r,t,e,n){return t.parentheses=!0,{expr:t,as:e,join:r,on:n}}(t,n,o,s),r=t):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)));return r}())!==u?(r,r=t):(wo=r,r=u)),r}function bu(){var t,e,n,o,a,s;return t=wo,(e=function(){var t;"dual"===r.substr(wo,4).toLowerCase()?(t=r.substr(wo,4),wo+=4):(t=u,0===Ao&&_o($n));return t}())!==u&&(t,e={type:"dual"}),(t=e)===u&&(t=wo,(e=yu())!==u&&$s()!==u?((n=au())===u&&(n=null),n!==u?(t,s=n,t=e="var"===(a=e).type?(a.as=s,a):{db:a.db,table:a.table,as:s}):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,(e=Us())!==u&&$s()!==u&&(n=Oo())!==u&&$s()!==u&&Ms()!==u&&$s()!==u?((o=au())===u&&(o=null),o!==u?(t,t=e=function(r,t){return r.parentheses=!0,{expr:r,as:t}}(n,o)):(wo=t,t=u)):(wo=t,t=u))),t}function du(){var t,e,n,o;return t=wo,(e=function(){var t,e,n,o;t=wo,"left"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(se));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(n=$s())!==u?((o=Ba())===u&&(o=null),o!==u&&$s()!==u&&$a()!==u?(t,t=e="LEFT JOIN"):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,(e=function(){var t,e,n,o;t=wo,"right"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(ie));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(n=$s())!==u?((o=Ba())===u&&(o=null),o!==u&&$s()!==u&&$a()!==u?(t,t=e="RIGHT JOIN"):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,(e=function(){var t,e,n,o;t=wo,"full"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(ce));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(n=$s())!==u?((o=Ba())===u&&(o=null),o!==u&&$s()!==u&&$a()!==u?(t,t=e="FULL JOIN"):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,e=wo,(n=function(){var t,e,n,o;t=wo,"inner"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(le));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(o=$s())!==u?e=n=[n,o]:(wo=e,e=u),e===u&&(e=null),e!==u&&(n=$a())!==u?(t,t=e="INNER JOIN"):(wo=t,t=u)))),t}function yu(){var r,t,e,n,o,a,s,i;return r=wo,(t=Zu())!==u?(e=wo,(n=$s())!==u&&(o=Ns())!==u&&(a=$s())!==u&&(s=Zu())!==u?e=n=[n,o,a,s]:(wo=e,e=u),e===u&&(e=null),e!==u?(r,r=t=function(r,t){const e={db:null,table:r};return null!==t&&(e.db=r,e.table=t[3]),e}(t,e)):(wo=r,r=u)):(wo=r,r=u),r===u&&(r=wo,(t=ni())!==u&&(r,(i=t).db=null,i.table=i.name,t=i),r=t),r}function hu(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Pu())!==u){for(e=[],n=wo,(o=$s())!==u?((a=es())===u&&(a=ns()),a!==u&&(s=$s())!==u&&(i=Pu())!==u?n=o=[o,a,s,i]:(wo=n,n=u)):(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u?((a=es())===u&&(a=ns()),a!==u&&(s=$s())!==u&&(i=Pu())!==u?n=o=[o,a,s,i]:(wo=n,n=u)):(wo=n,n=u);e!==u?(r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=ci(t[r][1],n,t[r][3]);return n}(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function mu(){var r,t;return r=wo,Ha()!==u&&$s()!==u&&(t=Fu())!==u?(r,r=t):(wo=r,r=u),r}function wu(){var t,e;return t=wo,function(){var t,e,n,o;t=wo,"where"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(he));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&(e=Fu())!==u?(t,t=e):(wo=t,t=u),t}function Lu(){var r,t,e,n,o,a,s,i;if(r=wo,(t=zu())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=zu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=zu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=l(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function Cu(){var t,e;return t=wo,function(){var t,e,n,o;t=wo,"order"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(Le));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&Ga()!==u&&$s()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Eu())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=Eu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=Eu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,t=l(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())!==u?(t,t=e):(wo=t,t=u),t}function Eu(){var t,e,n;return t=wo,(e=Pu())!==u&&$s()!==u?((n=function(){var t,e,n,o;t=wo,"desc"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Te));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="DESC"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=wo,"asc"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(ge));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="ASC"):(wo=t,t=u)):(wo=t,t=u);return t}()),n===u&&(n=null),n!==u?(t,t=e={expr:e,type:n}):(wo=t,t=u)):(wo=t,t=u),t}function Au(){var r;return(r=ha())===u&&(r=sa()),r}function gu(){var t,e,n,o,a,s,i,c;return t=wo,Va()!==u&&$s()!==u?("first"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(wr)),e!==u&&$s()!==u&&(n=Au())!==u&&$s()!==u?("rows"===r.substr(wo,4).toLowerCase()?(o=r.substr(wo,4),wo+=4):(o=u,0===Ao&&_o(Lr)),o!==u&&$s()!==u?("only"===r.substr(wo,4).toLowerCase()?(a=r.substr(wo,4),wo+=4):(a=u,0===Ao&&_o(Cr)),a!==u?(t,t={fetch:{prefix:"fetch first",value:n,suffix:"rows only"}}):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,function(){var t,e,n,o;t=wo,"offset"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(Ae));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="OFFSET"):(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&(e=Au())!==u&&$s()!==u?("rows"===r.substr(wo,4).toLowerCase()?(n=r.substr(wo,4),wo+=4):(n=u,0===Ao&&_o(Lr)),n!==u&&$s()!==u&&(o=Va())!==u&&$s()!==u?("next"===r.substr(wo,4).toLowerCase()?(a=r.substr(wo,4),wo+=4):(a=u,0===Ao&&_o(Er)),a!==u&&$s()!==u&&(s=Au())!==u&&$s()!==u?("rows"===r.substr(wo,4).toLowerCase()?(i=r.substr(wo,4),wo+=4):(i=u,0===Ao&&_o(Lr)),i!==u&&$s()!==u?("only"===r.substr(wo,4).toLowerCase()?(c=r.substr(wo,4),wo+=4):(c=u,0===Ao&&_o(Cr)),c!==u?(t,t=function(r,t){return{offset:{prefix:"offset",value:r,suffix:"rows"},fetch:{prefix:"fetch next",value:t,suffix:"rows only"}}}(e,s)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)),t}function Tu(){var r,t,e,n,o,a,s,i;if(r=wo,(t=ju())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=ju())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=ju())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=l(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function ju(){var t,e,n,o,a,s,i,c,l;return t=wo,e=wo,(n=Zu())!==u&&(o=$s())!==u&&(a=Ns())!==u?e=n=[n,o,a]:(wo=e,e=u),e===u&&(e=null),e!==u&&(n=$s())!==u&&(o=ra())!==u&&(a=$s())!==u?(61===r.charCodeAt(wo)?(s="=",wo++):(s=u,0===Ao&&_o(Ar)),s!==u&&$s()!==u&&(i=Gu())!==u?(t,t=e={column:o,value:i,table:(l=e)&&l[0]}):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,e=wo,(n=Zu())!==u&&(o=$s())!==u&&(a=Ns())!==u?e=n=[n,o,a]:(wo=e,e=u),e===u&&(e=null),e!==u&&(n=$s())!==u&&(o=ra())!==u&&(a=$s())!==u?(61===r.charCodeAt(wo)?(s="=",wo++):(s=u,0===Ao&&_o(Ar)),s!==u&&$s()!==u&&(i=qa())!==u&&$s()!==u&&Us()!==u&&$s()!==u&&(c=zu())!==u&&$s()!==u&&Ms()!==u?(t,t=e=function(r,t,e){return{column:t,value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):(wo=t,t=u)):(wo=t,t=u)),t}function Su(){var r;return(r=function(){var r,t;r=wo,qa()!==u&&$s()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Iu())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=Iu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=Iu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,t=l(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())!==u?(r,r=t):(wo=r,r=u);return r}())===u&&(r=eu()),r}function _u(){var r,t,e,n,o,a,s,i,c;if(r=wo,Oa()!==u)if($s()!==u)if((t=Us())!==u)if($s()!==u)if((e=na())!==u){for(n=[],o=wo,(a=$s())!==u&&(s=Rs())!==u&&(i=$s())!==u&&(c=na())!==u?o=a=[a,s,i,c]:(wo=o,o=u);o!==u;)n.push(o),o=wo,(a=$s())!==u&&(s=Rs())!==u&&(i=$s())!==u&&(c=na())!==u?o=a=[a,s,i,c]:(wo=o,o=u);n!==u&&(o=$s())!==u&&(a=Ms())!==u?(r,r=fi(e,n)):(wo=r,r=u)}else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;return r===u&&(r=wo,Oa()!==u&&$s()!==u&&(t=Iu())!==u?(r,r=t):(wo=r,r=u)),r}function xu(){var t,e,n;return t=wo,Ha()!==u&&$s()!==u?("duplicate"===r.substr(wo,9).toLowerCase()?(e=r.substr(wo,9),wo+=9):(e=u,0===Ao&&_o(gr)),e!==u&&$s()!==u&&xs()!==u&&$s()!==u&&Sa()!==u&&$s()!==u&&(n=Tu())!==u?(t,t={keyword:"on duplicate key update",set:n}):(wo=t,t=u)):(wo=t,t=u),t}function ku(){var t,e;return t=wo,(e=function(){var t,e,n,o;t=wo,"insert"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(Gt));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(t,e="insert"),(t=e)===u&&(t=wo,(e=Ia())!==u&&(t,e="replace"),t=e),t}function Iu(){var r,t;return r=wo,Us()!==u&&$s()!==u&&(t=Nu())!==u&&$s()!==u&&Ms()!==u?(r,r=t):(wo=r,r=u),r}function Nu(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Pu())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=Pu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=Pu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=function(r,t){const e={type:"expr_list"};return e.value=fi(r,t),e}(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function Ru(){var t,e,n;return t=wo,function(){var t,e,n,o;t=wo,"interval"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(Ln));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="INTERVAL"):(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&(e=Pu())!==u&&$s()!==u&&(n=function(){var t;(t=function(){var t,e,n,o;t=wo,"year"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Cn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="YEAR"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"month"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(En));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="MONTH"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"day"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(An));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="DAY"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"hour"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(gn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="HOUR"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"minute"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(Tn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="MINUTE"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"second"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(jn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="SECOND"):(wo=t,t=u)):(wo=t,t=u);return t}());return t}())!==u?(t,t={type:"interval",expr:e,unit:n.toLowerCase()}):(wo=t,t=u),t}function Ou(){var r,t,e,n,o,a;if(r=wo,(t=Uu())!==u)if($s()!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Uu())!==u?n=o=[o,a]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Uu())!==u?n=o=[o,a]:(wo=n,n=u);e!==u?(r,r=t=i(t,e)):(wo=r,r=u)}else wo=r,r=u;else wo=r,r=u;return r}function Uu(){var t,e,n;return t=wo,function(){var t,e,n,o;t=wo,"when"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Be));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&(e=Fu())!==u&&$s()!==u&&function(){var t,e,n,o;t=wo,"then"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(qe));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&(n=Pu())!==u?(t,t={type:"when",cond:e,result:n}):(wo=t,t=u),t}function Mu(){var t,e;return t=wo,function(){var t,e,n,o;t=wo,"else"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(We));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&(e=Pu())!==u?(t,t={type:"else",result:e}):(wo=t,t=u),t}function Du(){var r;return(r=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Ku())!==u){if(e=[],n=wo,(o=$s())!==u&&(a=Hs())!==u&&(s=$s())!==u&&(i=Ku())!==u?n=o=[o,a,s,i]:(wo=n,n=u),n!==u)for(;n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Hs())!==u&&(s=$s())!==u&&(i=Ku())!==u?n=o=[o,a,s,i]:(wo=n,n=u);else e=u;e!==u&&(n=$s())!==u?((o=qu())===u&&(o=null),o!==u?(r,t=function(r,t,e){const n=pi(r,t);return null===e?n:"arithmetic"===e.type?pi(n,e.tail):ci(e.op,n,e.right)}(t,e,o),r=t):(wo=r,r=u)):(wo=r,r=u)}else wo=r,r=u;return r}())===u&&(r=function(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Hu())!==u){for(e=[],n=wo,(o=Bs())!==u&&(a=ns())!==u&&(s=$s())!==u&&(i=Hu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=Bs())!==u&&(a=ns())!==u&&(s=$s())!==u&&(i=Hu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,t=Tr(t,e),r=t):(wo=r,r=u)}else wo=r,r=u;return r}())===u&&(r=function(){var r,t,e,n,o,a;if(r=wo,(t=Vu())!==u){if(e=[],n=wo,(o=$s())!==u&&(a=Ku())!==u?n=o=[o,a]:(wo=n,n=u),n!==u)for(;n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Ku())!==u?n=o=[o,a]:(wo=n,n=u);else e=u;e!==u?(r,t=ii(t,e[0][1]),r=t):(wo=r,r=u)}else wo=r,r=u;return r}()),r}function Pu(){var r;return(r=Du())===u&&(r=Oo()),r}function Fu(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Pu())!==u){for(e=[],n=wo,(o=$s())!==u?((a=es())===u&&(a=ns())===u&&(a=Rs()),a!==u&&(s=$s())!==u&&(i=Pu())!==u?n=o=[o,a,s,i]:(wo=n,n=u)):(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u?((a=es())===u&&(a=ns())===u&&(a=Rs()),a!==u&&(s=$s())!==u&&(i=Pu())!==u?n=o=[o,a,s,i]:(wo=n,n=u)):(wo=n,n=u);e!==u?(r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=ci(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=n,r}return n}(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function Hu(){var r,t,e,n,o,a,s,i;if(r=wo,(t=$u())!==u){for(e=[],n=wo,(o=Bs())!==u&&(a=es())!==u&&(s=$s())!==u&&(i=$u())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=Bs())!==u&&(a=es())!==u&&(s=$s())!==u&&(i=$u())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=Tr(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function $u(){var t,e,n,o,a;return(t=Bu())===u&&(t=function(){var r,t,e;r=wo,(t=function(){var r,t,e,n,o;r=wo,t=wo,(e=ts())!==u&&(n=$s())!==u&&(o=rs())!==u?t=e=[e,n,o]:(wo=t,t=u);t!==u&&(r,t=Sr(t));(r=t)===u&&(r=rs());return r}())!==u&&$s()!==u&&Us()!==u&&$s()!==u&&(e=Oo())!==u&&$s()!==u&&Ms()!==u?(r,n=t,(o=e).parentheses=!0,t=ii(n,o),r=t):(wo=r,r=u);var n,o;return r}())===u&&(t=wo,(e=ts())===u&&(e=wo,33===r.charCodeAt(wo)?(n="!",wo++):(n=u,0===Ao&&_o(jr)),n!==u?(o=wo,Ao++,61===r.charCodeAt(wo)?(a="=",wo++):(a=u,0===Ao&&_o(Ar)),Ao--,a===u?o=void 0:(wo=o,o=u),o!==u?e=n=[n,o]:(wo=e,e=u)):(wo=e,e=u)),e!==u&&(n=$s())!==u&&(o=$u())!==u?(t,t=e=ii("NOT",o)):(wo=t,t=u)),t}function Bu(){var r,t,e,n,o;return r=wo,(t=Gu())!==u&&$s()!==u?((e=qu())===u&&(e=null),e!==u?(r,n=t,r=t=null===(o=e)?n:"arithmetic"===o.type?pi(n,o.tail):ci(o.op,n,o.right)):(wo=r,r=u)):(wo=r,r=u),r===u&&(r=va())===u&&(r=zu()),r}function qu(){var r;return(r=function(){var r,t,e,n,o,a,s;r=wo,t=[],e=wo,(n=$s())!==u&&(o=Wu())!==u&&(a=$s())!==u&&(s=Gu())!==u?e=n=[n,o,a,s]:(wo=e,e=u);if(e!==u)for(;e!==u;)t.push(e),e=wo,(n=$s())!==u&&(o=Wu())!==u&&(a=$s())!==u&&(s=Gu())!==u?e=n=[n,o,a,s]:(wo=e,e=u);else t=u;t!==u&&(r,t={type:"arithmetic",tail:t});return r=t}())===u&&(r=function(){var r,t,e,n;r=wo,(t=Yu())!==u&&$s()!==u&&(e=Us())!==u&&$s()!==u&&(n=Nu())!==u&&$s()!==u&&Ms()!==u?(r,r=t={op:t,right:n}):(wo=r,r=u);r===u&&(r=wo,(t=Yu())!==u&&$s()!==u?((e=ni())===u&&(e=va()),e!==u?(r,t=function(r,t){return{op:r,right:t}}(t,e),r=t):(wo=r,r=u)):(wo=r,r=u));return r}())===u&&(r=function(){var r,t,e,n;r=wo,(t=function(){var r,t,e,n,o;r=wo,t=wo,(e=ts())!==u&&(n=$s())!==u&&(o=Ka())!==u?t=e=[e,n,o]:(wo=t,t=u);t!==u&&(r,t=Sr(t));(r=t)===u&&(r=Ka());return r}())!==u&&$s()!==u&&(e=Gu())!==u&&$s()!==u&&es()!==u&&$s()!==u&&(n=Gu())!==u?(r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):(wo=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o;r=wo,(t=Za())!==u&&(e=$s())!==u&&(n=Gu())!==u?(r,r=t={op:"IS",right:n}):(wo=r,r=u);r===u&&(r=wo,t=wo,(e=Za())!==u&&(n=$s())!==u&&(o=ts())!==u?t=e=[e,n,o]:(wo=t,t=u),t!==u&&(e=$s())!==u&&(n=Gu())!==u?(r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):(wo=r,r=u));return r}())===u&&(r=function(){var r,t,e;r=wo,(t=function(){var r,t,e,n,o;r=wo,t=wo,(e=ts())!==u&&(n=$s())!==u&&(o=Ja())!==u?t=e=[e,n,o]:(wo=t,t=u);t!==u&&(r,t=Sr(t));(r=t)===u&&(r=Ja());return r}())!==u&&$s()!==u?((e=fa())===u&&(e=Bu()),e!==u?(r,r=t={op:t,right:e}):(wo=r,r=u)):(wo=r,r=u);return r}()),r}function Wu(){var t;return">="===r.substr(wo,2)?(t=">=",wo+=2):(t=u,0===Ao&&_o(_r)),t===u&&(62===r.charCodeAt(wo)?(t=">",wo++):(t=u,0===Ao&&_o(xr)),t===u&&("<="===r.substr(wo,2)?(t="<=",wo+=2):(t=u,0===Ao&&_o(kr)),t===u&&("<>"===r.substr(wo,2)?(t="<>",wo+=2):(t=u,0===Ao&&_o(Ir)),t===u&&(60===r.charCodeAt(wo)?(t="<",wo++):(t=u,0===Ao&&_o(Nr)),t===u&&(61===r.charCodeAt(wo)?(t="=",wo++):(t=u,0===Ao&&_o(Ar)),t===u&&("!="===r.substr(wo,2)?(t="!=",wo+=2):(t=u,0===Ao&&_o(Rr)))))))),t}function Yu(){var r,t,e,n,o;return r=wo,t=wo,(e=ts())!==u&&(n=$s())!==u&&(o=za())!==u?t=e=[e,n,o]:(wo=t,t=u),t!==u&&(r,t=Sr(t)),(r=t)===u&&(r=za()),r}function Gu(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Qu())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Vu())!==u&&(s=$s())!==u&&(i=Qu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Vu())!==u&&(s=$s())!==u&&(i=Qu())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=Tr(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function Vu(){var t;return 43===r.charCodeAt(wo)?(t="+",wo++):(t=u,0===Ao&&_o(Or)),t===u&&(45===r.charCodeAt(wo)?(t="-",wo++):(t=u,0===Ao&&_o(Ur))),t}function Qu(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Ku())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Xu())!==u&&(s=$s())!==u&&(i=Ku())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Xu())!==u&&(s=$s())!==u&&(i=Ku())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=pi(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function Xu(){var t;return 42===r.charCodeAt(wo)?(t="*",wo++):(t=u,0===Ao&&_o(Mr)),t===u&&(47===r.charCodeAt(wo)?(t="/",wo++):(t=u,0===Ao&&_o(Dr)),t===u&&(37===r.charCodeAt(wo)?(t="%",wo++):(t=u,0===Ao&&_o(Pr)))),t}function Ku(){var t,e,n;return(t=function(){var t,e,n,o,a,s,i;t=wo,(e=as())!==u&&$s()!==u&&Us()!==u&&$s()!==u&&(n=Pu())!==u&&$s()!==u&&Da()!==u&&$s()!==u&&(o=ui())!==u&&$s()!==u&&(a=Ms())!==u?(t,c=n,l=o,e={type:"cast",keyword:e.toLowerCase(),expr:c,symbol:"as",target:l},t=e):(wo=t,t=u);var c,l;t===u&&(t=wo,(e=as())!==u&&$s()!==u&&Us()!==u&&$s()!==u&&(n=Pu())!==u&&$s()!==u&&Da()!==u&&$s()!==u&&(o=ls())!==u&&$s()!==u&&(a=Us())!==u&&$s()!==u&&(s=ma())!==u&&$s()!==u&&Ms()!==u&&$s()!==u&&(i=Ms())!==u?(t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:{dataType:"DECIMAL("+e+")"}}}(e,n,s),t=e):(wo=t,t=u),t===u&&(t=wo,(e=as())!==u&&$s()!==u&&Us()!==u&&$s()!==u&&(n=Pu())!==u&&$s()!==u&&Da()!==u&&$s()!==u&&(o=ls())!==u&&$s()!==u&&(a=Us())!==u&&$s()!==u&&(s=ma())!==u&&$s()!==u&&Rs()!==u&&$s()!==u&&(i=ma())!==u&&$s()!==u&&Ms()!==u&&$s()!==u&&Ms()!==u?(t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:{dataType:"DECIMAL("+e+", "+n+")"}}}(e,n,s,i),t=e):(wo=t,t=u),t===u&&(t=wo,(e=as())!==u&&$s()!==u&&Us()!==u&&$s()!==u&&(n=Pu())!==u&&$s()!==u&&Da()!==u&&$s()!==u&&(o=function(){var t;(t=function(){var t,e,n,o;t=wo,"signed"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(ze));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="SIGNED"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=fs());return t}())!==u&&$s()!==u?((a=vs())===u&&(a=null),a!==u&&$s()!==u&&(s=Ms())!==u?(t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:{dataType:e+(n?" "+n:"")}}}(e,n,o,a),t=e):(wo=t,t=u)):(wo=t,t=u))));return t}())===u&&(t=fa())===u&&(t=function(){var t;(t=function(){var t,e,n,o;t=wo,(e=function(){var t,e,n,o;t=wo,"count"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(Ue));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="COUNT"):(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&$s()!==u&&Us()!==u&&$s()!==u&&(n=function(){var t,e,n,o,a,s,i,c,l,f;t=wo,(e=function(){var t,e;t=wo,42===r.charCodeAt(wo)?(e="*",wo++):(e=u,0===Ao&&_o(Mr));e!==u&&(t,e={type:"star",value:"*"});return t=e}())!==u&&(t,e={expr:e});if((t=e)===u){if(t=wo,(e=Xa())===u&&(e=null),e!==u)if($s()!==u)if((n=Us())!==u)if($s()!==u)if((o=Pu())!==u)if($s()!==u)if(Ms()!==u){for(a=[],s=wo,(i=$s())!==u?((c=es())===u&&(c=ns()),c!==u&&(l=$s())!==u&&(f=Pu())!==u?s=i=[i,c,l,f]:(wo=s,s=u)):(wo=s,s=u);s!==u;)a.push(s),s=wo,(i=$s())!==u?((c=es())===u&&(c=ns()),c!==u&&(l=$s())!==u&&(f=Pu())!==u?s=i=[i,c,l,f]:(wo=s,s=u)):(wo=s,s=u);a!==u&&(s=$s())!==u?((i=Cu())===u&&(i=null),i!==u?(t,e=function(r,t,e,n){const o=e.length;let u=t;u.parentheses=!0;for(let r=0;r<o;++r)u=ci(e[r][1],u,e[r][3]);return{distinct:r,expr:u,orderby:n}}(e,o,a,i),t=e):(wo=t,t=u)):(wo=t,t=u)}else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;t===u&&(t=wo,(e=Xa())===u&&(e=null),e!==u&&$s()!==u&&(n=hu())!==u&&$s()!==u?((o=Cu())===u&&(o=null),o!==u?(t,t=e={distinct:e,expr:n,orderby:o}):(wo=t,t=u)):(wo=t,t=u))}return t}())!==u&&$s()!==u&&Ms()!==u&&$s()!==u?((o=ca())===u&&(o=null),o!==u?(t,t=e={type:"aggr_func",name:e,args:n,over:o}):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n;t=wo,(e=function(){var t;(t=function(){var t,e,n,o;t=wo,"sum"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(Pe));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="SUM"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"max"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(Me));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="MAX"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"min"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(De));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="MIN"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"avg"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(Fe));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="AVG"):(wo=t,t=u)):(wo=t,t=u);return t}());return t}())!==u&&$s()!==u&&Us()!==u&&$s()!==u&&(n=Gu())!==u&&$s()!==u&&Ms()!==u?(t,t=e={type:"aggr_func",name:e,args:{expr:n}}):(wo=t,t=u);return t}());return t}())===u&&(t=function(){var t,e,n,o,a;t=wo,(e=function(){var t;(t=la())===u&&(t=function(){var t,e,n,o;t=wo,"current_user"===r.substr(wo,12).toLowerCase()?(e=r.substr(wo,12),wo+=12):(e=u,0===Ao&&_o(xn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="CURRENT_USER"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"user"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(mn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="USER"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"session_user"===r.substr(wo,12).toLowerCase()?(e=r.substr(wo,12),wo+=12):(e=u,0===Ao&&_o(kn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="SESSION_USER"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"system_user"===r.substr(wo,11).toLowerCase()?(e=r.substr(wo,11),wo+=11):(e=u,0===Ao&&_o(In));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="SYSTEM_USER"):(wo=t,t=u)):(wo=t,t=u);return t}());return t}())!==u&&$s()!==u&&(n=Us())!==u&&$s()!==u?((o=Nu())===u&&(o=null),o!==u&&$s()!==u&&Ms()!==u&&$s()!==u?((a=ca())===u&&(a=null),a!==u?(t,t=e={type:"function",name:e,args:o||{type:"expr_list",value:[]},over:a}):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u);t===u&&(t=wo,(e=la())!==u&&$s()!==u?((n=ia())===u&&(n=null),n!==u?(t,t=e={type:"function",name:e,over:n}):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=wo,(e=ri())!==u&&$s()!==u&&(n=Us())!==u&&$s()!==u?((o=Fu())===u&&(o=null),o!==u&&$s()!==u&&Ms()!==u&&$s()!==u?((a=ca())===u&&(a=null),a!==u?(t,e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e}}(e,o,a),t=e):(wo=t,t=u)):(wo=t,t=u)):(wo=t,t=u)));return t}())===u&&(t=function(){var r,t,e,n,o,a,s,i;return r=wo,os()!==u&&$s()!==u&&(t=Ou())!==u&&$s()!==u?((e=Mu())===u&&(e=null),e!==u&&$s()!==u&&(n=us())!==u&&$s()!==u?((o=os())===u&&(o=null),o!==u?(r,s=t,(i=e)&&s.push(i),r={type:"case",expr:null,args:s}):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u),r===u&&(r=wo,os()!==u&&$s()!==u&&(t=Pu())!==u&&$s()!==u&&(e=Ou())!==u&&$s()!==u?((n=Mu())===u&&(n=null),n!==u&&$s()!==u&&(o=us())!==u&&$s()!==u?((a=os())===u&&(a=null),a!==u?(r,r=function(r,t,e){return e&&t.push(e),{type:"case",expr:r,args:t}}(t,e,n)):(wo=r,r=u)):(wo=r,r=u)):(wo=r,r=u)),r}())===u&&(t=Ru())===u&&(t=zu())===u&&(t=sa())===u&&(t=wo,Us()!==u&&$s()!==u&&(e=Fu())!==u&&$s()!==u&&Ms()!==u?(t,(n=e).parentheses=!0,t=n):(wo=t,t=u),t===u&&(t=ni())),t}function zu(){var r,t,e,n,o,a,s,i,c,l,f;if(r=wo,t=wo,(e=Zu())!==u&&(n=$s())!==u&&(o=Ns())!==u&&(a=$s())!==u?t=e=[e,n,o,a]:(wo=t,t=u),t===u&&(t=null),t!==u)if((e=ta())!==u)if((n=$s())!==u){if(o=[],a=wo,(s=Fs())===u&&(s=Ps()),s!==u&&(i=$s())!==u?((c=va())===u&&(c=ha()),c!==u?a=s=[s,i,c]:(wo=a,a=u)):(wo=a,a=u),a!==u)for(;a!==u;)o.push(a),a=wo,(s=Fs())===u&&(s=Ps()),s!==u&&(i=$s())!==u?((c=va())===u&&(c=ha()),c!==u?a=s=[s,i,c]:(wo=a,a=u)):(wo=a,a=u);else o=u;o!==u&&(a=$s())!==u?((s=Fo())===u&&(s=null),s!==u?(r,r=t=function(r,t,e,n){const o=r&&r[0]||null;return hi.add(`select::${o}::${t}`),{type:"column_ref",table:o,column:t,collate:n,arrows:e.map(r=>r[0]),properties:e.map(r=>r[2])}}(t,e,o,s)):(wo=r,r=u)):(wo=r,r=u)}else wo=r,r=u;else wo=r,r=u;else wo=r,r=u;return r===u&&(r=wo,(t=Zu())!==u&&(e=$s())!==u&&(n=Ns())!==u&&(o=$s())!==u&&(a=ra())!==u?(r,l=t,f=a,hi.add(`select::${l}::${f}`),r=t={type:"column_ref",table:l,column:f}):(wo=r,r=u),r===u&&(r=wo,(t=ta())!==u&&(r,t=function(r){return hi.add("select::null::"+r),{type:"column_ref",table:null,column:r}}(t)),r=t)),r}function Zu(){var r,t;return r=wo,(t=na())!==u?(wo,(Fr(t)?u:void 0)!==u?(r,r=t=t):(wo=r,r=u)):(wo=r,r=u),r===u&&(r=wo,(t=Ju())!==u&&(r,t=t),r=t),r}function Ju(){var t;return(t=function(){var t,e,n,o;t=wo,34===r.charCodeAt(wo)?(e='"',wo++):(e=u,0===Ao&&_o(Hr));if(e!==u){if(n=[],$r.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(Br)),o!==u)for(;o!==u;)n.push(o),$r.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(Br));else n=u;n!==u?(34===r.charCodeAt(wo)?(o='"',wo++):(o=u,0===Ao&&_o(Hr)),o!==u?(t,e=qr(n),t=e):(wo=t,t=u)):(wo=t,t=u)}else wo=t,t=u;return t}())===u&&(t=function(){var t,e,n,o;t=wo,39===r.charCodeAt(wo)?(e="'",wo++):(e=u,0===Ao&&_o(er));if(e!==u){if(n=[],Wr.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(Yr)),o!==u)for(;o!==u;)n.push(o),Wr.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(Yr));else n=u;n!==u?(39===r.charCodeAt(wo)?(o="'",wo++):(o=u,0===Ao&&_o(er)),o!==u?(t,e=qr(n),t=e):(wo=t,t=u)):(wo=t,t=u)}else wo=t,t=u;return t}())===u&&(t=function(){var t,e,n,o;t=wo,96===r.charCodeAt(wo)?(e="`",wo++):(e=u,0===Ao&&_o(Gr));if(e!==u){if(n=[],Vr.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(Qr)),o!==u)for(;o!==u;)n.push(o),Vr.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(Qr));else n=u;n!==u?(96===r.charCodeAt(wo)?(o="`",wo++):(o=u,0===Ao&&_o(Gr)),o!==u?(t,e=qr(n),t=e):(wo=t,t=u)):(wo=t,t=u)}else wo=t,t=u;return t}()),t}function ra(){var r,t;return r=wo,(t=ea())!==u&&(r,t=t),(r=t)===u&&(r=Ju()),r}function ta(){var r,t;return r=wo,(t=ea())!==u?(wo,(Fr(t)?u:void 0)!==u?(r,r=t=t):(wo=r,r=u)):(wo=r,r=u),r===u&&(r=Ju()),r}function ea(){var r,t,e,n;if(r=wo,(t=oa())!==u){for(e=[],n=aa();n!==u;)e.push(n),n=aa();e!==u?(r,r=t=Xr(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function na(){var r,t,e,n;if(r=wo,(t=oa())!==u){for(e=[],n=ua();n!==u;)e.push(n),n=ua();e!==u?(r,r=t=Xr(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function oa(){var t;return Kr.test(r.charAt(wo))?(t=r.charAt(wo),wo++):(t=u,0===Ao&&_o(zr)),t}function ua(){var t;return Zr.test(r.charAt(wo))?(t=r.charAt(wo),wo++):(t=u,0===Ao&&_o(Jr)),t}function aa(){var t;return rt.test(r.charAt(wo))?(t=r.charAt(wo),wo++):(t=u,0===Ao&&_o(tt)),t}function sa(){var t,e,n,o;return t=wo,e=wo,58===r.charCodeAt(wo)?(n=":",wo++):(n=u,0===Ao&&_o(et)),n!==u&&(o=na())!==u?e=n=[n,o]:(wo=e,e=u),e!==u&&(t,e={type:"param",value:e[1]}),t=e}function ia(){var r,t,e;return r=wo,Ha()!==u&&$s()!==u&&Sa()!==u&&$s()!==u&&(t=As())!==u&&$s()!==u&&Us()!==u&&$s()!==u?((e=Nu())===u&&(e=null),e!==u&&$s()!==u&&Ms()!==u?(r,r={type:"on update",keyword:t,parentheses:!0,expr:e}):(wo=r,r=u)):(wo=r,r=u),r===u&&(r=wo,Ha()!==u&&$s()!==u&&Sa()!==u&&$s()!==u&&(t=As())!==u?(r,r=function(r){return{type:"on update",keyword:r}}(t)):(wo=r,r=u)),r}function ca(){var t,e,n;return t=wo,function(){var t,e,n,o;t=wo,"over"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(ve));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}()!==u&&$s()!==u&&Us()!==u&&$s()!==u&&Oa()!==u&&$s()!==u&&Ga()!==u&&$s()!==u&&(e=ou())!==u&&$s()!==u?((n=Cu())===u&&(n=null),n!==u&&$s()!==u&&Ms()!==u?(t,t={partitionby:e,orderby:n}):(wo=t,t=u)):(wo=t,t=u),t===u&&(t=ia()),t}function la(){var t;return(t=function(){var t,e,n,o;t=wo,"current_date"===r.substr(wo,12).toLowerCase()?(e=r.substr(wo,12),wo+=12):(e=u,0===Ao&&_o(wn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="CURRENT_DATE"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=wo,"current_time"===r.substr(wo,12).toLowerCase()?(e=r.substr(wo,12),wo+=12):(e=u,0===Ao&&_o(Sn));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="CURRENT_TIME"):(wo=t,t=u)):(wo=t,t=u);return t}())===u&&(t=As()),t}function fa(){var t;return(t=va())===u&&(t=ha())===u&&(t=function(){var t,e;t=wo,(e=function(){var t,e,n,o;t=wo,"true"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Ut));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(t,e={type:"bool",value:!0});(t=e)===u&&(t=wo,(e=function(){var t,e,n,o;t=wo,"false"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(Dt));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(t,e={type:"bool",value:!1}),t=e);return t}())===u&&(t=pa())===u&&(t=function(){var t,e,n,o,a,s;t=wo,(e=Cs())===u&&(e=ws())===u&&(e=Es())===u&&(e=Ls());if(e!==u)if($s()!==u){if(n=wo,39===r.charCodeAt(wo)?(o="'",wo++):(o=u,0===Ao&&_o(er)),o!==u){for(a=[],s=da();s!==u;)a.push(s),s=da();a!==u?(39===r.charCodeAt(wo)?(s="'",wo++):(s=u,0===Ao&&_o(er)),s!==u?n=o=[o,a,s]:(wo=n,n=u)):(wo=n,n=u)}else wo=n,n=u;n!==u?(t,e=nt(e,n),t=e):(wo=t,t=u)}else wo=t,t=u;else wo=t,t=u;if(t===u)if(t=wo,(e=Cs())===u&&(e=ws())===u&&(e=Es())===u&&(e=Ls()),e!==u)if($s()!==u){if(n=wo,34===r.charCodeAt(wo)?(o='"',wo++):(o=u,0===Ao&&_o(Hr)),o!==u){for(a=[],s=ba();s!==u;)a.push(s),s=ba();a!==u?(34===r.charCodeAt(wo)?(s='"',wo++):(s=u,0===Ao&&_o(Hr)),s!==u?n=o=[o,a,s]:(wo=n,n=u)):(wo=n,n=u)}else wo=n,n=u;n!==u?(t,e=nt(e,n),t=e):(wo=t,t=u)}else wo=t,t=u;else wo=t,t=u;return t}()),t}function pa(){var t,e;return t=wo,(e=function(){var t,e,n,o;t=wo,"null"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Rt));e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u);return t}())!==u&&(t,e={type:"null",value:null}),t=e}function va(){var t,e,n,o,a;if(t=wo,e=wo,39===r.charCodeAt(wo)?(n="'",wo++):(n=u,0===Ao&&_o(er)),n!==u){for(o=[],a=da();a!==u;)o.push(a),a=da();o!==u?(39===r.charCodeAt(wo)?(a="'",wo++):(a=u,0===Ao&&_o(er)),a!==u?e=n=[n,o,a]:(wo=e,e=u)):(wo=e,e=u)}else wo=e,e=u;if(e!==u&&(t,e={type:"single_quote_string",value:e[1].join("")}),(t=e)===u){if(t=wo,e=wo,34===r.charCodeAt(wo)?(n='"',wo++):(n=u,0===Ao&&_o(Hr)),n!==u){for(o=[],a=ba();a!==u;)o.push(a),a=ba();o!==u?(34===r.charCodeAt(wo)?(a='"',wo++):(a=u,0===Ao&&_o(Hr)),a!==u?e=n=[n,o,a]:(wo=e,e=u)):(wo=e,e=u)}else wo=e,e=u;e!==u&&(t,e=function(r){return{type:"double_quote_string",value:r[1].join("")}}(e)),t=e}return t}function ba(){var t;return ot.test(r.charAt(wo))?(t=r.charAt(wo),wo++):(t=u,0===Ao&&_o(ut)),t===u&&(t=ya()),t}function da(){var t;return at.test(r.charAt(wo))?(t=r.charAt(wo),wo++):(t=u,0===Ao&&_o(st)),t===u&&(t=ya()),t}function ya(){var t,e,n,o,a,s,i,c,l,f;return t=wo,"\\'"===r.substr(wo,2)?(e="\\'",wo+=2):(e=u,0===Ao&&_o(it)),e!==u&&(t,e="\\'"),(t=e)===u&&(t=wo,'\\"'===r.substr(wo,2)?(e='\\"',wo+=2):(e=u,0===Ao&&_o(ct)),e!==u&&(t,e='\\"'),(t=e)===u&&(t=wo,"\\\\"===r.substr(wo,2)?(e="\\\\",wo+=2):(e=u,0===Ao&&_o(lt)),e!==u&&(t,e="\\\\"),(t=e)===u&&(t=wo,"\\/"===r.substr(wo,2)?(e="\\/",wo+=2):(e=u,0===Ao&&_o(ft)),e!==u&&(t,e="\\/"),(t=e)===u&&(t=wo,"\\b"===r.substr(wo,2)?(e="\\b",wo+=2):(e=u,0===Ao&&_o(pt)),e!==u&&(t,e="\b"),(t=e)===u&&(t=wo,"\\f"===r.substr(wo,2)?(e="\\f",wo+=2):(e=u,0===Ao&&_o(vt)),e!==u&&(t,e="\f"),(t=e)===u&&(t=wo,"\\n"===r.substr(wo,2)?(e="\\n",wo+=2):(e=u,0===Ao&&_o(bt)),e!==u&&(t,e="\n"),(t=e)===u&&(t=wo,"\\r"===r.substr(wo,2)?(e="\\r",wo+=2):(e=u,0===Ao&&_o(dt)),e!==u&&(t,e="\r"),(t=e)===u&&(t=wo,"\\t"===r.substr(wo,2)?(e="\\t",wo+=2):(e=u,0===Ao&&_o(yt)),e!==u&&(t,e="\t"),(t=e)===u&&(t=wo,"\\u"===r.substr(wo,2)?(e="\\u",wo+=2):(e=u,0===Ao&&_o(ht)),e!==u&&(n=Aa())!==u&&(o=Aa())!==u&&(a=Aa())!==u&&(s=Aa())!==u?(t,i=n,c=o,l=a,f=s,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):(wo=t,t=u),t===u&&(t=wo,92===r.charCodeAt(wo)?(e="\\",wo++):(e=u,0===Ao&&_o(mt)),e!==u&&(t,e="\\"),(t=e)===u&&(t=wo,"''"===r.substr(wo,2)?(e="''",wo+=2):(e=u,0===Ao&&_o(wt)),e!==u&&(t,e="''"),(t=e)===u&&(t=wo,'""'===r.substr(wo,2)?(e='""',wo+=2):(e=u,0===Ao&&_o(Lt)),e!==u&&(t,e='""'),(t=e)===u&&(t=wo,"``"===r.substr(wo,2)?(e="``",wo+=2):(e=u,0===Ao&&_o(Ct)),e!==u&&(t,e="``"),t=e))))))))))))),t}function ha(){var r,t,e;return r=wo,(t=function(){var r,t,e,n;r=wo,(t=ma())!==u&&(e=wa())!==u&&(n=La())!==u?(r,r=t={type:"bigint",value:t+e+n}):(wo=r,r=u);r===u&&(r=wo,(t=ma())!==u&&(e=wa())!==u?(r,t=function(r,t){const e=r+t;return li(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e),r=t):(wo=r,r=u),r===u&&(r=wo,(t=ma())!==u&&(e=La())!==u?(r,t=function(r,t){return{type:"bigint",value:r+t}}(t,e),r=t):(wo=r,r=u),r===u&&(r=wo,(t=ma())!==u&&(r,t=function(r){return li(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t)));return r}())!==u&&(r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function ma(){var t,e,n;return(t=Ca())===u&&(t=Ea())===u&&(t=wo,45===r.charCodeAt(wo)?(e="-",wo++):(e=u,0===Ao&&_o(Ur)),e===u&&(43===r.charCodeAt(wo)?(e="+",wo++):(e=u,0===Ao&&_o(Or))),e!==u&&(n=Ca())!==u?(t,t=e=e+n):(wo=t,t=u),t===u&&(t=wo,45===r.charCodeAt(wo)?(e="-",wo++):(e=u,0===Ao&&_o(Ur)),e===u&&(43===r.charCodeAt(wo)?(e="+",wo++):(e=u,0===Ao&&_o(Or))),e!==u&&(n=Ea())!==u?(t,t=e=function(r,t){return r+t}(e,n)):(wo=t,t=u))),t}function wa(){var t,e,n;return t=wo,46===r.charCodeAt(wo)?(e=".",wo++):(e=u,0===Ao&&_o(gt)),e!==u&&(n=Ca())!==u?(t,t=e="."+n):(wo=t,t=u),t}function La(){var t,e,n;return t=wo,(e=function(){var t,e,n;t=wo,xt.test(r.charAt(wo))?(e=r.charAt(wo),wo++):(e=u,0===Ao&&_o(kt));e!==u?(It.test(r.charAt(wo))?(n=r.charAt(wo),wo++):(n=u,0===Ao&&_o(Nt)),n===u&&(n=null),n!==u?(t,t=e=e+(null!==(o=n)?o:"")):(wo=t,t=u)):(wo=t,t=u);var o;return t}())!==u&&(n=Ca())!==u?(t,t=e=e+n):(wo=t,t=u),t}function Ca(){var r,t,e;if(r=wo,t=[],(e=Ea())!==u)for(;e!==u;)t.push(e),e=Ea();else t=u;return t!==u&&(r,t=t.join("")),r=t}function Ea(){var t;return Tt.test(r.charAt(wo))?(t=r.charAt(wo),wo++):(t=u,0===Ao&&_o(jt)),t}function Aa(){var t;return St.test(r.charAt(wo))?(t=r.charAt(wo),wo++):(t=u,0===Ao&&_o(_t)),t}function ga(){var t,e,n,o;return t=wo,"default"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(m)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Ta(){var t,e,n,o;return t=wo,"to"===r.substr(wo,2).toLowerCase()?(e=r.substr(wo,2),wo+=2):(e=u,0===Ao&&_o(Mt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function ja(){var t,e,n,o;return t=wo,"drop"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Pt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="DROP"):(wo=t,t=u)):(wo=t,t=u),t}function Sa(){var t,e,n,o;return t=wo,"update"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(Bt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function _a(){var t,e,n,o;return t=wo,"create"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(qt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function xa(){var t,e,n,o;return t=wo,"temporary"===r.substr(wo,9).toLowerCase()?(e=r.substr(wo,9),wo+=9):(e=u,0===Ao&&_o(Wt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function ka(){var t,e,n,o;return t=wo,"delete"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(Yt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Ia(){var t,e,n,o;return t=wo,"replace"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(Qt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Na(){var t,e,n,o;return t=wo,"rename"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(Xt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Ra(){var t,e,n,o;return t=wo,"ignore"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(Kt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Oa(){var t,e,n,o;return t=wo,"partition"===r.substr(wo,9).toLowerCase()?(e=r.substr(wo,9),wo+=9):(e=u,0===Ao&&_o(zt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="PARTITION"):(wo=t,t=u)):(wo=t,t=u),t}function Ua(){var t,e,n,o;return t=wo,"into"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Zt)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Ma(){var t,e,n,o;return t=wo,"set"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(G)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="SET"):(wo=t,t=u)):(wo=t,t=u),t}function Da(){var t,e,n,o;return t=wo,"as"===r.substr(wo,2).toLowerCase()?(e=r.substr(wo,2),wo+=2):(e=u,0===Ao&&_o(te)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Pa(){var t,e,n,o;return t=wo,"table"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(ee)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="TABLE"):(wo=t,t=u)):(wo=t,t=u),t}function Fa(){var t,e,n,o;return t=wo,"tables"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(ne)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="TABLES"):(wo=t,t=u)):(wo=t,t=u),t}function Ha(){var t,e,n,o;return t=wo,"on"===r.substr(wo,2).toLowerCase()?(e=r.substr(wo,2),wo+=2):(e=u,0===Ao&&_o(ae)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function $a(){var t,e,n,o;return t=wo,"join"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(fe)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Ba(){var t,e,n,o;return t=wo,"outer"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(pe)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function qa(){var t,e,n,o;return t=wo,"values"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(de)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Wa(){var t,e,n,o;return t=wo,"using"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(ye)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Ya(){var t,e,n,o;return t=wo,"with"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(br)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Ga(){var t,e,n,o;return t=wo,"by"===r.substr(wo,2).toLowerCase()?(e=r.substr(wo,2),wo+=2):(e=u,0===Ao&&_o(we)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function Va(){var t,e,n,o;return t=wo,"fetch"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(Ee)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="FETCH"):(wo=t,t=u)):(wo=t,t=u),t}function Qa(){var t,e,n,o;return t=wo,"all"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(je)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="ALL"):(wo=t,t=u)):(wo=t,t=u),t}function Xa(){var t,e,n,o;return t=wo,"distinct"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(Se)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="DISTINCT"):(wo=t,t=u)):(wo=t,t=u),t}function Ka(){var t,e,n,o;return t=wo,"between"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(_e)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="BETWEEN"):(wo=t,t=u)):(wo=t,t=u),t}function za(){var t,e,n,o;return t=wo,"in"===r.substr(wo,2).toLowerCase()?(e=r.substr(wo,2),wo+=2):(e=u,0===Ao&&_o(xe)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="IN"):(wo=t,t=u)):(wo=t,t=u),t}function Za(){var t,e,n,o;return t=wo,"is"===r.substr(wo,2).toLowerCase()?(e=r.substr(wo,2),wo+=2):(e=u,0===Ao&&_o(ke)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="IS"):(wo=t,t=u)):(wo=t,t=u),t}function Ja(){var t,e,n,o;return t=wo,"like"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Ie)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="LIKE"):(wo=t,t=u)):(wo=t,t=u),t}function rs(){var t,e,n,o;return t=wo,"exists"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(Ne)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="EXISTS"):(wo=t,t=u)):(wo=t,t=u),t}function ts(){var t,e,n,o;return t=wo,"not"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(R)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="NOT"):(wo=t,t=u)):(wo=t,t=u),t}function es(){var t,e,n,o;return t=wo,"and"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(Re)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="AND"):(wo=t,t=u)):(wo=t,t=u),t}function ns(){var t,e,n,o;return t=wo,"or"===r.substr(wo,2).toLowerCase()?(e=r.substr(wo,2),wo+=2):(e=u,0===Ao&&_o(Oe)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="OR"):(wo=t,t=u)):(wo=t,t=u),t}function os(){var t,e,n,o;return t=wo,"case"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o($e)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function us(){var t,e,n,o;return t=wo,"end"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(Ye)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?t=e=[e,n]:(wo=t,t=u)):(wo=t,t=u),t}function as(){var t,e,n,o;return t=wo,"cast"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Ge)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="CAST"):(wo=t,t=u)):(wo=t,t=u),t}function ss(){var t,e,n,o;return t=wo,"char"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(Ve)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="CHAR"):(wo=t,t=u)):(wo=t,t=u),t}function is(){var t,e,n,o;return t=wo,"varchar"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(Qe)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="VARCHAR"):(wo=t,t=u)):(wo=t,t=u),t}function cs(){var t,e,n,o;return t=wo,"numeric"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(Xe)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="NUMERIC"):(wo=t,t=u)):(wo=t,t=u),t}function ls(){var t,e,n,o;return t=wo,"decimal"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(Ke)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="DECIMAL"):(wo=t,t=u)):(wo=t,t=u),t}function fs(){var t,e,n,o;return t=wo,"unsigned"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(Ze)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="UNSIGNED"):(wo=t,t=u)):(wo=t,t=u),t}function ps(){var t,e,n,o;return t=wo,"int"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(Je)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="INT"):(wo=t,t=u)):(wo=t,t=u),t}function vs(){var t,e,n,o;return t=wo,"integer"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(tn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="INTEGER"):(wo=t,t=u)):(wo=t,t=u),t}function bs(){var t,e,n,o;return t=wo,"smallint"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(nn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="SMALLINT"):(wo=t,t=u)):(wo=t,t=u),t}function ds(){var t,e,n,o;return t=wo,"tinyint"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(on)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="TINYINT"):(wo=t,t=u)):(wo=t,t=u),t}function ys(){var t,e,n,o;return t=wo,"bigint"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(ln)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="BIGINT"):(wo=t,t=u)):(wo=t,t=u),t}function hs(){var t,e,n,o;return t=wo,"float"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(fn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="FLOAT"):(wo=t,t=u)):(wo=t,t=u),t}function ms(){var t,e,n,o;return t=wo,"double"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(pn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="DOUBLE"):(wo=t,t=u)):(wo=t,t=u),t}function ws(){var t,e,n,o;return t=wo,"date"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(vn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="DATE"):(wo=t,t=u)):(wo=t,t=u),t}function Ls(){var t,e,n,o;return t=wo,"datetime"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(bn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="DATETIME"):(wo=t,t=u)):(wo=t,t=u),t}function Cs(){var t,e,n,o;return t=wo,"time"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(dn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="TIME"):(wo=t,t=u)):(wo=t,t=u),t}function Es(){var t,e,n,o;return t=wo,"timestamp"===r.substr(wo,9).toLowerCase()?(e=r.substr(wo,9),wo+=9):(e=u,0===Ao&&_o(yn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="TIMESTAMP"):(wo=t,t=u)):(wo=t,t=u),t}function As(){var t,e,n,o;return t=wo,"current_timestamp"===r.substr(wo,17).toLowerCase()?(e=r.substr(wo,17),wo+=17):(e=u,0===Ao&&_o(_n)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="CURRENT_TIMESTAMP"):(wo=t,t=u)):(wo=t,t=u),t}function gs(){var t;return(t=function(){var t;return"@@"===r.substr(wo,2)?(t="@@",wo+=2):(t=u,0===Ao&&_o(Dn)),t}())===u&&(t=function(){var t;return 64===r.charCodeAt(wo)?(t="@",wo++):(t=u,0===Ao&&_o(Mn)),t}())===u&&(t=function(){var t;return 36===r.charCodeAt(wo)?(t="$",wo++):(t=u,0===Ao&&_o(Pn)),t}()),t}function Ts(){var t;return 61===r.charCodeAt(wo)?(t="=",wo++):(t=u,0===Ao&&_o(Ar)),t}function js(){var t,e,n,o;return t=wo,"add"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(Bn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="ADD"):(wo=t,t=u)):(wo=t,t=u),t}function Ss(){var t,e,n,o;return t=wo,"column"===r.substr(wo,6).toLowerCase()?(e=r.substr(wo,6),wo+=6):(e=u,0===Ao&&_o(qn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="COLUMN"):(wo=t,t=u)):(wo=t,t=u),t}function _s(){var t,e,n,o;return t=wo,"index"===r.substr(wo,5).toLowerCase()?(e=r.substr(wo,5),wo+=5):(e=u,0===Ao&&_o(Wn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="INDEX"):(wo=t,t=u)):(wo=t,t=u),t}function xs(){var t,e,n,o;return t=wo,"key"===r.substr(wo,3).toLowerCase()?(e=r.substr(wo,3),wo+=3):(e=u,0===Ao&&_o(v)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="KEY"):(wo=t,t=u)):(wo=t,t=u),t}function ks(){var t,e,n,o;return t=wo,"comment"===r.substr(wo,7).toLowerCase()?(e=r.substr(wo,7),wo+=7):(e=u,0===Ao&&_o(Vn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="COMMENT"):(wo=t,t=u)):(wo=t,t=u),t}function Is(){var t,e,n,o;return t=wo,"constraint"===r.substr(wo,10).toLowerCase()?(e=r.substr(wo,10),wo+=10):(e=u,0===Ao&&_o(Qn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="CONSTRAINT"):(wo=t,t=u)):(wo=t,t=u),t}function Ns(){var t;return 46===r.charCodeAt(wo)?(t=".",wo++):(t=u,0===Ao&&_o(gt)),t}function Rs(){var t;return 44===r.charCodeAt(wo)?(t=",",wo++):(t=u,0===Ao&&_o(eo)),t}function Os(){var t;return 42===r.charCodeAt(wo)?(t="*",wo++):(t=u,0===Ao&&_o(Mr)),t}function Us(){var t;return 40===r.charCodeAt(wo)?(t="(",wo++):(t=u,0===Ao&&_o(lr)),t}function Ms(){var t;return 41===r.charCodeAt(wo)?(t=")",wo++):(t=u,0===Ao&&_o(fr)),t}function Ds(){var t;return 59===r.charCodeAt(wo)?(t=";",wo++):(t=u,0===Ao&&_o(uo)),t}function Ps(){var t;return"->"===r.substr(wo,2)?(t="->",wo+=2):(t=u,0===Ao&&_o(ao)),t}function Fs(){var t;return"->>"===r.substr(wo,3)?(t="->>",wo+=3):(t=u,0===Ao&&_o(so)),t}function Hs(){var t;return(t=function(){var t;return"||"===r.substr(wo,2)?(t="||",wo+=2):(t=u,0===Ao&&_o(io)),t}())===u&&(t=function(){var t;return"&&"===r.substr(wo,2)?(t="&&",wo+=2):(t=u,0===Ao&&_o(co)),t}()),t}function $s(){var r,t;for(r=[],(t=Gs())===u&&(t=qs());t!==u;)r.push(t),(t=Gs())===u&&(t=qs());return r}function Bs(){var r,t;if(r=[],(t=Gs())===u&&(t=qs()),t!==u)for(;t!==u;)r.push(t),(t=Gs())===u&&(t=qs());else r=u;return r}function qs(){var t;return(t=function(){var t,e,n,o,a,s;t=wo,"/*"===r.substr(wo,2)?(e="/*",wo+=2):(e=u,0===Ao&&_o(lo));if(e!==u){for(n=[],o=wo,a=wo,Ao++,"*/"===r.substr(wo,2)?(s="*/",wo+=2):(s=u,0===Ao&&_o(fo)),Ao--,s===u?a=void 0:(wo=a,a=u),a!==u&&(s=Ys())!==u?o=a=[a,s]:(wo=o,o=u);o!==u;)n.push(o),o=wo,a=wo,Ao++,"*/"===r.substr(wo,2)?(s="*/",wo+=2):(s=u,0===Ao&&_o(fo)),Ao--,s===u?a=void 0:(wo=a,a=u),a!==u&&(s=Ys())!==u?o=a=[a,s]:(wo=o,o=u);n!==u?("*/"===r.substr(wo,2)?(o="*/",wo+=2):(o=u,0===Ao&&_o(fo)),o!==u?t=e=[e,n,o]:(wo=t,t=u)):(wo=t,t=u)}else wo=t,t=u;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=wo,"--"===r.substr(wo,2)?(e="--",wo+=2):(e=u,0===Ao&&_o(po));if(e!==u){for(n=[],o=wo,a=wo,Ao++,s=Vs(),Ao--,s===u?a=void 0:(wo=a,a=u),a!==u&&(s=Ys())!==u?o=a=[a,s]:(wo=o,o=u);o!==u;)n.push(o),o=wo,a=wo,Ao++,s=Vs(),Ao--,s===u?a=void 0:(wo=a,a=u),a!==u&&(s=Ys())!==u?o=a=[a,s]:(wo=o,o=u);n!==u?t=e=[e,n]:(wo=t,t=u)}else wo=t,t=u;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=wo,35===r.charCodeAt(wo)?(e="#",wo++):(e=u,0===Ao&&_o(vo));if(e!==u){for(n=[],o=wo,a=wo,Ao++,s=Vs(),Ao--,s===u?a=void 0:(wo=a,a=u),a!==u&&(s=Ys())!==u?o=a=[a,s]:(wo=o,o=u);o!==u;)n.push(o),o=wo,a=wo,Ao++,s=Vs(),Ao--,s===u?a=void 0:(wo=a,a=u),a!==u&&(s=Ys())!==u?o=a=[a,s]:(wo=o,o=u);n!==u?t=e=[e,n]:(wo=t,t=u)}else wo=t,t=u;return t}()),t}function Ws(){var r,t,e,n,o,a,s;return r=wo,(t=ks())!==u&&$s()!==u?((e=Ts())===u&&(e=null),e!==u&&$s()!==u&&(n=va())!==u?(r,a=e,s=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:a,value:s}):(wo=r,r=u)):(wo=r,r=u),r}function Ys(){var t;return r.length>wo?(t=r.charAt(wo),wo++):(t=u,0===Ao&&_o(bo)),t}function Gs(){var t;return yo.test(r.charAt(wo))?(t=r.charAt(wo),wo++):(t=u,0===Ao&&_o(ho)),t}function Vs(){var t,e;if((t=function(){var t,e;t=wo,Ao++,r.length>wo?(e=r.charAt(wo),wo++):(e=u,0===Ao&&_o(bo));Ao--,e===u?t=void 0:(wo=t,t=u);return t}())===u)if(t=[],Et.test(r.charAt(wo))?(e=r.charAt(wo),wo++):(e=u,0===Ao&&_o(At)),e!==u)for(;e!==u;)t.push(e),Et.test(r.charAt(wo))?(e=r.charAt(wo),wo++):(e=u,0===Ao&&_o(At));else t=u;return t}function Qs(){var t,e;return t=wo,wo,di=[],(!0?void 0:u)!==u&&$s()!==u?((e=Xs())===u&&(e=function(){var t,e;t=wo,function(){var t;return"return"===r.substr(wo,6).toLowerCase()?(t=r.substr(wo,6),wo+=6):(t=u,0===Ao&&_o(Fn)),t}()!==u&&$s()!==u&&(e=Ks())!==u?(t,t={type:"return",expr:e}):(wo=t,t=u);return t}()),e!==u?(t,t={stmt:e,vars:di}):(wo=t,t=u)):(wo=t,t=u),t}function Xs(){var t,e,n,o;return t=wo,(e=ni())===u&&(e=oi()),e!==u&&$s()!==u?((n=function(){var t;return":="===r.substr(wo,2)?(t=":=",wo+=2):(t=u,0===Ao&&_o(Hn)),t}())===u&&(n=Ts()),n!==u&&$s()!==u&&(o=Ks())!==u?(t,t=e={type:"assign",left:e,symbol:n,right:o}):(wo=t,t=u)):(wo=t,t=u),t}function Ks(){var t;return(t=Zo())===u&&(t=function(){var r,t,e,n,o;r=wo,(t=ni())!==u&&$s()!==u&&(e=du())!==u&&$s()!==u&&(n=ni())!==u&&$s()!==u&&(o=mu())!==u?(r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):(wo=r,r=u);return r}())===u&&(t=zs())===u&&(t=function(){var t,e;t=wo,function(){var t;return 91===r.charCodeAt(wo)?(t="[",wo++):(t=u,0===Ao&&_o(no)),t}()!==u&&$s()!==u&&(e=ei())!==u&&$s()!==u&&function(){var t;return 93===r.charCodeAt(wo)?(t="]",wo++):(t=u,0===Ao&&_o(oo)),t}()!==u?(t,t={type:"array",value:e}):(wo=t,t=u);return t}()),t}function zs(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Zs())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Vu())!==u&&(s=$s())!==u&&(i=Zs())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Vu())!==u&&(s=$s())!==u&&(i=Zs())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=Tr(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function Zs(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Js())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Xu())!==u&&(s=$s())!==u&&(i=Js())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Xu())!==u&&(s=$s())!==u&&(i=Js())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=Tr(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function Js(){var r,t,e;return(r=fa())===u&&(r=ni())===u&&(r=ti())===u&&(r=sa())===u&&(r=wo,Us()!==u&&$s()!==u&&(t=zs())!==u&&$s()!==u&&Ms()!==u?(r,(e=t).parentheses=!0,r=e):(wo=r,r=u)),r}function ri(){var r,t,e,n,o,a,s;return r=wo,(t=na())!==u?(e=wo,(n=$s())!==u&&(o=Ns())!==u&&(a=$s())!==u&&(s=na())!==u?e=n=[n,o,a,s]:(wo=e,e=u),e===u&&(e=null),e!==u?(r,r=t=function(r,t){let e=r;return null!==t&&(e=`${r}.${t[3]}`),e}(t,e)):(wo=r,r=u)):(wo=r,r=u),r}function ti(){var r,t,e;return r=wo,(t=ri())!==u&&$s()!==u&&Us()!==u&&$s()!==u?((e=ei())===u&&(e=null),e!==u&&$s()!==u&&Ms()!==u?(r,r=t={type:"function",name:t,args:{type:"expr_list",value:e}}):(wo=r,r=u)):(wo=r,r=u),r===u&&(r=wo,(t=ri())!==u&&(r,t=function(r){return{type:"function",name:r,args:null}}(t)),r=t),r}function ei(){var r,t,e,n,o,a,s,i;if(r=wo,(t=Js())!==u){for(e=[],n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=Js())!==u?n=o=[o,a,s,i]:(wo=n,n=u);n!==u;)e.push(n),n=wo,(o=$s())!==u&&(a=Rs())!==u&&(s=$s())!==u&&(i=Js())!==u?n=o=[o,a,s,i]:(wo=n,n=u);e!==u?(r,r=t=l(t,e)):(wo=r,r=u)}else wo=r,r=u;return r}function ni(){var r,t,e,n,o;return r=wo,(t=gs())!==u&&(e=oi())!==u?(r,n=t,o=e,r=t={type:"var",...o,prefix:n}):(wo=r,r=u),r}function oi(){var t,e,n,o,a;return t=wo,(e=na())!==u&&(n=function(){var t,e,n,o,a;t=wo,e=[],n=wo,46===r.charCodeAt(wo)?(o=".",wo++):(o=u,0===Ao&&_o(gt));o!==u&&(a=na())!==u?n=o=[o,a]:(wo=n,n=u);for(;n!==u;)e.push(n),n=wo,46===r.charCodeAt(wo)?(o=".",wo++):(o=u,0===Ao&&_o(gt)),o!==u&&(a=na())!==u?n=o=[o,a]:(wo=n,n=u);e!==u&&(t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==u?(t,o=e,a=n,di.push(o),t=e={type:"var",name:o,members:a,prefix:null}):(wo=t,t=u),t===u&&(t=wo,(e=ha())!==u&&(t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function ui(){var t;return(t=function(){var t,e,n,o;t=wo,(e=ss())===u&&(e=is());if(e!==u)if($s()!==u)if(Us()!==u)if($s()!==u){if(n=[],Tt.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(jt)),o!==u)for(;o!==u;)n.push(o),Tt.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(jt));else n=u;n!==u&&(o=$s())!==u&&Ms()!==u?(t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(wo=t,t=u)}else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;t===u&&(t=wo,(e=ss())!==u&&(t,e=mo(e)),(t=e)===u&&(t=wo,(e=is())!==u&&(t,e=mo(e)),t=e));return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c,l,f,p,v;t=wo,(e=cs())===u&&(e=ls())===u&&(e=ps())===u&&(e=vs())===u&&(e=bs())===u&&(e=ds())===u&&(e=ys())===u&&(e=hs())===u&&(e=ms());if(e!==u)if((n=$s())!==u)if((o=Us())!==u)if((a=$s())!==u){if(s=[],Tt.test(r.charAt(wo))?(i=r.charAt(wo),wo++):(i=u,0===Ao&&_o(jt)),i!==u)for(;i!==u;)s.push(i),Tt.test(r.charAt(wo))?(i=r.charAt(wo),wo++):(i=u,0===Ao&&_o(jt));else s=u;if(s!==u)if((i=$s())!==u){if(c=wo,(l=Rs())!==u)if((f=$s())!==u){if(p=[],Tt.test(r.charAt(wo))?(v=r.charAt(wo),wo++):(v=u,0===Ao&&_o(jt)),v!==u)for(;v!==u;)p.push(v),Tt.test(r.charAt(wo))?(v=r.charAt(wo),wo++):(v=u,0===Ao&&_o(jt));else p=u;p!==u?c=l=[l,f,p]:(wo=c,c=u)}else wo=c,c=u;else wo=c,c=u;c===u&&(c=null),c!==u&&(l=$s())!==u&&(f=Ms())!==u&&(p=$s())!==u?((v=ai())===u&&(v=null),v!==u?(t,b=c,d=v,e={dataType:e,length:parseInt(s.join(""),10),scale:b&&parseInt(b[2].join(""),10),parentheses:!0,suffix:d},t=e):(wo=t,t=u)):(wo=t,t=u)}else wo=t,t=u;else wo=t,t=u}else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;var b,d;if(t===u){if(t=wo,(e=cs())===u&&(e=ls())===u&&(e=ps())===u&&(e=vs())===u&&(e=bs())===u&&(e=ds())===u&&(e=ys())===u&&(e=hs())===u&&(e=ms()),e!==u){if(n=[],Tt.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(jt)),o!==u)for(;o!==u;)n.push(o),Tt.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(jt));else n=u;n!==u&&(o=$s())!==u?((a=ai())===u&&(a=null),a!==u?(t,e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,a),t=e):(wo=t,t=u)):(wo=t,t=u)}else wo=t,t=u;t===u&&(t=wo,(e=cs())===u&&(e=ls())===u&&(e=ps())===u&&(e=vs())===u&&(e=bs())===u&&(e=ds())===u&&(e=ys())===u&&(e=hs())===u&&(e=ms()),e!==u&&(n=$s())!==u?((o=ai())===u&&(o=null),o!==u&&(a=$s())!==u?(t,e=function(r,t){return{dataType:r,suffix:t}}(e,o),t=e):(wo=t,t=u)):(wo=t,t=u))}return t}())===u&&(t=function(){var t,e,n,o;t=wo,(e=ws())===u&&(e=Ls())===u&&(e=Cs())===u&&(e=Es());if(e!==u)if($s()!==u)if(Us()!==u)if($s()!==u){if(n=[],Tt.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(jt)),o!==u)for(;o!==u;)n.push(o),Tt.test(r.charAt(wo))?(o=r.charAt(wo),wo++):(o=u,0===Ao&&_o(jt));else n=u;n!==u&&(o=$s())!==u&&Ms()!==u?(t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):(wo=t,t=u)}else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;else wo=t,t=u;t===u&&(t=wo,(e=ws())===u&&(e=Ls())===u&&(e=Cs())===u&&(e=Es()),e!==u&&(t,e=mo(e)),t=e);return t}())===u&&(t=function(){var t,e;t=wo,(e=function(){var t,e,n,o;return t=wo,"json"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(en)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="JSON"):(wo=t,t=u)):(wo=t,t=u),t}())!==u&&(t,e=mo(e));return t=e}())===u&&(t=function(){var t,e;t=wo,(e=function(){var t,e,n,o;return t=wo,"tinytext"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(un)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="TINYTEXT"):(wo=t,t=u)):(wo=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=wo,"text"===r.substr(wo,4).toLowerCase()?(e=r.substr(wo,4),wo+=4):(e=u,0===Ao&&_o(an)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="TEXT"):(wo=t,t=u)):(wo=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=wo,"mediumtext"===r.substr(wo,10).toLowerCase()?(e=r.substr(wo,10),wo+=10):(e=u,0===Ao&&_o(sn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="MEDIUMTEXT"):(wo=t,t=u)):(wo=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=wo,"longtext"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(cn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="LONGTEXT"):(wo=t,t=u)):(wo=t,t=u),t}());e!==u&&(t,e={dataType:e});return t=e}()),t}function ai(){var t,e,n;return t=wo,(e=fs())===u&&(e=null),e!==u&&$s()!==u?((n=function(){var t,e,n,o;return t=wo,"zerofill"===r.substr(wo,8).toLowerCase()?(e=r.substr(wo,8),wo+=8):(e=u,0===Ao&&_o(rn)),e!==u?(n=wo,Ao++,o=oa(),Ao--,o===u?n=void 0:(wo=n,n=u),n!==u?(t,t=e="ZEROFILL"):(wo=t,t=u)):(wo=t,t=u),t}())===u&&(n=null),n!==u?(t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):(wo=t,t=u)):(wo=t,t=u),t}const si={ALTER:!0,ALL:!0,ADD:!0,AND:!0,AS:!0,ASC:!0,BETWEEN:!0,BY:!0,CALL:!0,CASE:!0,CREATE:!0,CONTAINS:!0,CURRENT_DATE:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_USER:!0,DELETE:!0,DESC:!0,DISTINCT:!0,DROP:!0,ELSE:!0,END:!0,EXISTS:!0,EXPLAIN:!0,FALSE:!0,FROM:!0,FULL:!0,GROUP:!0,HAVING:!0,IN:!0,INNER:!0,INSERT:!0,INTO:!0,IS:!0,JOIN:!0,JSON:!0,KEY:!0,LEFT:!0,LIKE:!0,LIMIT:!0,LOW_PRIORITY:!0,NOT:!0,NULL:!0,ON:!0,OR:!0,ORDER:!0,OUTER:!0,RECURSIVE:!0,RENAME:!0,READ:!0,RIGHT:!0,SELECT:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SYSTEM_USER:!0,TABLE:!0,THEN:!0,TRUE:!0,TRUNCATE:!0,TYPE:!0,UNION:!0,UPDATE:!0,USING:!0,VALUES:!0,WITH:!0,WHEN:!0,WHERE:!0,WRITE:!0,GLOBAL:!0,SESSION:!0,LOCAL:!0,PERSIST:!0,PERSIST_ONLY:!0};function ii(r,t){return{type:"unary_expr",operator:r,expr:t}}function ci(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e}}function li(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function fi(r,t,e=3){const n=[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function pi(r,t){let e=r;for(let r=0;r<t.length;r++)e=ci(t[r][1],e,t[r][3]);return e}function vi(r){const t=mi[r];return t||(r||null)}function bi(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=vi(r[1])),t.add(r.join("::"))}return Array.from(t)}let di=[];const yi=new Set,hi=new Set,mi={};if((e=s())!==u&&wo===r.length)return e;throw e!==u&&wo<r.length&&_o({type:"end"}),xo(Eo,Co<r.length?r.charAt(Co):null,Co<r.length?So(Co,Co+1):So(Co,Co))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Lt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return function(r){if(Array.isArray(r))return u(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return u(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return u(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function a(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),vr(e)]}function s(r){if(r){var t=r.type,e=r.expr,n=r.symbol,u=t.toUpperCase(),s=[];switch(s.push(u),u){case"KEY_BLOCK_SIZE":n&&s.push(n),s.push(dr(e));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,o(a(r)));break;case"WITH PARSER":s.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(Lr(r));break;case"DATA_COMPRESSION":s.push(n,vr(e.value),hr(e.on));break;default:s.push(n,dr(e))}return s.filter(br).join(" ")}}function i(r){return r?r.map(s):[]}function c(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,u=void 0===n?[]:n,s=r.definition,c=r.on,l=r.with,f=[];if(f.push.apply(f,o(a(e))),s&&s.length){var p="CHECK"===vr(t)?"(".concat(ut(s[0]),")"):"(".concat(s.map((function(r){return ut(r)})).join(", "),")");f.push(p)}return f.push(i(u).join(" ")),l&&f.push("WITH (".concat(i(l).join(", "),")")),c&&f.push("ON [".concat(c,"]")),f}function l(r){return function(r){if(Array.isArray(r))return f(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return f(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return f(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function p(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,u=r.keyword,a=r.reference_definition,s=[],i=sr().database;s.push(vr(u)),s.push(fr(t));var f=vr(e);return"sqlite"===i&&"UNIQUE KEY"===f&&(f="UNIQUE"),s.push(f),s.push("sqlite"!==i&&fr(o)),s.push.apply(s,l(c(r))),s.push.apply(s,l(V(a))),s.push(vr(n)),s.filter(br).join(" ")}}function v(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,st(e,"partition by"),st(n,"order by"),vr(o)].filter(br).join(" ")}(t),")")}function b(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(v(e))}function d(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,u=t?ut(t).join(", "):"",a=function(r){switch(vr(r)){case"NTH_VALUE":case"LEAD":case"LAG":return!1;default:return!0}}(e);return[e,"(",u,!a&&")",o&&" ",o,a&&")"].filter(br).join("")}function y(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,u=r.parentheses,a=vr(o);if("WINDOW"===a)return"OVER ".concat(v(t));if("ON UPDATE"===a){var s="".concat(vr(o)," ").concat(vr(n)),i=ut(e)||[];return u&&(s="".concat(s,"(").concat(i.join(", "),")")),s}throw new Error("unknown over type")}}function h(r){var t=r.arrows,e=void 0===t?[]:t,n=r.collate,o=r.target,u=r.expr,a=r.keyword,s=r.symbol,i=r.as,c=r.properties,l=void 0===c?[]:c,f=o.length,p=o.dataType,v=o.parentheses,b=o.quoted,d=o.scale,y=o.suffix,h="";null!=f&&(h=d?"".concat(f,", ").concat(d):f),v&&(h="(".concat(h,")")),y&&y.length&&(h+=" ".concat(y.join(" ")));var m=ut(u),w="::",L="";return"as"===s&&(m="".concat(vr(a),"(").concat(m),L=")",w=" ".concat(s.toUpperCase()," ")),L+=e.map((function(r,t){return er(r,dr,l[t])})).join(" "),i&&(L+=" AS ".concat(fr(i))),n&&(L+=" ".concat(pr(n).join(" "))),[m,w,b,p,b,function(r){if(!r||!r.array)return"";switch(r.array){case"one":return"[]";case"two":return"[][]"}}(o),h,L].filter(br).join("")}function m(r){var t=r.args,e=r.name,n=r.args_parentheses,o=r.parentheses,u=r.over,a=r.collate,s=r.suffix,i=pr(a).join(" "),c=y(u),l=ut(s);if(!t)return[e,c].filter(br).join(" ");var f=r.separator||", ";"TRIM"===vr(e)&&(f=" ");var p=[e];return p.push(!1===n?" ":"("),p.push(ut(t).join(f)),!1!==n&&p.push(")"),p=[p.join(""),l].filter(br).join(" "),[o?"(".concat(p,")"):p,i,c].filter(br).join(" ")}function w(r){var t=r.operator||r.op,e=ut(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},u=[ut(r.left),t,e,vr(o.type),ut(o.value)].filter(br).join(" ");return r.parentheses?"(".concat(u,")"):u}function L(r){return function(r){if(Array.isArray(r))return C(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return C(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return C(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function C(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function E(r){return r?[vr(r.prefix),ut(r.value),vr(r.suffix)]:[]}function A(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(L(E(n)),L(E(e))).filter(br).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?nr("OFFSET",ut(e[0])):nr("LIMIT",e.map(ut).join("".concat("offset"===t?" ":"").concat(vr(t)," ")))}(r):"";var t,e,n}function g(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(Y).join(", "),")"):"";return"".concat("default"===t.type?fr(t.value):dr(t)).concat(o," AS (").concat(ut(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function T(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=vr(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?fr(e):ut(e))}return n.filter(br).join(" ")}}function j(r){var t=r.as_struct_val,e=r.columns,n=r.distinct,o=r.for,u=r.from,a=r.for_sys_time_as_of,s=void 0===a?{}:a,i=r.locking_read,c=r.groupby,l=r.having,f=r.into,p=void 0===f?{}:f,v=r.limit,b=r.options,d=r.orderby,y=r.parentheses_symbol,h=r.qualify,m=r.top,w=r.window,L=r.with,C=r.where,E=[g(L),"SELECT",vr(t)];E.push(cr(m)),Array.isArray(b)&&E.push(b.join(" ")),E.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[vr(t)];return e&&n.push("(".concat(e.map(Y).join(", "),")")),n.filter(br).join(" ")}}(n),Z(e,u));var j=p.position,S="";j&&(S=er("INTO",T,p)),"column"===j&&E.push(S),E.push(er("FROM",$,u)),"from"===j&&E.push(S);var _=s||{},x=_.keyword,k=_.expr;E.push(er(x,ut,k)),E.push(er("WHERE",ut,C)),E.push(nr("GROUP BY",at(c).join(", "))),E.push(er("HAVING",ut,l)),E.push(er("QUALIFY",ut,h)),E.push(er("WINDOW",ut,w)),E.push(st(d,"order by")),E.push(A(v)),E.push(vr(i)),"end"===j&&E.push(S),E.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[vr(r.type),vr(e)];return t?"".concat(n.join(" "),"(").concat(ut(t),")"):n.join(" ")}}(o));var I=E.filter(br).join(" ");return y?"(".concat(I,")"):I}function S(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(!r)return;if("string"==typeof r)return _(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return _(r,t)}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function _(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function x(r){if(!r||0===r.length)return"";var t,e=[],n=S(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u=o.table,a=o.column,s=o.value,i=[[u,a].filter(br).map((function(r){return fr(r)})).join(".")],c="";s&&(c=ut(s),i.push("=",c)),e.push(i.filter(br).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function k(r){if("select"===r.type)return j(r);var t=r.map(ut);return"(".concat(t.join("), ("),")")}function I(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(fr).join(", "));else{var e=r.value;t.push(e.map(ut).join(", "))}return t.push(")"),t.filter(br).join("")}function N(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(Y).join(", "),")")}}function R(r){var t=r.expr,e=r.keyword,n=t.type,o=[vr(e)];switch(n){case"origin":o.push(dr(t));break;case"update":o.push("UPDATE",er("SET",x,t.set),er("WHERE",ut,t.where))}return o.filter(br).join(" ")}function O(r){if(!r)return"";var t=r.action;return[N(r.target),R(t)].filter(br).join(" ")}function U(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,u=r.columns,a=r.conflict,s=r.values,i=r.where,c=r.on_duplicate_update,l=r.partition,f=r.returning,p=r.set,v=c||{},b=v.keyword,d=v.set,y=[vr(e),vr(o),$(t),I(l)];return Array.isArray(u)&&y.push("(".concat(u.map(fr).join(", "),")")),y.push(er(Array.isArray(s)?"VALUES":"",k,s)),y.push(er("ON CONFLICT",O,a)),y.push(er("SET",x,p)),y.push(er("WHERE",ut,i)),y.push(Er(f)),y.push(er(b,x,d)),y.filter(br).join(" ")}function M(r){var t=r.expr,e=r.unit;return["INTERVAL",ut(t),vr(e)].filter(br).join(" ")}function D(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(vr(t),"(").concat(n&&ut(n)||"",")"),er("AS",fr,e),er(vr(o&&o.keyword),fr,o&&o.as)].filter(br).join(" ")}function P(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,u=r.type,a=[ut(n),"FOR",Y(e),w(o)],s=["".concat(vr(u),"(").concat(a.join(" "),")")];return t&&s.push("AS",fr(t)),s.join(" ")}(r);default:return""}}function F(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,u=r.parentheses,a=r.prefix,s=[];switch(t.toLowerCase()){case"forceseek":s.push(vr(t),"(".concat(fr(n)),"(".concat(o.map(ut).filter(br).join(", "),"))"));break;case"spatial_window_max_cells":s.push(vr(t),"=",ut(e));break;case"index":s.push(vr(a),vr(t),u?"(".concat(e.map(fr).join(", "),")"):"= ".concat(fr(e)));break;default:s.push(ut(e))}return s.filter(br).join(" ")}}function H(r){if("UNNEST"===vr(r.type))return D(r);var t=r.table,e=r.db,n=r.as,o=r.expr,u=r.operator,a=r.prefix,s=r.schema,i=r.server,c=r.tablesample,l=r.table_hint,f=fr(i),p=fr(e),v=fr(s),b=t&&fr(t);if(o)switch(o.type){case"values":var d=o.parentheses,y=o.values,h=o.prefix,m=[d&&"(","",d&&")"],w=k(y);h&&(w=w.split("(").slice(1).map((function(r){return"".concat(vr(h),"(").concat(r)})).join("")),m[1]="VALUES ".concat(w),b=m.filter(br).join("");break;case"tumble":b=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.size;return["TABLE(TUMBLE(TABLE",[fr(t.db),fr(t.table)].filter(br).join("."),"DESCRIPTOR(".concat(Y(e),")"),"".concat(M(n),"))")].filter(br).join(" ")}(o);break;default:b=ut(o)}var L=[f,p,v,b=[vr(a),b].filter(br).join(" ")].filter(br).join(".");r.parentheses&&(L="(".concat(L,")"));var C=[L];if(c){var E=["TABLESAMPLE",ut(c.expr),dr(c.repeatable)].filter(br).join(" ");C.push(E)}return C.push(er("AS",fr,n),P(u)),l&&C.push(vr(l.keyword),"(".concat(l.expr.map(F).filter(br).join(", "),")")),C.filter(br).join(" ")}function $(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=$(t);return e?"(".concat(n,")"):n}var o=r[0],u=[];if("dual"===o.type)return"DUAL";u.push(H(o));for(var a=1;a<r.length;++a){var s=r[a],i=s.on,c=s.using,l=s.join,f=[];f.push(l?" ".concat(vr(l)):","),f.push(H(s)),f.push(er("ON",ut,i)),c&&f.push("USING (".concat(c.map(fr).join(", "),")")),u.push(f.filter(br).join(" "))}return u.filter(br).join("")}function B(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var u=n;switch(t){case"partition by":case"default collate":u=ut(n);break;case"options":u="(".concat(n.map((function(r){return[r.keyword,r.symbol,ut(r.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(ut).join(", ")}return o.push(u),o.join(" ")}function q(r){return function(r){if(Array.isArray(r))return W(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return W(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return W(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function W(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function Y(r){var t=r.array_index,e=r.arrows,n=void 0===e?[]:e,o=r.as,u=r.collate,a=r.column,s=r.db,i=r.isDual,c=r.schema,l=r.table,f=r.parentheses,p=r.properties,v=r.suffix,b=r.order_by,d=r.subFields,y=void 0===d?[]:d,h="*"===a?"*":function(r,t){if("string"==typeof r)return fr(r,t);var e=r.expr,n=r.offset,o=r.suffix,u=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),dr(r.value),"".concat(r.name?")":""),"]"].filter(br).join("")})).join("");return[ut(e),u,o].filter(br).join("")}(a,i),m=[c,s,l].filter(br).map((function(r){return"".concat(fr(r))})).join(".");m&&(h="".concat(m,".").concat(h)),t&&(h="".concat(h,"[").concat(dr(t.index),"]"),t.property&&(h="".concat(h,".").concat(dr(t.property))));var w=[h=[h].concat(q(y)).join("."),er("AS",ut,o),n.map((function(r,t){return er(r,dr,p[t])})).join(" ")];u&&w.push(pr(u).join(" ")),w.push(vr(v)),w.push(vr(b));var L=w.filter(br).join(" ");return f?"(".concat(L,")"):L}function G(r){var t=r||{},e=t.dataType,n=t.length,o=t.suffix,u=t.scale,a=t.expr,s=e;return null!=n&&(s+="(".concat([n,u].filter((function(r){return null!=r})).join(", "),")")),o&&o.length&&(s+=" ".concat(o.join(" "))),a&&(s+=ut(a)),s}function V(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,u=r.table,a=r.on_action;return t.push(vr(n)),t.push($(u)),t.push(e&&"(".concat(e.map((function(r){return ut(r)})).join(", "),")")),t.push(vr(o)),a.map((function(r){return t.push(vr(r.type),ut(r.value))})),t.filter(br)}function Q(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,u=r.order_by;return[ut("string"==typeof t?{type:"column_ref",table:r.table,column:t}:r),er(e&&e.type,fr,e&&e.value),o,vr(u),vr(n)].filter(br).join(" ")}function X(r){var t=[],e=Y(r.column),n=G(r.definition);t.push(e),t.push(n);var o=function(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,u=r.comment,a=r.collate,s=r.storage,i=r.default_val,c=r.auto_increment,l=r.unique,f=r.primary_key,v=r.column_format,b=r.reference_definition;if(t.push(vr(e&&e.value)),i){var d=i.type,y=i.value;t.push(d.toUpperCase(),ut(y))}var h=sr().database;return t.push(p(o)),t.push(gr(c),vr(f),vr(l),Lr(u)),t.push.apply(t,q(pr(n))),"sqlite"!==h&&t.push.apply(t,q(pr(a))),t.push.apply(t,q(pr(v))),t.push.apply(t,q(pr(s))),t.push.apply(t,q(V(b))),t.filter(br).join(" ")}(r);t.push(o);var u=function(r){if(r)return[vr(r.value),"(".concat(ut(r.expr),")"),vr(r.storage_type)].filter(br).join(" ")}(r.generated);return t.push(u),t.filter(br).join(" ")}function K(r){return r?["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?fr(r):lr(r)].join(" "):""}function z(r,t){var e=r.expr,n=r.type;if("cast"===n)return h(r);t&&(e.isDual=t);var o=ut(e),u=r.expr_list;if(u){var a=[o],s=u.map((function(r){return z(r,t)})).join(", ");return a.push([vr(n),n&&"(",s,n&&")"].filter(br).join("")),a.filter(br).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o,"[").concat(dr(e.array_index.index),"]")),[o,K(r.as)].filter(br).join(" ")}function Z(r,t){if(!r||"*"===r)return r;var e=function(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}(t);return r.map((function(r){return z(r,e)})).join(", ")}function J(r){return(J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return wr})),e.d(n,"autoIncrementToSQL",(function(){return gr})),e.d(n,"columnOrderListToSQL",(function(){return Tr})),e.d(n,"commonKeywordArgsToSQL",(function(){return Ar})),e.d(n,"commonOptionConnector",(function(){return er})),e.d(n,"connector",(function(){return nr})),e.d(n,"commonTypeValue",(function(){return pr})),e.d(n,"commentToSQL",(function(){return Lr})),e.d(n,"createBinaryExpr",(function(){return ur})),e.d(n,"createValueExpr",(function(){return or})),e.d(n,"dataTypeToSQL",(function(){return mr})),e.d(n,"DEFAULT_OPT",(function(){return rr})),e.d(n,"escape",(function(){return ar})),e.d(n,"literalToSQL",(function(){return dr})),e.d(n,"columnIdentifierToSql",(function(){return lr})),e.d(n,"getParserOpt",(function(){return sr})),e.d(n,"identifierToSql",(function(){return fr})),e.d(n,"onPartitionsToSQL",(function(){return hr})),e.d(n,"replaceParams",(function(){return yr})),e.d(n,"returningToSQL",(function(){return Er})),e.d(n,"hasVal",(function(){return br})),e.d(n,"setParserOpt",(function(){return ir})),e.d(n,"toUpper",(function(){return vr})),e.d(n,"topToSQL",(function(){return cr})),e.d(n,"triggerEventToSQL",(function(){return Cr}));var rr={database:"db2",type:"table",parseOptions:{}},tr=rr;function er(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function nr(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function or(r){var t=J(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(or)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function ur(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:or(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[or(e[0]),or(e[1])]},n):(n.right=e.type?e:or(e),n)}function ar(r){return r}function sr(){return tr}function ir(r){tr=r}function cr(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function lr(r){var t=sr().database;if(r)switch(t&&t.toLowerCase()){case"postgresql":case"db2":case"snowflake":case"noql":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function fr(r,t){var e=sr().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":case"sqlite":return"`".concat(r,"`");case"postgresql":case"snowflake":case"noql":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function pr(r){var t=[];if(!r)return t;var e=r.type,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(o.toUpperCase()),t}function vr(r){if(r)return r.toUpperCase()}function br(r){return r}function dr(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,o=r.suffix,u=r.value,a="string"==typeof r?r:u;switch(e){case"backticks_quote_string":a="`".concat(u,"`");break;case"string":a="'".concat(u,"'");break;case"regex_string":a='r"'.concat(u,'"');break;case"hex_string":a="X'".concat(u,"'");break;case"full_hex_string":a="0x".concat(u);break;case"natural_string":a="N'".concat(u,"'");break;case"bit_string":a="b'".concat(u,"'");break;case"double_quote_string":a='"'.concat(u,'"');break;case"single_quote_string":a="'".concat(u,"'");break;case"boolean":case"bool":a=u?"TRUE":"FALSE";break;case"null":a="NULL";break;case"star":a="*";break;case"param":a="".concat(t||":").concat(u),t=null;break;case"origin":a=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":a="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":a="N'".concat(u,"'")}var s=[];return t&&s.push(vr(t)),s.push(a),o&&s.push("object"===J(o)&&o.collate?pr(o.collate).join(" "):vr(o)),a=s.join(" "),n?"(".concat(a,")"):a}}function yr(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===J(e)&&null!==e})).forEach((function(n){var o=t[n];if("object"!==J(o)||"param"!==o.type)return r(o,e);if(void 0===e[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return t[n]=or(e[o.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function hr(r){var t=r.type,e=r.partitions;return[vr(t),"(".concat(e.map((function(r){if("range"!==r.type)return dr(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(dr(t)," ").concat(vr(n)," ").concat(dr(e))})).join(", "),")")].join(" ")}function mr(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,u=r.suffix,a="";return null!=e&&(a=o?"".concat(e,", ").concat(o):e),n&&(a="(".concat(a,")")),u&&u.length&&(a+=" ".concat(u.join(" "))),"".concat(t).concat(a)}function wr(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=vr(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var u=e&&e.map((function(r){return[r.field_name,wr(r.field_type)].filter(br).join(" ")})).join(", ");return n?"".concat(o,"<").concat(u,">"):"".concat(o," ").concat(u)}}function Lr(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(dr(o)),t.join(" ")}}function Cr(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[vr(t)];if(e){var o=e.keyword,u=e.columns;n.push(vr(o),u.map(Y).join(", "))}return n.join(" ")})).join(" OR ")}function Er(r){return r?["RETURNING",r.columns.map(z).filter(br).join(", ")].join(" "):""}function Ar(r){return r?[vr(r.keyword),vr(r.args)]:[]}function gr(r){if(r){if("string"==typeof r){var t=sr().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,u=r.parentheses,a=vr(e);return u&&(a+="(".concat(dr(n),", ").concat(dr(o),")")),a}}function Tr(r){if(r)return r.map(Q).filter(br).join(", ")}function jr(r){var t=r.name,e=r.value;return["@".concat(t),"=",ut(e)].filter(br).join(" ")}function Sr(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var u=ut(t),a=ut(e);return"".concat(u," ").concat(n," ").concat(a)}function _r(r){var t,e,n,o,u=r.keyword,a=r.suffix,s="";switch(vr(u)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,s=[er("IN",dr,e&&e.right),er("FROM",$,n),A(o)].filter(br).join(" ");break;case"CHARACTER":case"COLLATION":s=function(r){var t=r.expr;if(t)return"LIKE"===vr(t.op)?er("LIKE",dr,t.right):er("WHERE",ut,t)}(r);break;case"COLUMNS":case"INDEXES":s=er("FROM",$,r.from);break;case"GRANTS":s=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,u="'".concat(e,"'");return n&&(u+="@'".concat(n,"'")),["FOR",u,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(br).join(" ")}}(r);break;case"CREATE":s=er("",H,r[a]);break;case"VAR":s=ot(r.var),u=""}return["SHOW",vr(u),vr(a),s].filter(br).join(" ")}var xr={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,u=r.type,a=t.expr,s=t.orderby;return[vr(u),vr(n),[[fr(o.schema),fr(o.name)].filter(br).join("."),"(".concat(a.map(Zr).join(", ")).concat(s?[" ORDER","BY",s.map(Zr).join(", ")].join(" "):"",")")].filter(br).join(""),zr(e)].filter(br).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.expr,o=void 0===n?[]:n,u=vr(t),a=$(e),s=o.map(ut);return[u,"TABLE",a,s.join(", ")].filter(br).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[vr(r.type),vr(e),fr(n),zr(t)].filter(br).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[vr(r.type),vr(e),[fr(n.schema),fr(n.name)].filter(br).join("."),zr(t)].filter(br).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[vr(r.type),vr(n),[[fr(o.schema),fr(o.name)].filter(br).join("."),t&&"(".concat(t.expr?t.expr.map(Zr).join(", "):"",")")].filter(br).join(""),zr(e)].filter(br).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,u=r.view,a=r.with,s=vr(t),i=H(u),c=[s,"VIEW",i];e&&c.push("(".concat(e.map(Y).join(", "),")"));n&&c.push("WITH ".concat(n.map(vr).join(", ")));c.push("AS",j(o)),a&&c.push(vr(a));return c.filter(br).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[vr(t),H(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,u=r.schema;return[vr(t),vr(e),ut(n),vr(o),fr(u)].filter(br).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,a=r.options,s=[vr(t),vr(e),vr(n)],i=[fr(o.schema),o.name].filter(br).join("."),c="".concat(u.expr.map(Zr).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(Zr).join(", ")].join(" "):"");return s.push("".concat(i,"(").concat(c,")"),"(".concat(a.map(Kr).join(", "),")")),s.filter(br).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,u=r.as,a=r.temporary,s=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.or_replace,p=r.query_expr,v=[vr(t),vr(f),vr(a),vr(e),vr(s),$(n)];if(o){var b=o.type,d=$(o.table);return v.push(vr(b),d),v.filter(br).join(" ")}i&&v.push("(".concat(i.map(Vr).join(", "),")"));c&&v.push(c.map(B).join(" "));v.push(vr(l),vr(u)),p&&v.push(kr(p));return v.filter(br).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,u=r.execute,a=r.for_each,s=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,v=r.when,b=[vr(f),vr(l),vr(e),vr(c),fr(t),vr(i)],d=Cr(o);b.push(d,"ON",H(p)),s&&b.push("FROM",H(s));b.push.apply(b,Wr(Ar(n)).concat(Wr(Ar(a)))),v&&b.push(vr(v.type),ut(v.cond));return b.push(vr(u.keyword),m(u.expr)),b.filter(br).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,u=r.type,a=r.table,s=r.if_not_exists,i=r.temporary,c=r.trigger,l=r.events,f=r.order,p=r.time,v=r.when,b=[vr(u),vr(i),t,vr(n),vr(s),H(c),vr(p),l.map((function(r){var t=[vr(r.keyword)],e=r.args;return e&&t.push(vr(e.keyword),e.columns.map(Y).join(", ")),t.join(" ")})),"ON",H(a),vr(e&&e.keyword),vr(e&&e.args),f&&"".concat(vr(f.keyword)," ").concat(fr(f.trigger)),er("WHEN",ut,v),vr(o.prefix)];switch(o.type){case"set":b.push(er("SET",x,o.expr));break;case"multiple":b.push(Ir(o.expr.ast))}return b.push(vr(o.suffix)),b.filter(br).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,u=r.schema,a=r.type,s=r.with,i=r.version;return[vr(a),vr(o),vr(n),dr(t),vr(s),er("SCHEMA",dr,u),er("VERSION",dr,i),er("FROM",dr,e)].filter(br).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,a=r.returns,s=r.options,i=r.last,c=[vr(t),vr(e),vr(n)],l=[fr(o.schema),o.name].filter(br).join("."),f=u.map(Zr).filter(br).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[vr(t),vr(e),Array.isArray(n)?"(".concat(n.map(X).join(", "),")"):Qr(n)].filter(br).join(" ")}(a),s.map(Xr).join(" "),i),c.filter(br).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.include,u=r.index_columns,s=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,v=r.algorithm_option,b=r.lock_option,d=r.on_kw,y=r.table,h=r.tablespace,m=r.type,w=r.where,L=r.with,C=r.with_before_where,E=L&&"WITH (".concat(i(L).join(", "),")"),A=o&&"".concat(vr(o.keyword)," (").concat(o.columns.map((function(r){return fr(r)})).join(", "),")"),g=[vr(m),vr(s),vr(n),vr(t),fr(l),vr(d),H(y)].concat(Wr(a(c)),["(".concat(Tr(u),")"),A,i(p).join(" "),zr(v),zr(b),er("TABLESPACE",dr,h)]);C?g.push(E,er("WHERE",ut,w)):g.push(er("WHERE",ut,w),E);return g.push(er("ON",ut,f),er("FILESTREAM_ON",dr,e)),g.filter(br).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,u=r.if_not_exists,a=r.create_definitions,s=[vr(t),vr(o),vr(e),vr(u),$(n)];a&&s.push(a.map(Vr).join(" "));return s.filter(br).join(" ")}(r);break;case"database":e=function(r){var t=r.type,e=r.keyword,n=r.database,o=r.if_not_exists,u=r.create_definitions,a=[vr(t),vr(e),vr(o),lr(n)];u&&a.push(u.map(B).join(" "));return a.filter(br).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,u=r.keyword,a=r.recursive,s=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,v=r.with,b=r.with_options,d=p.db,y=p.view,h=[fr(d),fr(y)].filter(br).join(".");return[vr(f),vr(s),vr(l),vr(a),t&&"ALGORITHM = ".concat(vr(t)),n,c&&"SQL SECURITY ".concat(vr(c)),vr(u),vr(o),h,e&&"(".concat(e.map(lr).join(", "),")"),b&&["WITH","(".concat(b.map((function(r){return pr(r).join(" ")})).join(", "),")")].join(" "),"AS",kr(i),vr(v)].filter(br).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,u=r.target,a=r.create_definitions,s=[vr(n),vr(o),[fr(e.schema),fr(e.name)].filter(br).join("."),vr(t),mr(u)];if(a&&a.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Yr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}(a);try{for(l.s();!(i=l.n()).done;){var f=i.value,v=f.type;switch(v){case"collate":c.push(pr(f).join(" "));break;case"default":c.push(vr(v),ut(f.value));break;case"constraint":c.push(p(f))}}}catch(r){l.e(r)}finally{l.f()}s.push(c.filter(br).join(" "))}return s.filter(br).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,u=r.resource,a=[vr(r.type),vr(n),[fr(o.schema),fr(o.name)].filter(br).join("."),vr(t),vr(u)];if(e){var s=[];switch(u){case"enum":s.push(ut(e))}a.push(s.filter(br).join(" "))}return a.filter(br).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,u=r.keyword,a=r.lock_option,s=r.password_options,i=r.require,c=r.resource_options,l=r.type,f=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[Br(t)];return e&&n.push(vr(e.keyword),e.auth_plugin,dr(e.value)),n.filter(br).join(" ")})).join(", "),p=[vr(l),vr(u),vr(o),f];n&&p.push(vr(n.keyword),n.value.map(Br).join(", "));p.push(er(i&&i.keyword,ut,i&&i.value)),c&&p.push(vr(c.keyword),c.value.map((function(r){return ut(r)})).join(" "));s&&s.forEach((function(r){return p.push(er(r.keyword,ut,r.value))}));return p.push(dr(a),Lr(e),dr(t)),p.filter(br).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},select:j,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[vr(t),vr(e),ut(n)].filter(br).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,u=r.orderby,a=r.with,s=r.limit,i=[g(a),"DELETE"],c=Z(t,e);return i.push(c),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||i.push($(n))),i.push(er("FROM",$,e)),i.push(er("WHERE",ut,o)),i.push(st(u,"order by")),i.push(A(s)),i.filter(br).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[vr(t),H(e),(n||[]).map(jr).filter(br).join(", ")].filter(br).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[vr(t)],u=[e];n&&u.push("(".concat(ut(n).join(", "),")"));return o.push(u.join("")),o.filter(br).join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,u=r.stmts;return[e,vr(t),n,"IN",Ir([o]),"LOOP",Ir(u),"END LOOP",e].filter(br).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,u=r.orderby,a=r.with,s=r.limit,i=r.returning;return[g(a),"UPDATE",$(e),er("SET",x,n),er("FROM",$,t),er("WHERE",ut,o),st(u,"order by"),A(s),Er(i)].filter(br).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,u=r.prefix,a=r.go,s=r.semicolons,i=r.suffix,c=[vr(r.type),ut(t),dr(u),"".concat(Ur(o.ast||o)).concat(s[0]),vr(a)];n&&c.push(n.map((function(r){return[vr(r.type),ut(r.boolean_expr),"THEN",Ur(r.then.ast||r.then),r.semicolon].filter(br).join(" ")})).join(" "));e&&c.push("ELSE","".concat(Ur(e.ast||e)).concat(s[1]));return c.push(dr(i)),c.filter(br).join(" ")},insert:U,drop:Hr,truncate:Hr,replace:U,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[vr(t)],u=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,u=r.datatype,a=r.not_null,s=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(br).join(""),vr(n),vr(o)];switch(c){case"variable":l.push.apply(l,[G(u)].concat(Dr(pr(r.collate)),[vr(a)])),i&&l.push(vr(i.keyword),ut(i.value));break;case"cursor":l.push(vr(s));break;case"table":l.push(vr(s),"(".concat(i.map(Vr).join(", "),")"))}return l.filter(br).join(" ")})).join("".concat(n," "));return o.push(u),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=vr(t),o=fr(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var u,a=Mr(e);try{for(a.s();!(u=a.n()).done;){var s=u.value.map(H);n.push(s.join(" TO "))}}catch(r){a.e(r)}finally{a.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=ut(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=vr(t);return"".concat(n," ").concat(fr(e))},set:function(r){var t=r.expr,e=ut(t);return"".concat("SET"," ").concat(e)},lock:$r,unlock:$r,show:_r,grant:qr,revoke:qr,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return Sr(t);case"return":return function(r){var t=r.type,e=r.expr;return[vr(t),ut(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,u=[vr(t),vr(e)];n&&u.push([dr(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(br).join(""),n.expr.map((function(r){return ut(r)})).join(", "));o&&u.push(vr(o.type),vr(o.option),o.symbol,o.expr.map((function(r){return ut(r)})).join(", "));return u.filter(br).join(" ")},transaction:function(r){return ut(r.expr)}};function kr(r){if(!r)return"";for(var t=xr[r.type],e=r,n=e._parentheses,o=e._orderby,u=e._limit,a=[n&&"(",t(r)];r._next;){var s=xr[r._next.type],i=vr(r.set_op);a.push(i,s(r._next)),r=r._next}return a.push(n&&")",st(o,"order by"),A(u)),a.filter(br).join(" ")}function Ir(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],u=kr(o);e===n-1&&"transaction"===o.type&&(u="".concat(u," ;")),t.push(u)}return t.join(" ; ")}var Nr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction"];function Rr(r){var t=r&&r.ast?r.ast:r;if(!Nr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function Or(r){return Array.isArray(r)?(r.forEach(Rr),Ir(r)):(Rr(r),kr(r))}function Ur(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[Or(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):Or(r)}function Mr(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Pr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function Dr(r){return function(r){if(Array.isArray(r))return Fr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Pr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pr(r,t){if(r){if("string"==typeof r)return Fr(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Fr(r,t):void 0}}function Fr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function Hr(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,u=[vr(t),vr(e),vr(o)];switch(e){case"table":u.push($(n));break;case"trigger":u.push([n[0].schema?"".concat(fr(n[0].schema),"."):"",fr(n[0].trigger)].filter(br).join(""));break;case"database":case"schema":case"procedure":u.push(fr(n));break;case"view":u.push($(n),r.options&&r.options.map(ut).filter(br).join(" "));break;case"index":u.push.apply(u,[Y(n)].concat(Dr(r.table?["ON",H(r.table)]:[]),[r.options&&r.options.map(ut).filter(br).join(" ")]))}return u.filter(br).join(" ")}function $r(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),vr(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var u,a=[],s=Mr(n);try{var i=function(){var r=u.value,t=r.table,e=r.lock_type,n=[H(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return vr(e[r])})).filter(br).join(" "))}a.push(n.join(" "))};for(s.s();!(u=s.n()).done;)i()}catch(r){s.e(r)}finally{s.f()}return o.push.apply(o,[a.join(", ")].concat(Dr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(br).join(" ")}function Br(r){var t=r.name,e=r.host,n=[dr(t)];return e&&n.push("@",dr(e)),n.join("")}function qr(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,u=r.on,a=r.to_from,s=r.user_or_roles,i=r.with,c=[vr(t),dr(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[ut(t)];return e&&n.push("(".concat(e.map(Y).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),u)switch(c.push("ON"),n){case"priv":c.push(dr(u.object_type),u.priv_level.map((function(r){return[fr(r.prefix),fr(r.name)].filter(br).join(".")})).join(", "));break;case"proxy":c.push(Br(u))}return c.push(vr(a),s.map(Br).join(", ")),c.push(dr(i)),c.filter(br).join(" ")}function Wr(r){return function(r){if(Array.isArray(r))return Gr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Yr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Yr(r,t){if(r){if("string"==typeof r)return Gr(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Gr(r,t):void 0}}function Gr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function Vr(r){if(!r)return[];var t,e,n,u,a=r.resource;switch(a){case"column":return X(r);case"index":return e=[],n=(t=r).keyword,u=t.index,e.push(vr(n)),e.push(u),e.push.apply(e,o(c(t))),e.filter(br).join(" ");case"constraint":return p(r);case"sequence":return[vr(r.prefix),ut(r.value)].filter(br).join(" ");default:throw new Error("unknown resource = ".concat(a," type"))}}function Qr(r){return r.dataType?mr(r):[fr(r.db),fr(r.schema),fr(r.table)].filter(br).join(".")}function Xr(r){var t=r.type;switch(t){case"as":return[vr(t),r.symbol,kr(r.declare),vr(r.begin),Ir(r.expr),vr(r.end),r.symbol].filter(br).join(" ");case"set":return[vr(t),r.parameter,vr(r.value&&r.value.prefix),r.value&&r.value.expr.map(ut).join(", ")].filter(br).join(" ");default:return ut(r)}}function Kr(r){var t=r.type,e=r.symbol,n=r.value,o=[vr(t),e];switch(vr(t)){case"SFUNC":o.push([fr(n.schema),n.name].filter(br).join("."));break;case"STYPE":case"MSTYPE":o.push(mr(n));break;default:o.push(ut(n))}return o.filter(br).join(" ")}function zr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.first_after,o=r.if_not_exists,u=r.keyword,a=r.old_column,s=r.prefix,i=r.resource,l=r.symbol,f="",p=[];switch(i){case"column":p=[X(r)];break;case"index":p=c(r),f=r[i];break;case"table":case"schema":f=fr(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=fr(r[i]);break;case"algorithm":case"lock":case"table-option":f=[l,vr(r[i])].filter(br).join(" ");break;case"constraint":f=fr(r[i]),p=[Vr(e)];break;case"key":f=fr(r[i]);break;default:f=[l,r[i]].filter((function(r){return null!==r})).join(" ")}return[vr(t),vr(u),vr(o),a&&Y(a),vr(s),f&&f.trim(),p.filter(br).join(" "),n&&"".concat(vr(n.keyword)," ").concat(Y(n.column))].filter(br).join(" ")}function Zr(r){var t=r.default&&[vr(r.default.keyword),ut(r.default.value)].join(" ");return[vr(r.mode),r.name,mr(r.type),t].filter(br).join(" ")}function Jr(r){return(Jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(vr(r.type)){case"STRUCT":return"(".concat(Z(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(Z(t),"]");var u=Array.isArray(n)?n.map((function(r){return"(".concat(Z(r),")")})).filter(br).join(", "):ut(n);return e?"[".concat(u,"]"):o?"(".concat(u,")"):u}(r);default:return""}}function tt(r){var t=r.definition,e=[vr(r.keyword)];return t&&"object"===Jr(t)&&(e.length=0,e.push(wr(t))),e.push(rt(r)),e.filter(br).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:zr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,u=ut(t.expr),a=r.name,s=y(n);t.distinct&&(u=["DISTINCT",u].join(" ")),t.orderby&&(u="".concat(u," ").concat(st(t.orderby,"order by"))),t.separator&&(u=[u,vr(t.separator.keyword),dr(t.separator.value)].filter(br).join(" "));var i=o?"WITHIN GROUP (".concat(st(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(ut(e.where),")"):"";return["".concat(a,"(").concat(u,")"),i,s,c].filter(br).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,u=t.having,a="".concat(vr(e),"(").concat(ut(o));return u&&(a="".concat(a," HAVING ").concat(vr(u.prefix)," ").concat(ut(u.expr))),[a="".concat(a,")"),y(n)].filter(br).join(" ")},window_func:function(r){var t=r.over;return[d(r),y(t)].filter(br).join(" ")},array:tt,assign:Sr,binary_expr:w,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(ut(n));for(var u=0,a=e.length;u<a;++u)t.push(e[u].type.toUpperCase()),e[u].cond&&(t.push(ut(e[u].cond)),t.push("THEN")),t.push(ut(e[u].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:h,column_ref:Y,column_definition:X,datatype:mr,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,u=t.source,a=["".concat(vr(e),"(").concat(vr(n)),"FROM",vr(o),ut(u)];return"".concat(a.filter(br).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[vr(t),e,ut(n)].filter(br).join(" ")}(t[r])})).filter(br).join(", ");return"".concat(vr(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,u=r.mode;return[[vr(o),"(".concat(n.map((function(r){return Y(r)})).join(", "),")")].join(" "),[vr(t),["(",ut(r.expr),u&&" ".concat(dr(u)),")"].filter(br).join("")].join(" "),K(e)].filter(br).join(" ")},function:m,insert:kr,interval:M,json:function(r){var t=r.keyword,e=r.expr_list;return[vr(t),e.map((function(r){return ut(r)})).join(", ")].join(" ")},show:_r,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args;return["".concat(e,"(").concat(ut(n).join(", "),")"),"AS",m(t)].join(" ")},tables:$,unnest:D,window:function(r){return r.expr.map(b).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,u=r.keyword,a=r.quoted,s=r.suffix,i=[];u&&i.push(u);var c=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,l="".concat(e||"").concat(c);return s&&(l+=s),i.push(l),[a,i.join(" "),a].filter(br).join("")}function ut(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var u=o[n];t[u]=e[u]}}return nt[t.type]?nt[t.type](t):dr(t)}}function at(r){return r?r.map(ut):[]}function st(r,t){if(!Array.isArray(r))return"";var e=[],n=vr(t);switch(n){case"ORDER BY":e=r.map((function(r){return[ut(r.expr),r.type||"ASC",vr(r.nulls)].filter(br).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return ut(r.expr)}))}return nr(n,e.join(", "))}nt.var=ot,nt.expr_list=function(r){var t=at(r.value);return r.parentheses?"(".concat(t.join(", "),")"):t},nt.select=function(r){var t="object"===et(r._next)?kr(r):j(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",u="".concat(t).concat(o).concat(ut(n));return e?"(".concat(u,")"):u};var it=e(0);function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var lt,ft,pt,vt,bt=(lt={},ft="db2",pt=it.parse,vt=function(r,t){if("object"!=ct(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=ct(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(ft,"string"),(ft="symbol"==ct(vt)?vt:String(vt))in lt?Object.defineProperty(lt,ft,{value:pt,enumerable:!0,configurable:!0,writable:!0}):lt[ft]=pt,lt);function dt(r){return(dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function yt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(!r)return;if("string"==typeof r)return ht(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return ht(r,t)}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function ht(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function mt(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,wt(n.key),n)}}function wt(r){var t=function(r,t){if("object"!=dt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=dt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==dt(t)?t:String(t)}var Lt=function(){function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}var t,e,n;return t=r,(e=[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr;return ir(t),Ur(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr;return ir(t),ut(r)}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr,e=t.database,n=void 0===e?"db2":e;ir(t);var o=n.toLowerCase();if(bt[o])return bt[o](r.trim(),t.parseOptions||rr.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:rr;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var u,a=this["".concat(o,"List")].bind(this),s=a(r,e),i=!0,c="",l=yt(s);try{for(l.s();!(u=l.n()).done;){var f,p=u.value,v=!1,b=yt(t);try{for(b.s();!(f=b.n()).done;){var d=f.value,y=new RegExp(d,"i");if(y.test(p)){v=!0;break}}}catch(r){b.e(r)}finally{b.f()}if(!v){c=p,i=!1;break}}}catch(r){l.e(r)}finally{l.f()}if(!i)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])&&mt(t.prototype,e),n&&mt(t,n),Object.defineProperty(t,"prototype",{writable:!1}),r}();function Ct(r){return(Ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":Ct(self))&&self&&(self.NodeSQLParser={Parser:Lt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":Ct(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":Ct(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Lt,util:n})}]));
//# sourceMappingURL=db2.js.map