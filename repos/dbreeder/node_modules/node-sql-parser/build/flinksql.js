!function(r,t){for(var e in t)r[e]=t[e]}(exports,function(r){var t={};function e(n){if(t[n])return t[n].exports;var o=t[n]={i:n,l:!1,exports:{}};return r[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}return e.m=r,e.c=t,e.d=function(r,t,n){e.o(r,t)||Object.defineProperty(r,t,{enumerable:!0,get:n})},e.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},e.t=function(r,t){if(1&t&&(r=e(r)),8&t)return r;if(4&t&&"object"==typeof r&&r&&r.__esModule)return r;var n=Object.create(null);if(e.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:r}),2&t&&"string"!=typeof r)for(var o in r)e.d(n,o,function(t){return r[t]}.bind(null,o));return n},e.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return e.d(t,"a",t),t},e.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},e.p="",e(e.s=1)}([function(r,t,e){"use strict";var n=e(2);function o(r,t,e,n){this.message=r,this.expected=t,this.found=e,this.location=n,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,o)}!function(r,t){function e(){this.constructor=r}e.prototype=t.prototype,r.prototype=new e}(o,Error),o.buildMessage=function(r,t){var e={literal:function(r){return'"'+o(r.text)+'"'},class:function(r){var t,e="";for(t=0;t<r.parts.length;t++)e+=r.parts[t]instanceof Array?u(r.parts[t][0])+"-"+u(r.parts[t][1]):u(r.parts[t]);return"["+(r.inverted?"^":"")+e+"]"},any:function(r){return"any character"},end:function(r){return"end of input"},other:function(r){return r.description}};function n(r){return r.charCodeAt(0).toString(16).toUpperCase()}function o(r){return r.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}function u(r){return r.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,(function(r){return"\\x0"+n(r)})).replace(/[\x10-\x1F\x7F-\x9F]/g,(function(r){return"\\x"+n(r)}))}return"Expected "+function(r){var t,n,o,u=new Array(r.length);for(t=0;t<r.length;t++)u[t]=(o=r[t],e[o.type](o));if(u.sort(),u.length>0){for(t=1,n=1;t<u.length;t++)u[t-1]!==u[t]&&(u[n]=u[t],n++);u.length=n}switch(u.length){case 1:return u[0];case 2:return u[0]+" or "+u[1];default:return u.slice(0,-1).join(", ")+", or "+u[u.length-1]}}(r)+" but "+function(r){return r?'"'+o(r)+'"':"end of input"}(t)+" found."},r.exports={SyntaxError:o,parse:function(r,t){t=void 0!==t?t:{};var e,u={},a={start:ta},s=ta,i=Ku("IF",!0),c=Ku("EXTENSION",!0),l=Ku("SCHEMA",!0),f=Ku("VERSION",!0),p=function(r,t){return nl(r,t,1)},b=Ku("NULLS",!0),v=Ku("FIRST",!0),d=Ku("LAST",!0),y=Ku("AUTO_INCREMENT",!0),E=Ku("UNIQUE",!0),h=Ku("KEY",!0),C=Ku("PRIMARY",!0),L=Ku("COLUMN_FORMAT",!0),w=Ku("FIXED",!0),m=Ku("DYNAMIC",!0),A=Ku("DEFAULT",!0),T=Ku("STORAGE",!0),S=Ku("DISK",!0),R=Ku("MEMORY",!0),I=Ku("ALGORITHM",!0),N=Ku("INSTANT",!0),_=Ku("INPLACE",!0),O=Ku("COPY",!0),g=Ku("LOCK",!0),j=Ku("NONE",!0),x=Ku("SHARED",!0),U=Ku("EXCLUSIVE",!0),k=Ku("PRIMARY KEY",!0),M=Ku("FOREIGN KEY",!0),D=Ku("MATCH FULL",!0),P=Ku("MATCH PARTIAL",!0),G=Ku("MATCH SIMPLE",!0),F=Ku("RESTRICT",!0),H=Ku("CASCADE",!0),B=Ku("SET NULL",!0),Y=Ku("NO ACTION",!0),$=Ku("SET DEFAULT",!0),V=Ku("TRIGGER",!0),W=Ku("BEFORE",!0),X=Ku("AFTER",!0),q=Ku("INSTEAD OF",!0),K=Ku("ON",!0),Q=Ku("EXECUTE",!0),J=Ku("PROCEDURE",!0),Z=Ku("FUNCTION",!0),z=Ku("OF",!0),rr=Ku("NOT",!0),tr=Ku("DEFERRABLE",!0),er=Ku("INITIALLY IMMEDIATE",!0),nr=Ku("INITIALLY DEFERRED",!0),or=Ku("FOR",!0),ur=Ku("EACH",!0),ar=Ku("ROW",!0),sr=Ku("STATEMENT",!0),ir=Ku("CHARACTER",!0),cr=Ku("SET",!0),lr=Ku("CHARSET",!0),fr=Ku("COLLATE",!0),pr=Ku("AVG_ROW_LENGTH",!0),br=Ku("KEY_BLOCK_SIZE",!0),vr=Ku("MAX_ROWS",!0),dr=Ku("MIN_ROWS",!0),yr=Ku("STATS_SAMPLE_PAGES",!0),Er=Ku("CONNECTION",!0),hr=Ku("COMPRESSION",!0),Cr=Ku("'",!1),Lr=Ku("ZLIB",!0),wr=Ku("LZ4",!0),mr=Ku("ENGINE",!0),Ar=Ku("IN",!0),Tr=Ku("ACCESS SHARE",!0),Sr=Ku("ROW SHARE",!0),Rr=Ku("ROW EXCLUSIVE",!0),Ir=Ku("SHARE UPDATE EXCLUSIVE",!0),Nr=Ku("SHARE ROW EXCLUSIVE",!0),_r=Ku("ACCESS EXCLUSIVE",!0),Or=Ku("SHARE",!0),gr=Ku("MODE",!0),jr=Ku("NOWAIT",!0),xr=Ku("(",!1),Ur=Ku(")",!1),kr=Ku("BTREE",!0),Mr=Ku("HASH",!0),Dr=Ku("GIST",!0),Pr=Ku("GIN",!0),Gr=Ku("WITH",!0),Fr=Ku("PARSER",!0),Hr=Ku("VISIBLE",!0),Br=Ku("INVISIBLE",!0),Yr=function(r,t){return t.unshift(r),t.forEach(r=>{const{table:t,as:e}=r;fl[t]=t,e&&(fl[e]=t),function(r){const t=al(r);r.clear(),t.forEach(t=>r.add(t))}(ll)}),t},$r=Ku("DESCRIPTOR",!0),Vr=Ku("=",!1),Wr=function(r,t){return ol(r,t)},Xr=Ku("!",!1),qr=Ku(">=",!1),Kr=Ku(">",!1),Qr=Ku("<=",!1),Jr=Ku("<>",!1),Zr=Ku("<",!1),zr=Ku("!=",!1),rt=Ku("ESCAPE",!0),tt=Ku("@>",!1),et=Ku("<@",!1),nt=Ku("?",!1),ot=Ku("?|",!1),ut=Ku("?&",!1),at=Ku("#-",!1),st=Ku("+",!1),it=Ku("-",!1),ct=Ku("*",!1),lt=Ku("/",!1),ft=Ku("%",!1),pt=Ku("$",!1),bt=function(r){return!0===zc[r.toUpperCase()]},vt=Ku('"',!1),dt=/^[^"]/,yt=Qu(['"'],!0,!1),Et=function(r){return r.join("")},ht=/^[^']/,Ct=Qu(["'"],!0,!1),Lt=Ku("`",!1),wt=/^[^`]/,mt=Qu(["`"],!0,!1),At=/^[A-Za-z_]/,Tt=Qu([["A","Z"],["a","z"],"_"],!1,!1),St=/^[A-Za-z0-9_\-]/,Rt=Qu([["A","Z"],["a","z"],["0","9"],"_","-"],!1,!1),It=/^[A-Za-z0-9_]/,Nt=Qu([["A","Z"],["a","z"],["0","9"],"_"],!1,!1),_t=Ku(":",!1),Ot=Ku("OVER",!0),gt=Ku("POSITION",!0),jt=Ku("BOTH",!0),xt=Ku("LEADING",!0),Ut=Ku("TRAILING",!0),kt=Ku("trim",!0),Mt=Ku("placing",!0),Dt=Ku("for",!0),Pt=Ku("overlay",!0),Gt=Ku("SUBSTRING",!0),Ft=Ku("CENTURY",!0),Ht=Ku("DAY",!0),Bt=Ku("DATE",!0),Yt=Ku("DECADE",!0),$t=Ku("DOW",!0),Vt=Ku("DOY",!0),Wt=Ku("EPOCH",!0),Xt=Ku("HOUR",!0),qt=Ku("ISODOW",!0),Kt=Ku("ISOYEAR",!0),Qt=Ku("MICROSECONDS",!0),Jt=Ku("MILLENNIUM",!0),Zt=Ku("MILLISECONDS",!0),zt=Ku("MINUTE",!0),re=Ku("MONTH",!0),te=Ku("QUARTER",!0),ee=Ku("SECOND",!0),ne=Ku("TIMEZONE",!0),oe=Ku("TIMEZONE_HOUR",!0),ue=Ku("TIMEZONE_MINUTE",!0),ae=Ku("WEEK",!0),se=Ku("YEAR",!0),ie=/^[^"\\\0-\x1F\x7F]/,ce=Qu(['"',"\\",["\0",""],""],!0,!1),le=/^[^'\\]/,fe=Qu(["'","\\"],!0,!1),pe=Ku("\\'",!1),be=Ku('\\"',!1),ve=Ku("\\\\",!1),de=Ku("\\/",!1),ye=Ku("\\b",!1),Ee=Ku("\\f",!1),he=Ku("\\n",!1),Ce=Ku("\\r",!1),Le=Ku("\\t",!1),we=Ku("\\u",!1),me=Ku("\\",!1),Ae=Ku("''",!1),Te=Ku('""',!1),Se=Ku("``",!1),Re=/^[\n\r]/,Ie=Qu(["\n","\r"],!1,!1),Ne=Ku(".",!1),_e=/^[0-9]/,Oe=Qu([["0","9"]],!1,!1),ge=/^[0-9a-fA-F]/,je=Qu([["0","9"],["a","f"],["A","F"]],!1,!1),xe=/^[eE]/,Ue=Qu(["e","E"],!1,!1),ke=/^[+\-]/,Me=Qu(["+","-"],!1,!1),De=Ku("NULL",!0),Pe=Ku("NOT NULL",!0),Ge=Ku("TRUE",!0),Fe=Ku("TO",!0),He=Ku("FALSE",!0),Be=(Ku("SHOW",!0),Ku("DROP",!0)),Ye=Ku("USE",!0),$e=Ku("ALTER",!0),Ve=Ku("SELECT",!0),We=Ku("UPDATE",!0),Xe=Ku("CREATE",!0),qe=Ku("TEMPORARY",!0),Ke=Ku("DELETE",!0),Qe=Ku("INSERT",!0),Je=Ku("RECURSIVE",!1),Ze=Ku("REPLACE",!0),ze=Ku("RETURNING",!0),rn=Ku("RENAME",!0),tn=Ku("IGNORE",!0),en=(Ku("EXPLAIN",!0),Ku("PARTITION",!0)),nn=Ku("INTO",!0),on=Ku("FROM",!0),un=Ku("AS",!0),an=Ku("TABLE",!0),sn=Ku("TABLESPACE",!0),cn=Ku("DATABASE",!0),ln=Ku("SCHEME",!0),fn=Ku("NATURAL",!0),pn=Ku("LEFT",!0),bn=Ku("RIGHT",!0),vn=Ku("FULL",!0),dn=Ku("INNER",!0),yn=Ku("JOIN",!0),En=Ku("CROSS",!0),hn=Ku("APPLY",!0),Cn=Ku("OUTER",!0),Ln=Ku("UNION",!0),wn=Ku("INTERSECT",!0),mn=Ku("EXCEPT",!0),An=Ku("VALUES",!0),Tn=Ku("USING",!0),Sn=Ku("WHERE",!0),Rn=Ku("GROUP",!0),In=Ku("BY",!0),Nn=Ku("ORDER",!0),_n=Ku("HAVING",!0),On=Ku("LIMIT",!0),gn=Ku("OFFSET",!0),jn=Ku("ASC",!0),xn=Ku("DESC",!0),Un=Ku("ALL",!0),kn=Ku("DISTINCT",!0),Mn=Ku("BETWEEN",!0),Dn=Ku("IS",!0),Pn=Ku("LIKE",!0),Gn=Ku("SIMILAR",!0),Fn=Ku("EXISTS",!0),Hn=Ku("AND",!0),Bn=Ku("OR",!0),Yn=Ku("COUNT",!0),$n=Ku("MAX",!0),Vn=Ku("MIN",!0),Wn=Ku("SUM",!0),Xn=Ku("AVG",!0),qn=Ku("COLLECT",!0),Kn=Ku("RANK",!0),Qn=Ku("DENSE_RANK",!0),Jn=Ku("LISTAGG",!0),Zn=Ku("ROW_NUMBER",!0),zn=Ku("TUMBLE",!0),ro=(Ku("TUMBLE_START",!0),Ku("TUMBLE_END",!0),Ku("HOP_START",!0),Ku("HOP_END",!0),Ku("SESSION_START",!0),Ku("SESSION_END",!0),Ku("TUMBLE_ROWTIME",!0),Ku("HOP_ROWTIME",!0),Ku("SESSION_ROWTIME",!0),Ku("TUMBLE_PROCTIME",!0),Ku("HOP_PROCTIME",!0),Ku("SESSION_PROCTIME",!0),Ku("EXTRACT",!0)),to=Ku("CALL",!0),eo=Ku("CASE",!0),no=Ku("WHEN",!0),oo=Ku("THEN",!0),uo=Ku("ELSE",!0),ao=Ku("END",!0),so=Ku("CAST",!0),io=Ku("TRY_CAST",!0),co=Ku("BOOL",!0),lo=Ku("BOOLEAN",!0),fo=Ku("CHAR",!0),po=Ku("VARCHAR",!0),bo=Ku("STRING",!0),vo=Ku("NUMERIC",!0),yo=Ku("DECIMAL",!0),Eo=Ku("SIGNED",!0),ho=Ku("UNSIGNED",!0),Co=Ku("INT",!0),Lo=Ku("ZEROFILL",!0),wo=Ku("INTEGER",!0),mo=Ku("JSON",!0),Ao=Ku("JSONB",!0),To=Ku("GEOMETRY",!0),So=Ku("SMALLINT",!0),Ro=Ku("TINYINT",!0),Io=Ku("TINYTEXT",!0),No=Ku("TEXT",!0),_o=Ku("MEDIUMTEXT",!0),Oo=Ku("LONGTEXT",!0),go=Ku("BIGINT",!0),jo=Ku("FLOAT",!0),xo=Ku("DOUBLE",!0),Uo=Ku("DATETIME",!0),ko=Ku("TIME",!0),Mo=Ku("TIMESTAMP",!0),Do=Ku("TRUNCATE",!0),Po=Ku("USER",!0),Go=Ku("UUID",!0),Fo=Ku("ARRAY",!0),Ho=Ku("MAP",!0),Bo=(Ku("MULTISET",!0),Ku("CURRENT_DATE",!0)),Yo=(Ku("ADDDATE",!0),Ku("INTERVAL",!0)),$o=(Ku("SECONDS",!0),Ku("CURRENT_TIME",!0)),Vo=Ku("CURRENT_TIMESTAMP",!0),Wo=Ku("CURRENT_USER",!0),Xo=Ku("SESSION_USER",!0),qo=Ku("SYSTEM_USER",!0),Ko=Ku("GLOBAL",!0),Qo=Ku("SESSION",!0),Jo=Ku("LOCAL",!0),Zo=Ku("PERSIST",!0),zo=Ku("PERSIST_ONLY",!0),ru=Ku("@",!1),tu=Ku("@@",!1),eu=Ku("return",!0),nu=Ku(":=",!1),ou=Ku("::",!1),uu=Ku("DUAL",!0),au=Ku("ADD",!0),su=Ku("COLUMN",!0),iu=Ku("INDEX",!0),cu=Ku("FULLTEXT",!0),lu=Ku("SPATIAL",!0),fu=Ku("COMMENT",!0),pu=Ku("CONSTRAINT",!0),bu=Ku("CONCURRENTLY",!0),vu=Ku("REFERENCES",!0),du=Ku("SQL_CALC_FOUND_ROWS",!0),yu=Ku("SQL_CACHE",!0),Eu=Ku("SQL_NO_CACHE",!0),hu=Ku("SQL_SMALL_RESULT",!0),Cu=Ku("SQL_BIG_RESULT",!0),Lu=Ku("SQL_BUFFER_RESULT",!0),wu=Ku(",",!1),mu=Ku("[",!1),Au=Ku("]",!1),Tu=Ku(";",!1),Su=Ku("->",!1),Ru=Ku("->>",!1),Iu=Ku("#>",!1),Nu=Ku("#>>",!1),_u=Ku("||",!1),Ou=Ku("&&",!1),gu=Ku("/*",!1),ju=Ku("*/",!1),xu=Ku("--",!1),Uu=(Ku("#",!1),{type:"any"}),ku=Ku("years",!0),Mu=Ku("months",!0),Du=Ku("days",!0),Pu=Ku("hours",!0),Gu=Ku("minutes",!0),Fu=Ku("seconds",!0),Hu=/^[ \t\n\r]/,Bu=Qu([" ","\t","\n","\r"],!1,!1),Yu=function(r){return{dataType:r}},$u=0,Vu=[{line:1,column:1}],Wu=0,Xu=[],qu=0;if("startRule"in t){if(!(t.startRule in a))throw new Error("Can't start parsing from rule \""+t.startRule+'".');s=a[t.startRule]}function Ku(r,t){return{type:"literal",text:r,ignoreCase:t}}function Qu(r,t,e){return{type:"class",parts:r,inverted:t,ignoreCase:e}}function Ju(t){var e,n=Vu[t];if(n)return n;for(e=t-1;!Vu[e];)e--;for(n={line:(n=Vu[e]).line,column:n.column};e<t;)10===r.charCodeAt(e)?(n.line++,n.column=1):n.column++,e++;return Vu[t]=n,n}function Zu(r,t){var e=Ju(r),n=Ju(t);return{start:{offset:r,line:e.line,column:e.column},end:{offset:t,line:n.line,column:n.column}}}function zu(r){$u<Wu||($u>Wu&&(Wu=$u,Xu=[]),Xu.push(r))}function ra(r,t,e){return new o(o.buildMessage(r,t),r,t,e)}function ta(){var r,t;return r=$u,xc()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=na())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=Nc())!==u&&(s=xc())!==u&&(i=na())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=Nc())!==u&&(s=xc())!==u&&(i=na())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=function(r,t){const e=r&&r.ast||r,n=t&&t.length&&t[0].length>=4?[e]:e;for(let r=0;r<t.length;r++)t[r][3]&&0!==t[r][3].length&&n.push(t[r][3]&&t[r][3].ast||t[r][3]);return{tableList:Array.from(cl),columnList:al(ll),ast:n}}(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())!==u?(r,r=t):($u=r,r=u),r}function ea(){var t;return(t=function(){var r,t,e,n,o,a;r=$u,(t=ui())!==u&&xc()!==u&&(e=Ci())!==u&&xc()!==u&&(n=Da())!==u?(r,s=t,i=e,(c=n)&&c.forEach(r=>cl.add(`${s}::${r.db}::${r.table}`)),t={tableList:Array.from(cl),columnList:al(ll),ast:{type:s.toLowerCase(),keyword:i.toLowerCase(),name:c}},r=t):($u=r,r=u);var s,i,c;r===u&&(r=$u,(t=ui())!==u&&xc()!==u&&(e=yc())!==u&&xc()!==u&&(n=As())!==u&&xc()!==u&&Li()!==u&&xc()!==u&&(o=Ha())!==u&&xc()!==u?((a=function(){var r,t,e,n,o,a;r=$u,(t=ba())===u&&(t=va());if(t!==u){for(e=[],n=$u,(o=xc())!==u?((a=ba())===u&&(a=va()),a!==u?n=o=[o,a]:($u=n,n=u)):($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u?((a=ba())===u&&(a=va()),a!==u?n=o=[o,a]:($u=n,n=u)):($u=n,n=u);e!==u?(r,t=p(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())===u&&(a=null),a!==u&&xc()!==u?(r,t=function(r,t,e,n,o){return{tableList:Array.from(cl),columnList:al(ll),ast:{type:r.toLowerCase(),keyword:t.toLowerCase(),name:e,table:n,options:o}}}(t,e,n,o,a),r=t):($u=r,r=u)):($u=r,r=u));return r}())===u&&(t=function(){var t;(t=function(){var r,t,e,n,o,a,s,i,c,l;r=$u,(t=si())!==u&&xc()!==u?((e=ii())===u&&(e=null),e!==u&&xc()!==u&&Ci()!==u&&xc()!==u?((n=aa())===u&&(n=null),n!==u&&xc()!==u&&(o=Da())!==u&&xc()!==u&&(a=function(){var r,t,e,n,o,a,s,i,c;if(r=$u,(t=Tc())!==u)if(xc()!==u)if((e=ia())!==u){for(n=[],o=$u,(a=xc())!==u&&(s=mc())!==u&&(i=xc())!==u&&(c=ia())!==u?o=a=[a,s,i,c]:($u=o,o=u);o!==u;)n.push(o),o=$u,(a=xc())!==u&&(s=mc())!==u&&(i=xc())!==u&&(c=ia())!==u?o=a=[a,s,i,c]:($u=o,o=u);n!==u&&(o=xc())!==u&&(a=Sc())!==u?(r,t=nl(e,n),r=t):($u=r,r=u)}else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;return r}())!==u&&xc()!==u?((s=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=ma())!==u){for(e=[],n=$u,(o=xc())!==u?((a=mc())===u&&(a=null),a!==u&&(s=xc())!==u&&(i=ma())!==u?n=o=[o,a,s,i]:($u=n,n=u)):($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u?((a=mc())===u&&(a=null),a!==u&&(s=xc())!==u&&(i=ma())!==u?n=o=[o,a,s,i]:($u=n,n=u)):($u=n,n=u);e!==u?(r,t=nl(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())===u&&(s=null),s!==u&&xc()!==u?((i=bi())===u&&(i=fi()),i===u&&(i=null),i!==u&&xc()!==u?((c=hi())===u&&(c=null),c!==u&&xc()!==u?((l=ua())===u&&(l=null),l!==u?(r,f=t,p=e,b=n,d=a,y=s,E=i,h=c,C=l,(v=o)&&v.forEach(r=>cl.add(`create::${r.db}::${r.table}`)),t={tableList:Array.from(cl),columnList:al(ll),ast:{type:f[0].toLowerCase(),keyword:"table",temporary:p&&p[0].toLowerCase(),if_not_exists:b,table:v,ignore_replace:E&&E[0].toLowerCase(),as:h&&h[0].toLowerCase(),query_expr:C&&C.ast,create_definitions:d,table_options:y}},r=t):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u);var f,p,b,v,d,y,E,h,C;r===u&&(r=$u,(t=si())!==u&&xc()!==u?((e=ii())===u&&(e=null),e!==u&&xc()!==u&&Ci()!==u&&xc()!==u?((n=aa())===u&&(n=null),n!==u&&xc()!==u&&(o=Da())!==u&&xc()!==u&&(a=function r(){var t,e;(t=function(){var r,t;r=$u,ki()!==u&&xc()!==u&&(t=Da())!==u?(r,r={type:"like",table:t}):($u=r,r=u);return r}())===u&&(t=$u,Tc()!==u&&xc()!==u&&(e=r())!==u&&xc()!==u&&Sc()!==u?(t,(n=e).parentheses=!0,t=n):($u=t,t=u));var n;return t}())!==u?(r,t=function(r,t,e,n,o){return n&&n.forEach(r=>cl.add(`create::${r.db}::${r.table}`)),{tableList:Array.from(cl),columnList:al(ll),ast:{type:r[0].toLowerCase(),keyword:"table",temporary:t&&t[0].toLowerCase(),if_not_exists:e,table:n,like:o}}}(t,e,n,o,a),r=t):($u=r,r=u)):($u=r,r=u)):($u=r,r=u));return r}())===u&&(t=function(){var t,e,n,o,a,s,i,c,l,f,p,b,v,d,y,E,h,C,L,w,m;t=$u,(e=si())!==u&&xc()!==u?(n=$u,(o=Fi())!==u&&(a=xc())!==u&&(s=fi())!==u?n=o=[o,a,s]:($u=n,n=u),n===u&&(n=null),n!==u&&(o=xc())!==u?((a=Lc())===u&&(a=null),a!==u&&(s=xc())!==u?("trigger"===r.substr($u,7).toLowerCase()?(i=r.substr($u,7),$u+=7):(i=u,0===qu&&zu(V)),i!==u&&xc()!==u&&(c=_s())!==u&&xc()!==u?("before"===r.substr($u,6).toLowerCase()?(l=r.substr($u,6),$u+=6):(l=u,0===qu&&zu(W)),l===u&&("after"===r.substr($u,5).toLowerCase()?(l=r.substr($u,5),$u+=5):(l=u,0===qu&&zu(X)),l===u&&("instead of"===r.substr($u,10).toLowerCase()?(l=r.substr($u,10),$u+=10):(l=u,0===qu&&zu(q)))),l!==u&&xc()!==u&&(f=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=La())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=Fi())!==u&&(s=xc())!==u&&(i=La())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=Fi())!==u&&(s=xc())!==u&&(i=La())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=nl(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())!==u&&xc()!==u?("on"===r.substr($u,2).toLowerCase()?(p=r.substr($u,2),$u+=2):(p=u,0===qu&&zu(K)),p!==u&&xc()!==u&&(b=Ha())!==u&&xc()!==u?(v=$u,(d=yi())!==u&&(y=xc())!==u&&(E=Ha())!==u?v=d=[d,y,E]:($u=v,v=u),v===u&&(v=null),v!==u&&(d=xc())!==u?((y=function(){var t,e,n,o,a;t=$u,e=$u,"not"===r.substr($u,3).toLowerCase()?(n=r.substr($u,3),$u+=3):(n=u,0===qu&&zu(rr));n===u&&(n=null);n!==u&&(o=xc())!==u?("deferrable"===r.substr($u,10).toLowerCase()?(a=r.substr($u,10),$u+=10):(a=u,0===qu&&zu(tr)),a!==u?e=n=[n,o,a]:($u=e,e=u)):($u=e,e=u);e!==u&&(n=xc())!==u?("initially immediate"===r.substr($u,19).toLowerCase()?(o=r.substr($u,19),$u+=19):(o=u,0===qu&&zu(er)),o===u&&("initially deferred"===r.substr($u,18).toLowerCase()?(o=r.substr($u,18),$u+=18):(o=u,0===qu&&zu(nr))),o!==u?(t,i=o,e={keyword:(s=e)&&s[0]?s[0].toLowerCase()+" deferrable":"deferrable",args:i&&i.toLowerCase()},t=e):($u=t,t=u)):($u=t,t=u);var s,i;return t}())===u&&(y=null),y!==u&&(E=xc())!==u?((h=function(){var t,e,n,o;t=$u,"for"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(or));e!==u&&xc()!==u?("each"===r.substr($u,4).toLowerCase()?(n=r.substr($u,4),$u+=4):(n=u,0===qu&&zu(ur)),n===u&&(n=null),n!==u&&xc()!==u?("row"===r.substr($u,3).toLowerCase()?(o=r.substr($u,3),$u+=3):(o=u,0===qu&&zu(ar)),o===u&&("statement"===r.substr($u,9).toLowerCase()?(o=r.substr($u,9),$u+=9):(o=u,0===qu&&zu(sr))),o!==u?(t,a=e,i=o,e={keyword:(s=n)?`${a.toLowerCase()} ${s.toLowerCase()}`:a.toLowerCase(),args:i.toLowerCase()},t=e):($u=t,t=u)):($u=t,t=u)):($u=t,t=u);var a,s,i;return t}())===u&&(h=null),h!==u&&xc()!==u?((C=function(){var r,t;r=$u,Yi()!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(t=ss())!==u&&xc()!==u&&Sc()!==u?(r,r={type:"when",cond:t,parentheses:!0}):($u=r,r=u);return r}())===u&&(C=null),C!==u&&xc()!==u?("execute"===r.substr($u,7).toLowerCase()?(L=r.substr($u,7),$u+=7):(L=u,0===qu&&zu(Q)),L!==u&&xc()!==u?("procedure"===r.substr($u,9).toLowerCase()?(w=r.substr($u,9),$u+=9):(w=u,0===qu&&zu(J)),w===u&&("function"===r.substr($u,8).toLowerCase()?(w=r.substr($u,8),$u+=8):(w=u,0===qu&&zu(Z))),w!==u&&xc()!==u&&(m=Xc())!==u?(t,A=a,T=i,R=f,I=b,N=v,_=y,O=h,g=C,j=w,x=m,e={type:"create",replace:n&&"or replace",constraint:c,location:(S=l)&&S.toLowerCase(),events:R,table:I,from:N&&N[2],deferrable:_,for_each:O,when:g,execute:{keyword:"execute "+j.toLowerCase(),expr:x},constraint_type:T&&T.toLowerCase(),keyword:T&&T.toLowerCase(),constraint_kw:A&&A.toLowerCase(),resource:"constraint"},t=e):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u);var A,T,S,R,I,N,_,O,g,j,x;return t}())===u&&(t=function(){var t,e,n,o,a,s,i,p,b,v,d,y,E,h;t=$u,(e=si())!==u&&xc()!==u?("extension"===r.substr($u,9).toLowerCase()?(n=r.substr($u,9),$u+=9):(n=u,0===qu&&zu(c)),n!==u&&xc()!==u?((o=aa())===u&&(o=null),o!==u&&xc()!==u?((a=_s())===u&&(a=Ws()),a!==u&&xc()!==u?((s=Ri())===u&&(s=null),s!==u&&xc()!==u?(i=$u,"schema"===r.substr($u,6).toLowerCase()?(p=r.substr($u,6),$u+=6):(p=u,0===qu&&zu(l)),p!==u&&(b=xc())!==u&&(v=_s())!==u?i=p=[p,b,v]:($u=i,i=u),i===u&&(i=Ws()),i===u&&(i=null),i!==u&&(p=xc())!==u?(b=$u,"version"===r.substr($u,7).toLowerCase()?(v=r.substr($u,7),$u+=7):(v=u,0===qu&&zu(f)),v!==u&&(d=xc())!==u?((y=_s())===u&&(y=Ws()),y!==u?b=v=[v,d,y]:($u=b,b=u)):($u=b,b=u),b===u&&(b=null),b!==u&&(v=xc())!==u?(d=$u,(y=yi())!==u&&(E=xc())!==u?((h=_s())===u&&(h=Ws()),h!==u?d=y=[y,E,h]:($u=d,d=u)):($u=d,d=u),d===u&&(d=null),d!==u?(t,C=o,L=a,w=s,m=i,A=b,T=d,e={type:"create",keyword:n.toLowerCase(),if_not_exists:C,extension:sl(L),with:w&&w[0].toLowerCase(),schema:sl(m&&m[2].toLowerCase()),version:sl(A&&A[2]),from:sl(T&&T[2])},t=e):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u);var C,L,w,m,A,T;return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c,l,f,p,b,v,d,y,E,h,C;t=$u,(e=si())!==u&&xc()!==u?((n=hc())===u&&(n=null),n!==u&&xc()!==u&&(o=yc())!==u&&xc()!==u?((a=function(){var t,e,n,o;t=$u,"concurrently"===r.substr($u,12).toLowerCase()?(e=r.substr($u,12),$u+=12):(e=u,0===qu&&zu(bu));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="CONCURRENTLY"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(a=null),a!==u&&xc()!==u?((s=Ts())===u&&(s=null),s!==u&&xc()!==u&&(i=Li())!==u&&xc()!==u&&(c=Ha())!==u&&xc()!==u?((l=Ua())===u&&(l=null),l!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(f=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=sa())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=sa())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=sa())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=nl(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())!==u&&xc()!==u&&Sc()!==u&&xc()!==u?(p=$u,(b=Ri())!==u&&(v=xc())!==u&&(d=Tc())!==u&&(y=xc())!==u&&(E=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=Ma())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Ma())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Ma())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=nl(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())!==u&&(h=xc())!==u&&(C=Sc())!==u?p=b=[b,v,d,y,E,h,C]:($u=p,p=u),p===u&&(p=null),p!==u&&(b=xc())!==u?(v=$u,(d=function(){var t,e,n,o;t=$u,"tablespace"===r.substr($u,10).toLowerCase()?(e=r.substr($u,10),$u+=10):(e=u,0===qu&&zu(sn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="TABLESPACE"):($u=t,t=u)):($u=t,t=u);return t}())!==u&&(y=xc())!==u&&(E=_s())!==u?v=d=[d,y,E]:($u=v,v=u),v===u&&(v=null),v!==u&&(d=xc())!==u?((y=$a())===u&&(y=null),y!==u&&(E=xc())!==u?(t,L=e,w=n,m=o,A=a,T=s,S=i,R=c,I=l,N=f,_=p,O=v,g=y,e={tableList:Array.from(cl),columnList:al(ll),ast:{type:L[0].toLowerCase(),index_type:w&&w.toLowerCase(),keyword:m.toLowerCase(),concurrently:A&&A.toLowerCase(),index:T,on_kw:S[0].toLowerCase(),table:R,index_using:I,index_columns:N,with:_&&_[4],with_before_where:!0,tablespace:O&&{type:"origin",value:O[2]},where:g}},t=e):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u);var L,w,m,A,T,S,R,I,N,_,O,g;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=$u,(e=si())!==u&&xc()!==u?((n=function(){var t,e,n,o;t=$u,"database"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(cn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="DATABASE"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=$u,"scheme"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(ln));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SCHEME"):($u=t,t=u)):($u=t,t=u);return t}()),n!==u&&xc()!==u?((o=aa())===u&&(o=null),o!==u&&xc()!==u&&(a=_s())!==u&&xc()!==u?((s=function(){var r,t,e,n,o,a;if(r=$u,(t=wa())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=wa())!==u?n=o=[o,a]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=wa())!==u?n=o=[o,a]:($u=n,n=u);e!==u?(r,t=p(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())===u&&(s=null),s!==u?(t,i=e,c=o,l=a,f=s,e={tableList:Array.from(cl),columnList:al(ll),ast:{type:i[0].toLowerCase(),keyword:"database",if_not_exists:c,database:l,create_definitions:f}},t=e):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u);var i,c,l,f;return t}());return t}())===u&&(t=function(){var r,t,e,n;r=$u,(t=ic())!==u&&xc()!==u?((e=Ci())===u&&(e=null),e!==u&&xc()!==u&&(n=Da())!==u?(r,o=t,a=e,(s=n)&&s.forEach(r=>cl.add(`${o}::${r.db}::${r.table}`)),t={tableList:Array.from(cl),columnList:al(ll),ast:{type:o.toLowerCase(),keyword:a&&a.toLowerCase()||"table",name:s}},r=t):($u=r,r=u)):($u=r,r=u);var o,a,s;return r}())===u&&(t=function(){var r,t,e;r=$u,(t=pi())!==u&&xc()!==u&&Ci()!==u&&xc()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=xa())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=xa())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=xa())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=nl(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())!==u?(r,(n=e).forEach(r=>r.forEach(r=>r.table&&cl.add(`rename::${r.db}::${r.table}`))),t={tableList:Array.from(cl),columnList:al(ll),ast:{type:"rename",table:n}},r=t):($u=r,r=u);var n;return r}())===u&&(t=function(){var t,e,n;t=$u,(e=function(){var t,e,n,o;t=$u,"call"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(to));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="CALL"):($u=t,t=u)):($u=t,t=u);return t}())!==u&&xc()!==u&&(n=Xc())!==u?(t,o=n,e={tableList:Array.from(cl),columnList:al(ll),ast:{type:"call",expr:o}},t=e):($u=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n;t=$u,(e=function(){var t,e,n,o;t=$u,"use"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Ye));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}())!==u&&xc()!==u&&(n=Ts())!==u?(t,o=n,cl.add(`use::${o}::null`),e={tableList:Array.from(cl),columnList:al(ll),ast:{type:"use",db:o}},t=e):($u=t,t=u);var o;return t}())===u&&(t=function(){var t,e,n,o;t=$u,(e=function(){var t,e,n,o;t=$u,"alter"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu($e));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}())!==u&&xc()!==u&&Ci()!==u&&xc()!==u&&(n=Da())!==u&&xc()!==u&&(o=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=pa())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=pa())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=pa())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=nl(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())!==u?(t,s=o,(a=n)&&a.length>0&&a.forEach(r=>cl.add(`alter::${r.db}::${r.table}`)),e={tableList:Array.from(cl),columnList:al(ll),ast:{type:"alter",table:a,expr:s}},t=e):($u=t,t=u);var a,s;return t}())===u&&(t=function(){var t,e,n,o;t=$u,(e=Ei())!==u&&xc()!==u?((n=function(){var t,e,n,o;t=$u,"global"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(Ko));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="GLOBAL"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=$u,"session"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Qo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SESSION"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=$u,"local"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Jo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="LOCAL"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=$u,"persist"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Zo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="PERSIST"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(n=function(){var t,e,n,o;t=$u,"persist_only"===r.substr($u,12).toLowerCase()?(e=r.substr($u,12),$u+=12):(e=u,0===qu&&zu(zo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="PERSIST_ONLY"):($u=t,t=u)):($u=t,t=u);return t}()),n===u&&(n=null),n!==u&&xc()!==u&&(o=Hc())!==u?(t,a=n,(s=o).keyword=a,e={tableList:Array.from(cl),columnList:al(ll),ast:{type:"set",expr:s}},t=e):($u=t,t=u)):($u=t,t=u);var a,s;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=$u,(e=function(){var t,e,n,o;t=$u,"lock"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(g));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}())!==u&&xc()!==u?((n=Ci())===u&&(n=null),n!==u&&xc()!==u&&(o=Da())!==u&&xc()!==u?((a=function(){var t,e,n,o;t=$u,"in"===r.substr($u,2).toLowerCase()?(e=r.substr($u,2),$u+=2):(e=u,0===qu&&zu(Ar));e!==u&&xc()!==u?("access share"===r.substr($u,12).toLowerCase()?(n=r.substr($u,12),$u+=12):(n=u,0===qu&&zu(Tr)),n===u&&("row share"===r.substr($u,9).toLowerCase()?(n=r.substr($u,9),$u+=9):(n=u,0===qu&&zu(Sr)),n===u&&("row exclusive"===r.substr($u,13).toLowerCase()?(n=r.substr($u,13),$u+=13):(n=u,0===qu&&zu(Rr)),n===u&&("share update exclusive"===r.substr($u,22).toLowerCase()?(n=r.substr($u,22),$u+=22):(n=u,0===qu&&zu(Ir)),n===u&&("share row exclusive"===r.substr($u,19).toLowerCase()?(n=r.substr($u,19),$u+=19):(n=u,0===qu&&zu(Nr)),n===u&&("exclusive"===r.substr($u,9).toLowerCase()?(n=r.substr($u,9),$u+=9):(n=u,0===qu&&zu(U)),n===u&&("access exclusive"===r.substr($u,16).toLowerCase()?(n=r.substr($u,16),$u+=16):(n=u,0===qu&&zu(_r)),n===u&&("share"===r.substr($u,5).toLowerCase()?(n=r.substr($u,5),$u+=5):(n=u,0===qu&&zu(Or))))))))),n!==u&&xc()!==u?("mode"===r.substr($u,4).toLowerCase()?(o=r.substr($u,4),$u+=4):(o=u,0===qu&&zu(gr)),o!==u?(t,e={mode:`in ${n.toLowerCase()} mode`},t=e):($u=t,t=u)):($u=t,t=u)):($u=t,t=u);return t}())===u&&(a=null),a!==u&&xc()!==u?("nowait"===r.substr($u,6).toLowerCase()?(s=r.substr($u,6),$u+=6):(s=u,0===qu&&zu(jr)),s===u&&(s=null),s!==u?(t,i=n,l=a,f=s,(c=o)&&c.forEach(r=>cl.add(`lock::${r.db}::${r.table}`)),e={tableList:Array.from(cl),columnList:al(ll),ast:{type:"lock",keyword:i&&i.toLowerCase(),tables:c.map(r=>({table:r})),lock_mode:l,nowait:f}},t=e):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u);var i,c,l,f;return t}()),t}function na(){var r;return(r=ua())===u&&(r=function(){var r,t,e,n,o,a;r=$u,(t=ai())!==u&&xc()!==u&&(e=Da())!==u&&xc()!==u&&Ei()!==u&&xc()!==u&&(n=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=Qa())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Qa())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Qa())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=nl(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())!==u&&xc()!==u?((o=$a())===u&&(o=null),o!==u&&xc()!==u?((a=Ja())===u&&(a=null),a!==u?(r,t=function(r,t,e,n){const o={};return r&&r.forEach(r=>{const{db:t,as:e,table:n,join:u}=r,a=u?"select":"update";t&&(o[n]=t),n&&cl.add(`${a}::${t}::${n}`)}),t&&t.forEach(r=>{if(r.table){const t=ul(r.table);cl.add(`update::${o[t]||null}::${t}`)}ll.add(`update::${r.table}::${r.column}`)}),{tableList:Array.from(cl),columnList:al(ll),ast:{type:"update",table:r,set:t,where:e,returning:n}}}(e,n,o,a),r=t):($u=r,r=u)):($u=r,r=u)):($u=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o,a,s,i;r=$u,(t=rs())!==u&&xc()!==u?((e=di())===u&&(e=null),e!==u&&xc()!==u&&(n=Ha())!==u&&xc()!==u?((o=za())===u&&(o=null),o!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(a=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=Is())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Is())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Is())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=nl(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())!==u&&xc()!==u&&Sc()!==u&&xc()!==u&&(s=Za())!==u&&xc()!==u?((i=Ja())===u&&(i=null),i!==u?(r,t=function(r,t,e,n,o,u){if(t&&(cl.add(`insert::${t.db}::${t.table}`),t.as=null),n){let r=t&&t.table||null;Array.isArray(o)&&o.forEach((r,t)=>{if(r.value.length!=n.length)throw new Error("Error: column count doesn't match value count at row "+(t+1))}),n.forEach(t=>ll.add(`insert::${r}::${t}`))}return{tableList:Array.from(cl),columnList:al(ll),ast:{type:r,table:[t],columns:n,values:o,partition:e,returning:u}}}(t,n,o,a,s,i),r=t):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o,a,s,i;r=$u,(t=rs())!==u&&xc()!==u?((e=bi())===u&&(e=null),e!==u&&xc()!==u?((n=di())===u&&(n=null),n!==u&&xc()!==u&&(o=Ha())!==u&&xc()!==u?((a=za())===u&&(a=null),a!==u&&xc()!==u&&(s=Za())!==u&&xc()!==u?((i=Ja())===u&&(i=null),i!==u?(r,t=function(r,t,e,n,o,u,a){n&&(cl.add(`insert::${n.db}::${n.table}`),ll.add(`insert::${n.table}::(.*)`),n.as=null);const s=[t,e].filter(r=>r).map(r=>r[0]&&r[0].toLowerCase()).join(" ");return{tableList:Array.from(cl),columnList:al(ll),ast:{type:r,table:[n],columns:null,values:u,partition:o,prefix:s,returning:a}}}(t,e,n,o,a,s,i),r=t):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u);return r}())===u&&(r=function(){var r,t,e,n,o;r=$u,(t=ci())!==u&&xc()!==u?((e=Da())===u&&(e=null),e!==u&&xc()!==u&&(n=ja())!==u&&xc()!==u?((o=$a())===u&&(o=null),o!==u?(r,t=function(r,t,e){if(t&&t.forEach(r=>{const{db:t,as:e,table:n,join:o}=r,u=o?"select":"delete";n&&cl.add(`${u}::${t}::${n}`),o||ll.add(`delete::${n}::(.*)`)}),null===r&&1===t.length){const e=t[0];r=[{db:e.db,table:e.table,as:e.as,addition:!0}]}return{tableList:Array.from(cl),columnList:al(ll),ast:{type:"delete",table:r,from:t,where:e}}}(e,n,o),r=t):($u=r,r=u)):($u=r,r=u)):($u=r,r=u);return r}())===u&&(r=ea())===u&&(r=function(){var r,t;r=[],t=Fc();for(;t!==u;)r.push(t),t=Fc();return r}()),r}function oa(){var t,e,n,o,a;return t=$u,(e=function(){var t,e,n,o;t=$u,"union"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Ln));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="UNION"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=$u,"intersect"===r.substr($u,9).toLowerCase()?(e=r.substr($u,9),$u+=9):(e=u,0===qu&&zu(wn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="INTERSECT"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=$u,"except"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(mn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="EXCEPT"):($u=t,t=u)):($u=t,t=u);return t}()),e!==u&&xc()!==u?((n=Oi())===u&&(n=gi()),n===u&&(n=null),n!==u?(t,o=e,t=e=(a=n)?`${o.toLowerCase()} ${a.toLowerCase()}`:""+o.toLowerCase()):($u=t,t=u)):($u=t,t=u),t}function ua(){var r,t,e,n,o,a,s,i;if(r=$u,(t=Aa())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=oa())!==u&&(s=xc())!==u&&(i=Aa())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=oa())!==u&&(s=xc())!==u&&(i=Aa())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u&&(n=xc())!==u?((o=Wa())===u&&(o=null),o!==u&&(a=xc())!==u?((s=Ka())===u&&(s=null),s!==u?(r,r=t=function(r,t,e,n){let o=r;for(let r=0;r<t.length;r++)o._next=t[r][3],o.set_op=t[r][1],o=o._next;return e&&(r._orderby=e),n&&(r._limit=n),{tableList:Array.from(cl),columnList:al(ll),ast:r}}(t,e,o,s)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)}else $u=r,r=u;return r}function aa(){var t,e;return t=$u,"if"===r.substr($u,2).toLowerCase()?(e=r.substr($u,2),$u+=2):(e=u,0===qu&&zu(i)),e!==u&&xc()!==u&&Pi()!==u&&xc()!==u&&Di()!==u?(t,t=e="IF NOT EXISTS"):($u=t,t=u),t}function sa(){var t,e,n,o,a,s,i,c,l,f,p,y,E,h;return t=$u,(e=ss())!==u&&xc()!==u?((n=fa())===u&&(n=null),n!==u&&xc()!==u?((o=Ts())===u&&(o=null),o!==u&&xc()!==u?((a=Ni())===u&&(a=_i()),a===u&&(a=null),a!==u&&xc()!==u?(s=$u,"nulls"===r.substr($u,5).toLowerCase()?(i=r.substr($u,5),$u+=5):(i=u,0===qu&&zu(b)),i!==u&&(c=xc())!==u?("first"===r.substr($u,5).toLowerCase()?(l=r.substr($u,5),$u+=5):(l=u,0===qu&&zu(v)),l===u&&("last"===r.substr($u,4).toLowerCase()?(l=r.substr($u,4),$u+=4):(l=u,0===qu&&zu(d))),l!==u?s=i=[i,c,l]:($u=s,s=u)):($u=s,s=u),s===u&&(s=null),s!==u?(t,f=e,p=n,y=o,E=a,h=s,t=e={...f,collate:p,opclass:y,order_by:E&&E.toLowerCase(),nulls:h&&`${h[0].toLowerCase()} ${h[2].toLowerCase()}`}):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t}function ia(){var t;return(t=la())===u&&(t=da())===u&&(t=ya())===u&&(t=function(){var t;(t=function(){var t,e,n,o,a,s;t=$u,(e=Ea())===u&&(e=null);e!==u&&xc()!==u?("primary key"===r.substr($u,11).toLowerCase()?(n=r.substr($u,11),$u+=11):(n=u,0===qu&&zu(k)),n!==u&&xc()!==u?((o=Ua())===u&&(o=null),o!==u&&xc()!==u&&(a=Ra())!==u&&xc()!==u?((s=ka())===u&&(s=null),s!==u?(t,c=n,l=o,f=a,p=s,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c.toLowerCase(),keyword:i&&i.keyword,index_type:l,resource:"constraint",index_options:p},t=e):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u);var i,c,l,f,p;return t}())===u&&(t=function(){var r,t,e,n,o,a,s,i;r=$u,(t=Ea())===u&&(t=null);t!==u&&xc()!==u&&(e=hc())!==u&&xc()!==u?((n=yc())===u&&(n=Ec()),n===u&&(n=null),n!==u&&xc()!==u?((o=Is())===u&&(o=null),o!==u&&xc()!==u?((a=Ua())===u&&(a=null),a!==u&&xc()!==u&&(s=Ra())!==u&&xc()!==u?((i=ka())===u&&(i=null),i!==u?(r,l=e,f=n,p=o,b=a,v=s,d=i,t={constraint:(c=t)&&c.constraint,definition:v,constraint_type:f&&`${l.toLowerCase()} ${f.toLowerCase()}`||l.toLowerCase(),keyword:c&&c.keyword,index_type:b,index:p,resource:"constraint",index_options:d},r=t):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u);var c,l,f,p,b,v,d;return r}())===u&&(t=function(){var t,e,n,o,a,s;t=$u,(e=Ea())===u&&(e=null);e!==u&&xc()!==u?("foreign key"===r.substr($u,11).toLowerCase()?(n=r.substr($u,11),$u+=11):(n=u,0===qu&&zu(M)),n!==u&&xc()!==u?((o=Is())===u&&(o=null),o!==u&&xc()!==u&&(a=Ra())!==u&&xc()!==u?((s=ha())===u&&(s=null),s!==u?(t,c=n,l=o,f=a,p=s,e={constraint:(i=e)&&i.constraint,definition:f,constraint_type:c,keyword:i&&i.keyword,index:l,resource:"constraint",reference_definition:p},t=e):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u);var i,c,l,f,p;return t}());return t}()),t}function ca(){var t,e,n,o;return t=$u,(e=function(){var t,e;t=$u,(e=function(){var t,e,n,o;t=$u,"not null"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(Pe));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}())!==u&&(t,e={type:"not null",value:"not null"});return t=e}())===u&&(e=Vs()),e!==u&&(t,(o=e)&&!o.value&&(o.value="null"),e={nullable:o}),(t=e)===u&&(t=$u,(e=function(){var r,t;r=$u,ni()!==u&&xc()!==u?((t=$s())===u&&(t=ss()),t!==u?(r,r={type:"default",value:t}):($u=r,r=u)):($u=r,r=u);return r}())!==u&&(t,e={default_val:e}),(t=e)===u&&(t=$u,"auto_increment"===r.substr($u,14).toLowerCase()?(e=r.substr($u,14),$u+=14):(e=u,0===qu&&zu(y)),e!==u&&(t,e={auto_increment:e.toLowerCase()}),(t=e)===u&&(t=$u,"unique"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(E)),e!==u&&xc()!==u?("key"===r.substr($u,3).toLowerCase()?(n=r.substr($u,3),$u+=3):(n=u,0===qu&&zu(h)),n===u&&(n=null),n!==u?(t,t=e=function(r){const t=["unique"];return r&&t.push(r),{unique:t.join(" ").toLowerCase("")}}(n)):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,"primary"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(C)),e===u&&(e=null),e!==u&&xc()!==u?("key"===r.substr($u,3).toLowerCase()?(n=r.substr($u,3),$u+=3):(n=u,0===qu&&zu(h)),n!==u?(t,t=e=function(r){const t=[];return r&&t.push("primary"),t.push("key"),{primary_key:t.join(" ").toLowerCase("")}}(e)):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,(e=Mc())!==u&&(t,e={comment:e}),(t=e)===u&&(t=$u,(e=fa())!==u&&(t,e={collate:e}),(t=e)===u&&(t=$u,(e=function(){var t,e,n;t=$u,"column_format"===r.substr($u,13).toLowerCase()?(e=r.substr($u,13),$u+=13):(e=u,0===qu&&zu(L));e!==u&&xc()!==u?("fixed"===r.substr($u,5).toLowerCase()?(n=r.substr($u,5),$u+=5):(n=u,0===qu&&zu(w)),n===u&&("dynamic"===r.substr($u,7).toLowerCase()?(n=r.substr($u,7),$u+=7):(n=u,0===qu&&zu(m)),n===u&&("default"===r.substr($u,7).toLowerCase()?(n=r.substr($u,7),$u+=7):(n=u,0===qu&&zu(A)))),n!==u?(t,e={type:"column_format",value:n.toLowerCase()},t=e):($u=t,t=u)):($u=t,t=u);return t}())!==u&&(t,e={column_format:e}),(t=e)===u&&(t=$u,(e=function(){var t,e,n;t=$u,"storage"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(T));e!==u&&xc()!==u?("disk"===r.substr($u,4).toLowerCase()?(n=r.substr($u,4),$u+=4):(n=u,0===qu&&zu(S)),n===u&&("memory"===r.substr($u,6).toLowerCase()?(n=r.substr($u,6),$u+=6):(n=u,0===qu&&zu(R))),n!==u?(t,e={type:"storage",value:n.toLowerCase()},t=e):($u=t,t=u)):($u=t,t=u);return t}())!==u&&(t,e={storage:e}),(t=e)===u&&(t=$u,(e=ha())!==u&&(t,e={reference_definition:e}),t=e))))))))),t}function la(){var r,t,e,n,o,a,s;return r=$u,(t=As())!==u&&xc()!==u&&(e=Jc())!==u&&xc()!==u?((n=function(){var r,t,e,n,o,a;if(r=$u,(t=ca())!==u)if(xc()!==u){for(e=[],n=$u,(o=xc())!==u&&(a=ca())!==u?n=o=[o,a]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=ca())!==u?n=o=[o,a]:($u=n,n=u);e!==u?(r,r=t=function(r,t){let e=r;for(let r=0;r<t.length;r++)e={...e,...t[r][1]};return e}(t,e)):($u=r,r=u)}else $u=r,r=u;else $u=r,r=u;return r}())===u&&(n=null),n!==u?(r,o=t,a=e,s=n,ll.add(`create::${o.table}::${o.column}`),r=t={column:o,definition:a,resource:"column",...s||{}}):($u=r,r=u)):($u=r,r=u),r}function fa(){var t,e;return t=$u,function(){var t,e,n,o;t=$u,"collate"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(fr));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="COLLATE"):($u=t,t=u)):($u=t,t=u);return t}()!==u&&xc()!==u&&(e=Ts())!==u?(t,t={type:"collate",value:e}):($u=t,t=u),t}function pa(){var r;return(r=function(){var r,t,e,n;r=$u,(t=vc())!==u&&xc()!==u?((e=dc())===u&&(e=null),e!==u&&xc()!==u&&(n=la())!==u?(r,o=e,a=n,t={action:"add",...a,keyword:o,resource:"column",type:"alter"},r=t):($u=r,r=u)):($u=r,r=u);var o,a;return r}())===u&&(r=function(){var r,t,e;r=$u,ui()!==u&&xc()!==u?((t=dc())===u&&(t=null),t!==u&&xc()!==u&&(e=As())!==u?(r,r={action:"drop",column:e,keyword:t,resource:"column",type:"alter"}):($u=r,r=u)):($u=r,r=u);return r}())===u&&(r=function(){var r,t,e;r=$u,(t=vc())!==u&&xc()!==u&&(e=da())!==u?(r,n=e,t={action:"add",type:"alter",...n},r=t):($u=r,r=u);var n;return r}())===u&&(r=function(){var r,t,e;r=$u,(t=vc())!==u&&xc()!==u&&(e=ya())!==u?(r,n=e,t={action:"add",type:"alter",...n},r=t):($u=r,r=u);var n;return r}())===u&&(r=function(){var r,t,e,n;r=$u,(t=pi())!==u&&xc()!==u?((e=oi())===u&&(e=hi()),e===u&&(e=null),e!==u&&xc()!==u&&(n=Ts())!==u?(r,a=n,t={action:"rename",type:"alter",resource:"table",keyword:(o=e)&&o[0].toLowerCase(),table:a},r=t):($u=r,r=u)):($u=r,r=u);var o,a;return r}())===u&&(r=ba())===u&&(r=va()),r}function ba(){var t,e,n,o;return t=$u,"algorithm"===r.substr($u,9).toLowerCase()?(e=r.substr($u,9),$u+=9):(e=u,0===qu&&zu(I)),e!==u&&xc()!==u?((n=bc())===u&&(n=null),n!==u&&xc()!==u?("default"===r.substr($u,7).toLowerCase()?(o=r.substr($u,7),$u+=7):(o=u,0===qu&&zu(A)),o===u&&("instant"===r.substr($u,7).toLowerCase()?(o=r.substr($u,7),$u+=7):(o=u,0===qu&&zu(N)),o===u&&("inplace"===r.substr($u,7).toLowerCase()?(o=r.substr($u,7),$u+=7):(o=u,0===qu&&zu(_)),o===u&&("copy"===r.substr($u,4).toLowerCase()?(o=r.substr($u,4),$u+=4):(o=u,0===qu&&zu(O))))),o!==u?(t,t=e={type:"alter",keyword:"algorithm",resource:"algorithm",symbol:n,algorithm:o}):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t}function va(){var t,e,n,o;return t=$u,"lock"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(g)),e!==u&&xc()!==u?((n=bc())===u&&(n=null),n!==u&&xc()!==u?("default"===r.substr($u,7).toLowerCase()?(o=r.substr($u,7),$u+=7):(o=u,0===qu&&zu(A)),o===u&&("none"===r.substr($u,4).toLowerCase()?(o=r.substr($u,4),$u+=4):(o=u,0===qu&&zu(j)),o===u&&("shared"===r.substr($u,6).toLowerCase()?(o=r.substr($u,6),$u+=6):(o=u,0===qu&&zu(x)),o===u&&("exclusive"===r.substr($u,9).toLowerCase()?(o=r.substr($u,9),$u+=9):(o=u,0===qu&&zu(U))))),o!==u?(t,t=e={type:"alter",keyword:"lock",resource:"lock",symbol:n,lock:o}):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t}function da(){var r,t,e,n,o,a,s,i;return r=$u,(t=yc())===u&&(t=Ec()),t!==u&&xc()!==u?((e=Is())===u&&(e=null),e!==u&&xc()!==u?((n=Ua())===u&&(n=null),n!==u&&xc()!==u&&(o=Ra())!==u&&xc()!==u?((a=ka())===u&&(a=null),a!==u&&xc()!==u?(r,s=n,i=a,r=t={index:e,definition:o,keyword:t.toLowerCase(),index_type:s,resource:"index",index_options:i}):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u),r}function ya(){var t,e,n,o,a,s,i,c,l;return t=$u,(e=function(){var t,e,n,o;t=$u,"fulltext"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(cu));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="FULLTEXT"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(e=function(){var t,e,n,o;t=$u,"spatial"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(lu));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SPATIAL"):($u=t,t=u)):($u=t,t=u);return t}()),e!==u&&xc()!==u?((n=yc())===u&&(n=Ec()),n===u&&(n=null),n!==u&&xc()!==u?((o=Is())===u&&(o=null),o!==u&&xc()!==u&&(a=Ra())!==u&&xc()!==u?((s=ka())===u&&(s=null),s!==u&&xc()!==u?(t,i=e,l=s,t=e={index:o,definition:a,keyword:(c=n)&&`${i.toLowerCase()} ${c.toLowerCase()}`||i.toLowerCase(),index_options:l,resource:"index"}):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t}function Ea(){var r,t,e,n;return r=$u,(t=Lc())!==u&&xc()!==u?((e=Ts())===u&&(e=null),e!==u?(r,n=e,r=t={keyword:t.toLowerCase(),constraint:n}):($u=r,r=u)):($u=r,r=u),r}function ha(){var t,e,n,o,a,s,i,c,l,f;return t=$u,(e=function(){var t,e,n,o;t=$u,"references"===r.substr($u,10).toLowerCase()?(e=r.substr($u,10),$u+=10):(e=u,0===qu&&zu(vu));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="REFERENCES"):($u=t,t=u)):($u=t,t=u);return t}())!==u&&xc()!==u&&(n=Da())!==u&&xc()!==u&&(o=Ra())!==u&&xc()!==u?("match full"===r.substr($u,10).toLowerCase()?(a=r.substr($u,10),$u+=10):(a=u,0===qu&&zu(D)),a===u&&("match partial"===r.substr($u,13).toLowerCase()?(a=r.substr($u,13),$u+=13):(a=u,0===qu&&zu(P)),a===u&&("match simple"===r.substr($u,12).toLowerCase()?(a=r.substr($u,12),$u+=12):(a=u,0===qu&&zu(G)))),a===u&&(a=null),a!==u&&xc()!==u?((s=Ca())===u&&(s=null),s!==u&&xc()!==u?((i=Ca())===u&&(i=null),i!==u?(t,c=a,l=s,f=i,t=e={definition:o,table:n,keyword:e.toLowerCase(),match:c&&c.toLowerCase(),on_action:[l,f].filter(r=>r)}):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t}function Ca(){var t,e,n,o;return t=$u,Li()!==u&&xc()!==u?((e=ci())===u&&(e=ai()),e!==u&&xc()!==u&&(n=function(){var t,e,n;t=$u,(e=lc())!==u&&xc()!==u&&Tc()!==u&&xc()!==u?((n=es())===u&&(n=null),n!==u&&xc()!==u&&Sc()!==u?(t,t=e={type:"function",name:e,args:n}):($u=t,t=u)):($u=t,t=u);t===u&&(t=$u,"restrict"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(F)),e===u&&("cascade"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(H)),e===u&&("set null"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(B)),e===u&&("no action"===r.substr($u,9).toLowerCase()?(e=r.substr($u,9),$u+=9):(e=u,0===qu&&zu(Y)),e===u&&("set default"===r.substr($u,11).toLowerCase()?(e=r.substr($u,11),$u+=11):(e=u,0===qu&&zu($)),e===u&&(e=lc()))))),e!==u&&(t,e={type:"origin",value:e.toLowerCase()}),t=e);return t}())!==u?(t,o=n,t={type:"on "+e[0].toLowerCase(),value:o}):($u=t,t=u)):($u=t,t=u),t}function La(){var t,e,n,o,a,s,i;return t=$u,(e=li())===u&&(e=ci())===u&&(e=ic()),e!==u&&(t,i=e,e={keyword:Array.isArray(i)?i[0].toLowerCase():i.toLowerCase()}),(t=e)===u&&(t=$u,(e=ai())!==u&&xc()!==u?(n=$u,"of"===r.substr($u,2).toLowerCase()?(o=r.substr($u,2),$u+=2):(o=u,0===qu&&zu(z)),o!==u&&(a=xc())!==u&&(s=Va())!==u?n=o=[o,a,s]:($u=n,n=u),n===u&&(n=null),n!==u?(t,t=e=function(r,t){return{keyword:r&&r[0]&&r[0].toLowerCase(),args:t&&{keyword:t[0],columns:t[2]}||null}}(e,n)):($u=t,t=u)):($u=t,t=u)),t}function wa(){var t,e,n,o,a,s,i,c,l;return t=$u,(e=ni())===u&&(e=null),e!==u&&xc()!==u?((n=function(){var t,e,n;return t=$u,"character"===r.substr($u,9).toLowerCase()?(e=r.substr($u,9),$u+=9):(e=u,0===qu&&zu(ir)),e!==u&&xc()!==u?("set"===r.substr($u,3).toLowerCase()?(n=r.substr($u,3),$u+=3):(n=u,0===qu&&zu(cr)),n!==u?(t,t=e="CHARACTER SET"):($u=t,t=u)):($u=t,t=u),t}())===u&&("charset"===r.substr($u,7).toLowerCase()?(n=r.substr($u,7),$u+=7):(n=u,0===qu&&zu(lr)),n===u&&("collate"===r.substr($u,7).toLowerCase()?(n=r.substr($u,7),$u+=7):(n=u,0===qu&&zu(fr)))),n!==u&&xc()!==u?((o=bc())===u&&(o=null),o!==u&&xc()!==u&&(a=_s())!==u?(t,i=n,c=o,l=a,t=e={keyword:(s=e)&&`${s[0].toLowerCase()} ${i.toLowerCase()}`||i.toLowerCase(),symbol:c,value:l}):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t}function ma(){var t,e,n,o,a,s,i,c,l;return t=$u,"auto_increment"===r.substr($u,14).toLowerCase()?(e=r.substr($u,14),$u+=14):(e=u,0===qu&&zu(y)),e===u&&("avg_row_length"===r.substr($u,14).toLowerCase()?(e=r.substr($u,14),$u+=14):(e=u,0===qu&&zu(pr)),e===u&&("key_block_size"===r.substr($u,14).toLowerCase()?(e=r.substr($u,14),$u+=14):(e=u,0===qu&&zu(br)),e===u&&("max_rows"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(vr)),e===u&&("min_rows"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(dr)),e===u&&("stats_sample_pages"===r.substr($u,18).toLowerCase()?(e=r.substr($u,18),$u+=18):(e=u,0===qu&&zu(yr))))))),e!==u&&xc()!==u?((n=bc())===u&&(n=null),n!==u&&xc()!==u&&(o=Qs())!==u?(t,c=n,l=o,t=e={keyword:e.toLowerCase(),symbol:c,value:l.value}):($u=t,t=u)):($u=t,t=u),t===u&&(t=wa())===u&&(t=$u,(e=Cc())===u&&("connection"===r.substr($u,10).toLowerCase()?(e=r.substr($u,10),$u+=10):(e=u,0===qu&&zu(Er))),e!==u&&xc()!==u?((n=bc())===u&&(n=null),n!==u&&xc()!==u&&(o=Ws())!==u?(t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:`'${e.value}'`}}(e,n,o)):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,"compression"===r.substr($u,11).toLowerCase()?(e=r.substr($u,11),$u+=11):(e=u,0===qu&&zu(hr)),e!==u&&xc()!==u?((n=bc())===u&&(n=null),n!==u&&xc()!==u?(o=$u,39===r.charCodeAt($u)?(a="'",$u++):(a=u,0===qu&&zu(Cr)),a!==u?("zlib"===r.substr($u,4).toLowerCase()?(s=r.substr($u,4),$u+=4):(s=u,0===qu&&zu(Lr)),s===u&&("lz4"===r.substr($u,3).toLowerCase()?(s=r.substr($u,3),$u+=3):(s=u,0===qu&&zu(wr)),s===u&&("none"===r.substr($u,4).toLowerCase()?(s=r.substr($u,4),$u+=4):(s=u,0===qu&&zu(j)))),s!==u?(39===r.charCodeAt($u)?(i="'",$u++):(i=u,0===qu&&zu(Cr)),i!==u?o=a=[a,s,i]:($u=o,o=u)):($u=o,o=u)):($u=o,o=u),o!==u?(t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.join("").toUpperCase()}}(e,n,o)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,"engine"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(mr)),e!==u&&xc()!==u?((n=bc())===u&&(n=null),n!==u&&xc()!==u&&(o=_s())!==u?(t,t=e=function(r,t,e){return{keyword:r.toLowerCase(),symbol:t,value:e.toUpperCase()}}(e,n,o)):($u=t,t=u)):($u=t,t=u)))),t}function Aa(){var t,e,n,o,a,s,i;return(t=Ia())===u&&(t=$u,e=$u,40===r.charCodeAt($u)?(n="(",$u++):(n=u,0===qu&&zu(xr)),n!==u&&(o=xc())!==u&&(a=Aa())!==u&&(s=xc())!==u?(41===r.charCodeAt($u)?(i=")",$u++):(i=u,0===qu&&zu(Ur)),i!==u?e=n=[n,o,a,s,i]:($u=e,e=u)):($u=e,e=u),e!==u&&(t,e={...e[2],parentheses_symbol:!0}),t=e),t}function Ta(){var t,e,n,o,a,s,i,c,l;if(t=$u,Ri()!==u)if(xc()!==u)if((e=Sa())!==u){for(n=[],o=$u,(a=xc())!==u&&(s=mc())!==u&&(i=xc())!==u&&(c=Sa())!==u?o=a=[a,s,i,c]:($u=o,o=u);o!==u;)n.push(o),o=$u,(a=xc())!==u&&(s=mc())!==u&&(i=xc())!==u&&(c=Sa())!==u?o=a=[a,s,i,c]:($u=o,o=u);n!==u?(t,t=nl(e,n)):($u=t,t=u)}else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;return t===u&&(t=$u,xc()!==u&&Ri()!==u&&(e=xc())!==u&&(n=function(){var t,e,n,o;t=$u,"RECURSIVE"===r.substr($u,9)?(e="RECURSIVE",$u+=9):(e=u,0===qu&&zu(Je));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}())!==u&&(o=xc())!==u&&(a=Sa())!==u?(t,(l=a).recursive=!0,t=[l]):($u=t,t=u)),t}function Sa(){var r,t,e,n,o;return r=$u,(t=Ws())===u&&(t=_s()),t!==u&&xc()!==u?((e=Ra())===u&&(e=null),e!==u&&xc()!==u&&hi()!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(n=ua())!==u&&xc()!==u&&Sc()!==u?(r,"string"==typeof(o=t)&&(o={type:"default",value:o}),r=t={name:o,stmt:n,columns:e}):($u=r,r=u)):($u=r,r=u),r}function Ra(){var r,t;return r=$u,Tc()!==u&&xc()!==u&&(t=Va())!==u&&xc()!==u&&Sc()!==u?(r,r=t):($u=r,r=u),r}function Ia(){var t,e,n,o,a,s,i,c,l,f,p,b,v,d,y,E,h,C,L,w,m;return t=$u,xc()!==u?((e=Ta())===u&&(e=null),e!==u&&xc()!==u&&function(){var t,e,n,o;t=$u,"select"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(Ve));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}()!==u&&Uc()!==u?((n=function(){var r,t,e,n,o,a;if(r=$u,(t=Na())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=Na())!==u?n=o=[o,a]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=Na())!==u?n=o=[o,a]:($u=n,n=u);e!==u?(r,t=function(r,t){const e=[r];for(let r=0,n=t.length;r<n;++r)e.push(t[r][1]);return e}(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())===u&&(n=null),n!==u&&xc()!==u?((o=gi())===u&&(o=null),o!==u&&xc()!==u&&(a=_a())!==u&&xc()!==u?((s=ja())===u&&(s=null),s!==u&&xc()!==u?((i=$a())===u&&(i=null),i!==u&&xc()!==u?((c=function(){var t,e,n;t=$u,(e=function(){var t,e,n,o;t=$u,"group"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Rn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}())!==u&&xc()!==u&&Ii()!==u&&xc()!==u&&(n=es())!==u?(t,e=n.value,t=e):($u=t,t=u);return t}())===u&&(c=null),c!==u&&xc()!==u?((l=function(){var t,e;t=$u,function(){var t,e,n,o;t=$u,"having"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(_n));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}()!==u&&xc()!==u&&(e=cs())!==u?(t,t=e):($u=t,t=u);return t}())===u&&(l=null),l!==u&&xc()!==u?((f=Wa())===u&&(f=null),f!==u&&xc()!==u?((p=Ka())===u&&(p=null),p!==u?(t,b=e,v=n,d=o,y=a,h=i,C=c,L=l,w=f,m=p,(E=s)&&E.forEach(r=>r.table&&cl.add(`select::${r.db}::${r.table}`)),t={with:b,type:"select",options:v,distinct:d,columns:y,from:E,where:h,groupby:C,having:L,orderby:w,limit:m}):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t}function Na(){var t,e;return t=$u,(e=function(){var t;"sql_calc_found_rows"===r.substr($u,19).toLowerCase()?(t=r.substr($u,19),$u+=19):(t=u,0===qu&&zu(du));return t}())===u&&((e=function(){var t;"sql_cache"===r.substr($u,9).toLowerCase()?(t=r.substr($u,9),$u+=9):(t=u,0===qu&&zu(yu));return t}())===u&&(e=function(){var t;"sql_no_cache"===r.substr($u,12).toLowerCase()?(t=r.substr($u,12),$u+=12):(t=u,0===qu&&zu(Eu));return t}()),e===u&&(e=function(){var t;"sql_big_result"===r.substr($u,14).toLowerCase()?(t=r.substr($u,14),$u+=14):(t=u,0===qu&&zu(Cu));return t}())===u&&(e=function(){var t;"sql_small_result"===r.substr($u,16).toLowerCase()?(t=r.substr($u,16),$u+=16):(t=u,0===qu&&zu(hu));return t}())===u&&(e=function(){var t;"sql_buffer_result"===r.substr($u,17).toLowerCase()?(t=r.substr($u,17),$u+=17):(t=u,0===qu&&zu(Lu));return t}())),e!==u&&(t,e=e),t=e}function _a(){var r,t,e,n,o,a,s,i;if(r=$u,(t=Oi())===u&&(t=$u,(e=Ac())!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t===u&&(t=Ac())),t!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Oa())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Oa())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,r=t=function(r,t){ll.add("select::null::(.*)");const e={expr:{type:"column_ref",table:null,column:"*"},as:null};return t&&t.length>0?nl(e,t):[e]}(0,e)):($u=r,r=u)}else $u=r,r=u;if(r===u)if(r=$u,(t=Oa())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Oa())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Oa())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,r=t=nl(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function Oa(){var r,t,e,n,o;return r=$u,(t=is())!==u&&(e=pc())!==u&&(n=Jc())!==u?(r,r=t={type:"cast",expr:t,symbol:"::",target:n}):($u=r,r=u),r===u&&(r=$u,t=$u,(e=Ts())!==u&&(n=xc())!==u&&(o=wc())!==u?t=e=[e,n,o]:($u=t,t=u),t===u&&(t=null),t!==u&&(e=xc())!==u&&(n=Ac())!==u?(r,r=t=function(r){const t=r&&r[0]||null;return ll.add(`select::${t}::(.*)`),{expr:{type:"column_ref",table:t,column:"*"},as:null}}(t)):($u=r,r=u),r===u&&(r=$u,(t=is())!==u&&(e=xc())!==u?((n=ga())===u&&(n=null),n!==u?(r,r=t=function(r,t){return{type:"expr",expr:r,as:t}}(t,n)):($u=r,r=u)):($u=r,r=u))),r}function ga(){var r,t,e;return r=$u,(t=hi())!==u&&xc()!==u&&(e=function(){var r,t;r=$u,(t=_s())!==u?($u,(function(r){if(!0===zc[r.toUpperCase()])throw new Error("Error: "+JSON.stringify(r)+" is a reserved word, can not as alias clause");return!1}(t)?u:void 0)!==u?(r,r=t=t):($u=r,r=u)):($u=r,r=u);r===u&&(r=$u,(t=Ss())!==u&&(r,t=t),r=t);return r}())!==u?(r,r=t=e):($u=r,r=u),r===u&&(r=$u,(t=hi())===u&&(t=null),t!==u&&xc()!==u&&(e=Ts())!==u?(r,r=t=e):($u=r,r=u)),r}function ja(){var r,t;return r=$u,yi()!==u&&xc()!==u&&(t=Da())!==u?(r,r=t):($u=r,r=u),r}function xa(){var r,t,e;return r=$u,(t=Ha())!==u&&xc()!==u&&oi()!==u&&xc()!==u&&(e=Ha())!==u?(r,r=t=[t,e]):($u=r,r=u),r}function Ua(){var t,e;return t=$u,Si()!==u&&xc()!==u?("btree"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(kr)),e===u&&("hash"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Mr)),e===u&&("gist"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Dr)),e===u&&("gin"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Pr))))),e!==u?(t,t={keyword:"using",type:e.toLowerCase()}):($u=t,t=u)):($u=t,t=u),t}function ka(){var r,t,e,n,o,a;if(r=$u,(t=Ma())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=Ma())!==u?n=o=[o,a]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=Ma())!==u?n=o=[o,a]:($u=n,n=u);e!==u?(r,r=t=function(r,t){const e=[r];for(let r=0;r<t.length;r++)e.push(t[r][1]);return e}(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function Ma(){var t,e,n,o,a,s;return t=$u,(e=function(){var t,e,n,o;t=$u,"key_block_size"===r.substr($u,14).toLowerCase()?(e=r.substr($u,14),$u+=14):(e=u,0===qu&&zu(br));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="KEY_BLOCK_SIZE"):($u=t,t=u)):($u=t,t=u);return t}())!==u&&xc()!==u?((n=bc())===u&&(n=null),n!==u&&xc()!==u&&(o=Qs())!==u?(t,a=n,s=o,t=e={type:e.toLowerCase(),symbol:a,expr:s}):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,(e=_s())!==u&&xc()!==u&&(n=bc())!==u&&xc()!==u?((o=Qs())===u&&(o=Ts()),o!==u?(t,t=e=function(r,t,e){return{type:r.toLowerCase(),symbol:t,expr:"string"==typeof e&&{type:"origin",value:e}||e}}(e,n,o)):($u=t,t=u)):($u=t,t=u),t===u&&(t=Ua())===u&&(t=$u,"with"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Gr)),e!==u&&xc()!==u?("parser"===r.substr($u,6).toLowerCase()?(n=r.substr($u,6),$u+=6):(n=u,0===qu&&zu(Fr)),n!==u&&xc()!==u&&(o=_s())!==u?(t,t=e={type:"with parser",expr:o}):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,"visible"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Hr)),e===u&&("invisible"===r.substr($u,9).toLowerCase()?(e=r.substr($u,9),$u+=9):(e=u,0===qu&&zu(Br))),e!==u&&(t,e=function(r){return{type:r.toLowerCase(),expr:r.toLowerCase()}}(e)),(t=e)===u&&(t=Mc())))),t}function Da(){var r,t,e,n;if(r=$u,(t=Ga())!==u){for(e=[],n=Pa();n!==u;)e.push(n),n=Pa();e!==u?(r,r=t=Yr(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function Pa(){var r,t,e;return r=$u,xc()!==u&&(t=mc())!==u&&xc()!==u&&(e=Ga())!==u?(r,r=e):($u=r,r=u),r===u&&(r=$u,xc()!==u&&(t=function(){var r,t,e,n,o,a,s,i,c,l,f;if(r=$u,(t=Fa())!==u)if(xc()!==u)if((e=Ga())!==u)if(xc()!==u)if((n=Si())!==u)if(xc()!==u)if(Tc()!==u)if(xc()!==u)if((o=_s())!==u){for(a=[],s=$u,(i=xc())!==u&&(c=mc())!==u&&(l=xc())!==u&&(f=_s())!==u?s=i=[i,c,l,f]:($u=s,s=u);s!==u;)a.push(s),s=$u,(i=xc())!==u&&(c=mc())!==u&&(l=xc())!==u&&(f=_s())!==u?s=i=[i,c,l,f]:($u=s,s=u);a!==u&&(s=xc())!==u&&(i=Sc())!==u?(r,p=t,v=o,d=a,(b=e).join=p,b.using=nl(v,d),r=t=b):($u=r,r=u)}else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;var p,b,v,d;r===u&&(r=$u,(t=Fa())!==u&&xc()!==u&&(e=Ga())!==u&&xc()!==u?((n=Ya())===u&&(n=null),n!==u?(r,t=function(r,t,e){return t.join=r,t.on=e,t}(t,e,n),r=t):($u=r,r=u)):($u=r,r=u),r===u&&(r=$u,(t=Fa())!==u&&xc()!==u&&(e=Tc())!==u&&xc()!==u&&(n=ua())!==u&&xc()!==u&&Sc()!==u&&xc()!==u?((o=ga())===u&&(o=null),o!==u&&(a=xc())!==u?((s=Ya())===u&&(s=null),s!==u?(r,t=function(r,t,e,n){return t.parentheses=!0,{expr:t,as:e,join:r,on:n}}(t,n,o,s),r=t):($u=r,r=u)):($u=r,r=u)):($u=r,r=u)));return r}())!==u?(r,r=t):($u=r,r=u)),r}function Ga(){var t,e,n,o,a,s,i,c,l,f,p;return t=$u,(e=function(){var t;"dual"===r.substr($u,4).toLowerCase()?(t=r.substr($u,4),$u+=4):(t=u,0===qu&&zu(uu));return t}())!==u&&(t,e={type:"dual"}),(t=e)===u&&(t=$u,(e=Ha())!==u&&xc()!==u?((n=ga())===u&&(n=null),n!==u?(t,p=n,t=e="var"===(f=e).type?(f.as=p,f):{db:f.db,table:f.table,as:p}):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,(e=Tc())!==u&&xc()!==u&&(n=ua())!==u&&xc()!==u&&Sc()!==u&&xc()!==u?((o=ga())===u&&(o=null),o!==u?(t,t=e=function(r,t){return r.parentheses=!0,{expr:r,as:t}}(n,o)):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,(e=Ci())!==u&&xc()!==u&&(n=Tc())!==u&&xc()!==u&&function(){var t,e,n,o;t=$u,"tumble"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(zn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="TUMBLE"):($u=t,t=u)):($u=t,t=u);return t}()!==u&&xc()!==u&&(o=Tc())!==u&&xc()!==u&&Ci()!==u&&xc()!==u&&(a=Ha())!==u&&xc()!==u&&mc()!==u&&xc()!==u?("descriptor"===r.substr($u,10).toLowerCase()?(s=r.substr($u,10),$u+=10):(s=u,0===qu&&zu($r)),s!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(i=As())!==u&&xc()!==u&&Sc()!==u&&xc()!==u&&mc()!==u&&xc()!==u&&(c=ns())!==u&&xc()!==u&&Sc()!==u&&xc()!==u&&Sc()!==u&&xc()!==u?((l=ga())===u&&(l=null),l!==u?(t,t=e=function(r,t,e,n){return{expr:{type:"tumble",data:r,timecol:t,size:e},as:n}}(a,i,c,l)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)))),t}function Fa(){var t,e,n,o,a,s;return t=$u,(e=function(){var t,e,n,o;t=$u,"natural"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(fn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="NATURAL"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(e=null),e!==u&&(n=xc())!==u?((o=function(){var t,e,n,o;t=$u,"left"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(pn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="LEFT"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(o=function(){var t,e,n,o;t=$u,"right"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(bn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="RIGHT"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(o=function(){var t,e,n,o;t=$u,"full"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(vn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="FULL"):($u=t,t=u)):($u=t,t=u);return t}()),o===u&&(o=null),o!==u&&xc()!==u?((a=Ai())===u&&(a=null),a!==u&&xc()!==u&&wi()!==u?(t,t=e=`${e?"NATURAL ":""}${(s=o)?s+" ":""}${a?"OUTER ":""}JOIN`):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,e=$u,(n=function(){var t,e,n,o;t=$u,"inner"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(dn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="INNER"):($u=t,t=u)):($u=t,t=u);return t}())!==u&&(o=xc())!==u?e=n=[n,o]:($u=e,e=u),e===u&&(e=null),e!==u&&(n=wi())!==u?(t,t=e=e?"INNER JOIN":"JOIN"):($u=t,t=u),t===u&&(t=$u,(e=mi())!==u&&(n=xc())!==u&&(o=wi())!==u?(t,t=e="CROSS JOIN"):($u=t,t=u),t===u&&(t=$u,(e=mi())===u&&(e=Ai()),e!==u&&(n=xc())!==u&&(o=function(){var t,e,n,o;t=$u,"apply"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(hn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}())!==u?(t,t=e=e[0].toUpperCase()+" APPLY"):($u=t,t=u)))),t}function Ha(){var r,t,e,n,o,a,s,i,c,l;return r=$u,(t=Ts())!==u?(e=$u,(n=xc())!==u&&(o=wc())!==u&&(a=xc())!==u&&(s=Ts())!==u?e=n=[n,o,a,s]:($u=e,e=u),e!==u?(n=$u,(o=xc())!==u&&(a=wc())!==u&&(s=xc())!==u&&(i=Ts())!==u?n=o=[o,a,s,i]:($u=n,n=u),n!==u?(r,r=t=function(r,t,e){const n={db:null,table:r};return null!==e&&(n.db=`${r}.${t[3]}`,n.table=e[3]),n}(t,e,n)):($u=r,r=u)):($u=r,r=u)):($u=r,r=u),r===u&&(r=$u,(t=Ts())!==u&&(e=xc())!==u&&(n=wc())!==u&&(o=xc())!==u&&(a=Ac())!==u?(r,l=t,cl.add(`select::${l}::(.*)`),r=t={db:l,table:"*"}):($u=r,r=u),r===u&&(r=$u,(t=Ts())!==u?(e=$u,(n=xc())!==u&&(o=wc())!==u&&(a=xc())!==u&&(s=Ts())!==u?e=n=[n,o,a,s]:($u=e,e=u),e===u&&(e=null),e!==u?(r,r=t=function(r,t){const e={db:null,table:r};return null!==t&&(e.db=r,e.table=t[3]),e}(t,e)):($u=r,r=u)):($u=r,r=u),r===u&&(r=$u,(t=Kc())!==u&&(r,(c=t).db=null,c.table=c.name,t=c),r=t))),r}function Ba(){var r,t,e,n,o,a,s,i;if(r=$u,(t=ss())!==u){for(e=[],n=$u,(o=xc())!==u?((a=Gi())===u&&(a=Fi()),a!==u&&(s=xc())!==u&&(i=ss())!==u?n=o=[o,a,s,i]:($u=n,n=u)):($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u?((a=Gi())===u&&(a=Fi()),a!==u&&(s=xc())!==u&&(i=ss())!==u?n=o=[o,a,s,i]:($u=n,n=u)):($u=n,n=u);e!==u?(r,r=t=function(r,t){const e=t.length;let n=r;for(let r=0;r<e;++r)n=tl(t[r][1],n,t[r][3]);return n}(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function Ya(){var r,t;return r=$u,Li()!==u&&xc()!==u&&(t=cs())!==u?(r,r=t):($u=r,r=u),r}function $a(){var t,e;return t=$u,function(){var t,e,n,o;t=$u,"where"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Sn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}()!==u&&xc()!==u?((e=cs())===u&&(e=ss()),e!==u?(t,t=e):($u=t,t=u)):($u=t,t=u),t}function Va(){var r,t,e,n,o,a,s,i;if(r=$u,(t=As())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=As())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=As())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,r=t=nl(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function Wa(){var t,e;return t=$u,function(){var t,e,n,o;t=$u,"order"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Nn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}()!==u&&xc()!==u&&Ii()!==u&&xc()!==u&&(e=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=Xa())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Xa())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Xa())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=nl(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())!==u?(t,t=e):($u=t,t=u),t}function Xa(){var r,t,e;return r=$u,(t=ss())!==u&&xc()!==u?((e=_i())===u&&(e=Ni()),e===u&&(e=null),e!==u?(r,r=t={expr:t,type:e}):($u=r,r=u)):($u=r,r=u),r}function qa(){var r;return(r=Qs())===u&&(r=xs()),r}function Ka(){var t,e,n,o,a,s;return t=$u,function(){var t,e,n,o;t=$u,"limit"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(On));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}()!==u&&xc()!==u?((e=qa())===u&&(e=Oi()),e!==u&&xc()!==u?(n=$u,(o=function(){var t,e,n,o;t=$u,"offset"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(gn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="OFFSET"):($u=t,t=u)):($u=t,t=u);return t}())!==u&&(a=xc())!==u&&(s=qa())!==u?n=o=[o,a,s]:($u=n,n=u),n===u&&(n=null),n!==u?(t,t=function(r,t){const e=[];return"string"==typeof r?e.push({type:"origin",value:"all"}):e.push(r),t&&e.push(t[2]),{seperator:t&&t[0]&&t[0].toLowerCase()||"",value:e}}(e,n)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t}function Qa(){var t,e,n,o,a,s,i,c,l;return t=$u,e=$u,(n=Ts())!==u&&(o=xc())!==u&&(a=wc())!==u?e=n=[n,o,a]:($u=e,e=u),e===u&&(e=null),e!==u&&(n=xc())!==u&&(o=Rs())!==u&&(a=xc())!==u?(61===r.charCodeAt($u)?(s="=",$u++):(s=u,0===qu&&zu(Vr)),s!==u&&xc()!==u&&(i=hs())!==u?(t,t=e={column:o,value:i,table:(l=e)&&l[0]}):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,e=$u,(n=Ts())!==u&&(o=xc())!==u&&(a=wc())!==u?e=n=[n,o,a]:($u=e,e=u),e===u&&(e=null),e!==u&&(n=xc())!==u&&(o=Rs())!==u&&(a=xc())!==u?(61===r.charCodeAt($u)?(s="=",$u++):(s=u,0===qu&&zu(Vr)),s!==u&&xc()!==u&&(i=Ti())!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(c=As())!==u&&xc()!==u&&Sc()!==u?(t,t=e=function(r,t,e){return{column:t,value:e,table:r&&r[0],keyword:"values"}}(e,o,c)):($u=t,t=u)):($u=t,t=u)),t}function Ja(){var t,e,n,o,a;return t=$u,(e=function(){var t,e,n,o;t=$u,"returning"===r.substr($u,9).toLowerCase()?(e=r.substr($u,9),$u+=9):(e=u,0===qu&&zu(ze));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="RETURNING"):($u=t,t=u)):($u=t,t=u);return t}())!==u&&xc()!==u?((n=Ac())===u&&(n=Va()),n!==u?(t,a=n,t=e={type:(o=e)&&o.toLowerCase()||"returning",columns:"*"===a&&[{type:"columne_ref",table:null,column:"*"}]||a}):($u=t,t=u)):($u=t,t=u),t}function Za(){var r;return(r=function(){var r,t;r=$u,Ti()!==u&&xc()!==u&&(t=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=ts())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=ts())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=ts())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=nl(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())!==u?(r,r=t):($u=r,r=u);return r}())===u&&(r=Ia()),r}function za(){var r,t,e,n,o,a,s,i,c;if(r=$u,vi()!==u)if(xc()!==u)if((t=Tc())!==u)if(xc()!==u)if((e=_s())!==u){for(n=[],o=$u,(a=xc())!==u&&(s=mc())!==u&&(i=xc())!==u&&(c=_s())!==u?o=a=[a,s,i,c]:($u=o,o=u);o!==u;)n.push(o),o=$u,(a=xc())!==u&&(s=mc())!==u&&(i=xc())!==u&&(c=_s())!==u?o=a=[a,s,i,c]:($u=o,o=u);n!==u&&(o=xc())!==u&&(a=Sc())!==u?(r,r=nl(e,n)):($u=r,r=u)}else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;return r===u&&(r=$u,vi()!==u&&xc()!==u&&(t=ts())!==u?(r,r=t):($u=r,r=u)),r}function rs(){var r,t;return r=$u,(t=li())!==u&&(r,t="insert"),(r=t)===u&&(r=$u,(t=fi())!==u&&(r,t="replace"),r=t),r}function ts(){var r,t;return r=$u,Tc()!==u&&xc()!==u&&(t=es())!==u&&xc()!==u&&Sc()!==u?(r,r=t):($u=r,r=u),r}function es(){var r,t,e,n,o,a,s,i;if(r=$u,(t=ss())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=ss())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=ss())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,r=t=function(r,t){const e={type:"expr_list"};return e.value=nl(r,t),e}(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function ns(){var t,e,n;return t=$u,cc()!==u&&xc()!==u&&(e=ss())!==u&&xc()!==u&&(n=function(){var t,e;(t=function(){var t,e,n,o;t=$u,"year"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(se));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="YEAR"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"month"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(re));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="MONTH"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"day"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Ht));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="DAY"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"hour"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Xt));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="HOUR"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"minute"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(zt));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="MINUTE"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"second"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(ee));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SECOND"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=$u,"years"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(ku)),e===u&&("months"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(Mu)),e===u&&("days"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Du)),e===u&&("hours"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Pu)),e===u&&("minutes"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Gu)),e===u&&("seconds"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Fu))))))),e!==u&&(t,e=e.toUpperCase()),t=e);return t}())!==u?(t,t={type:"interval",expr:e,unit:n.toLowerCase()}):($u=t,t=u),t===u&&(t=$u,cc()!==u&&xc()!==u&&(e=Ws())!==u?(t,t=function(r){return{type:"interval",expr:r,unit:""}}(e)):($u=t,t=u)),t}function os(){var t,e,n,o,a,s,i,c;return t=$u,Bi()!==u&&xc()!==u?((e=ss())===u&&(e=null),e!==u&&xc()!==u&&(n=function(){var r,t,e,n,o,a;if(r=$u,(t=us())!==u)if(xc()!==u){for(e=[],n=$u,(o=xc())!==u&&(a=us())!==u?n=o=[o,a]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=us())!==u?n=o=[o,a]:($u=n,n=u);e!==u?(r,t=p(t,e),r=t):($u=r,r=u)}else $u=r,r=u;else $u=r,r=u;return r}())!==u&&xc()!==u?((o=function(){var t,e;t=$u,function(){var t,e,n,o;t=$u,"else"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(uo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}()!==u&&xc()!==u&&(e=ss())!==u?(t,t={type:"else",result:e}):($u=t,t=u);return t}())===u&&(o=null),o!==u&&xc()!==u&&function(){var t,e,n,o;t=$u,"end"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(ao));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}()!==u&&xc()!==u?((a=Bi())===u&&(a=null),a!==u?(t,s=e,i=n,(c=o)&&i.push(c),t={type:"case",expr:s||null,args:i}):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t}function us(){var t,e,n;return t=$u,Yi()!==u&&xc()!==u&&(e=cs())!==u&&xc()!==u&&function(){var t,e,n,o;t=$u,"then"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(oo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}()!==u&&xc()!==u&&(n=ss())!==u?(t,t={type:"when",cond:e,result:n}):($u=t,t=u),t}function as(){var r;return(r=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=ms())!==u){if(e=[],n=$u,(o=xc())!==u&&(a=jc())!==u&&(s=xc())!==u&&(i=ms())!==u?n=o=[o,a,s,i]:($u=n,n=u),n!==u)for(;n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=jc())!==u&&(s=xc())!==u&&(i=ms())!==u?n=o=[o,a,s,i]:($u=n,n=u);else e=u;e!==u&&(n=xc())!==u?((o=vs())===u&&(o=null),o!==u?(r,t=function(r,t,e){const n=ol(r,t);return null===e?n:"arithmetic"===e.type?ol(n,e.tail):tl(e.op,n,e.right)}(t,e,o),r=t):($u=r,r=u)):($u=r,r=u)}else $u=r,r=u;return r}())===u&&(r=function(){var r,t,e,n,o,a,s,i;if(r=$u,(t=ls())!==u){for(e=[],n=$u,(o=Uc())!==u&&(a=Fi())!==u&&(s=xc())!==u&&(i=ls())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=Uc())!==u&&(a=Fi())!==u&&(s=xc())!==u&&(i=ls())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,t=Wr(t,e),r=t):($u=r,r=u)}else $u=r,r=u;return r}())===u&&(r=function(){var r,t,e,n,o,a;if(r=$u,(t=Cs())!==u){if(e=[],n=$u,(o=xc())!==u&&(a=ms())!==u?n=o=[o,a]:($u=n,n=u),n!==u)for(;n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=ms())!==u?n=o=[o,a]:($u=n,n=u);else e=u;e!==u?(r,t=rl(t,e[0][1]),r=t):($u=r,r=u)}else $u=r,r=u;return r}()),r}function ss(){var r;return(r=as())===u&&(r=ua()),r}function is(){var r,t,e,n,o,a,s,i;if(r=$u,(t=ss())!==u){for(e=[],n=$u,(o=xc())!==u?((a=Gi())===u&&(a=Fi())===u&&(a=jc()),a!==u&&(s=xc())!==u&&(i=ss())!==u?n=o=[o,a,s,i]:($u=n,n=u)):($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u?((a=Gi())===u&&(a=Fi())===u&&(a=jc()),a!==u&&(s=xc())!==u&&(i=ss())!==u?n=o=[o,a,s,i]:($u=n,n=u)):($u=n,n=u);e!==u?(r,r=t=function(r,t){const e=r.ast;if(e&&"select"===e.type&&(!(r.parentheses_symbol||r.parentheses||r.ast.parentheses||r.ast.parentheses_symbol)||1!==e.columns.length||"*"===e.columns[0].expr.column))throw new Error("invalid column clause with select statement");if(!t||0===t.length)return r;const n=t.length;let o=t[n-1][3];for(let e=n-1;e>=0;e--){const n=0===e?r:t[e-1][3];o=tl(t[e][1],n,o)}return o}(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function cs(){var r,t,e,n,o,a,s,i;if(r=$u,(t=ss())!==u){for(e=[],n=$u,(o=xc())!==u?((a=Gi())===u&&(a=Fi())===u&&(a=mc()),a!==u&&(s=xc())!==u&&(i=ss())!==u?n=o=[o,a,s,i]:($u=n,n=u)):($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u?((a=Gi())===u&&(a=Fi())===u&&(a=mc()),a!==u&&(s=xc())!==u&&(i=ss())!==u?n=o=[o,a,s,i]:($u=n,n=u)):($u=n,n=u);e!==u?(r,r=t=function(r,t){const e=t.length;let n=r,o="";for(let r=0;r<e;++r)","===t[r][1]?(o=",",Array.isArray(n)||(n=[n]),n.push(t[r][3])):n=tl(t[r][1],n,t[r][3]);if(","===o){const r={type:"expr_list"};return r.value=n,r}return n}(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function ls(){var r,t,e,n,o,a,s,i;if(r=$u,(t=fs())!==u){for(e=[],n=$u,(o=Uc())!==u&&(a=Gi())!==u&&(s=xc())!==u&&(i=fs())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=Uc())!==u&&(a=Gi())!==u&&(s=xc())!==u&&(i=fs())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,r=t=Wr(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function fs(){var t,e,n,o,a;return(t=ps())===u&&(t=function(){var r,t,e;r=$u,(t=bs())!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(e=ua())!==u&&xc()!==u&&Sc()!==u?(r,n=t,(o=e).parentheses=!0,t=rl(n,o),r=t):($u=r,r=u);var n,o;return r}())===u&&(t=$u,(e=Pi())===u&&(e=$u,33===r.charCodeAt($u)?(n="!",$u++):(n=u,0===qu&&zu(Xr)),n!==u?(o=$u,qu++,61===r.charCodeAt($u)?(a="=",$u++):(a=u,0===qu&&zu(Vr)),qu--,a===u?o=void 0:($u=o,o=u),o!==u?e=n=[n,o]:($u=e,e=u)):($u=e,e=u)),e!==u&&(n=xc())!==u&&(o=fs())!==u?(t,t=e=rl("NOT",o)):($u=t,t=u)),t}function ps(){var r,t,e,n,o;return r=$u,(t=hs())!==u&&xc()!==u?((e=vs())===u&&(e=null),e!==u?(r,n=t,r=t=null===(o=e)?n:"arithmetic"===o.type?ol(n,o.tail):tl(o.op,n,o.right)):($u=r,r=u)):($u=r,r=u),r===u&&(r=Ws())===u&&(r=As()),r}function bs(){var r,t,e,n,o,a;return r=$u,t=$u,(e=Pi())!==u&&(n=xc())!==u&&(o=Di())!==u?t=e=[e,n,o]:($u=t,t=u),t!==u&&(r,t=(a=t)[0]+" "+a[2]),(r=t)===u&&(r=Di()),r}function vs(){var t;return(t=function(){var r,t,e,n,o,a,s;r=$u,t=[],e=$u,(n=xc())!==u&&(o=ds())!==u&&(a=xc())!==u&&(s=hs())!==u?e=n=[n,o,a,s]:($u=e,e=u);if(e!==u)for(;e!==u;)t.push(e),e=$u,(n=xc())!==u&&(o=ds())!==u&&(a=xc())!==u&&(s=hs())!==u?e=n=[n,o,a,s]:($u=e,e=u);else t=u;t!==u&&(r,t={type:"arithmetic",tail:t});return r=t}())===u&&(t=function(){var r,t,e,n;r=$u,(t=Es())!==u&&xc()!==u&&(e=Tc())!==u&&xc()!==u&&(n=es())!==u&&xc()!==u&&Sc()!==u?(r,r=t={op:t,right:n}):($u=r,r=u);r===u&&(r=$u,(t=Es())!==u&&xc()!==u?((e=Kc())===u&&(e=Ws()),e!==u?(r,t=function(r,t){return{op:r,right:t}}(t,e),r=t):($u=r,r=u)):($u=r,r=u));return r}())===u&&(t=function(){var r,t,e;r=$u,(t=bs())!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(e=es())!==u&&xc()!==u&&Sc()!==u?(r,r=t={op:t,right:e}):($u=r,r=u);return r}())===u&&(t=function(){var r,t,e,n;r=$u,(t=function(){var r,t,e,n,o;r=$u,t=$u,(e=Pi())!==u&&(n=xc())!==u&&(o=ji())!==u?t=e=[e,n,o]:($u=t,t=u);t!==u&&(r,t=(a=t)[0]+" "+a[2]);var a;(r=t)===u&&(r=ji());return r}())!==u&&xc()!==u&&(e=hs())!==u&&xc()!==u&&Gi()!==u&&xc()!==u&&(n=hs())!==u?(r,r=t={op:t,right:{type:"expr_list",value:[e,n]}}):($u=r,r=u);return r}())===u&&(t=function(){var r,t,e;r=$u,(t=function(){var r;r=$u,Ui()!==u&&xc()!==u&&Pi()!==u&&xc()!==u&&gi()!==u&&xc()!==u&&yi()!==u?(r,r="IS NOT DISTINCT FROM"):($u=r,r=u);r===u&&(r=$u,Ui()!==u&&xc()!==u&&gi()!==u&&xc()!==u&&yi()!==u?(r,r="IS DISTINCT FROM"):($u=r,r=u));return r}())!==u&&xc()!==u&&(e=ss())!==u?(r,r=t={op:t,right:e}):($u=r,r=u);return r}())===u&&(t=function(){var r,t,e,n,o,a,s,i,c;r=$u,(t=Ui())!==u&&(e=xc())!==u&&(n=hs())!==u?(r,r=t={op:"IS",right:n}):($u=r,r=u);r===u&&(r=$u,(t=Ui())!==u&&(e=xc())!==u?(n=$u,(o=gi())!==u&&(a=xc())!==u&&(s=yi())!==u&&(i=xc())!==u&&(c=Ha())!==u?n=o=[o,a,s,i,c]:($u=n,n=u),n!==u?(r,t=function(r){const{db:t,table:e}=r.pop(),n="*"===e?"*":`"${e}"`;return{op:"IS",right:{type:"origin",value:"DISTINCT FROM "+(t?`"${t}".${n}`:n)}}}(n),r=t):($u=r,r=u)):($u=r,r=u),r===u&&(r=$u,t=$u,(e=Ui())!==u&&(n=xc())!==u&&(o=Pi())!==u?t=e=[e,n,o]:($u=t,t=u),t!==u&&(e=xc())!==u&&(n=hs())!==u?(r,t=function(r){return{op:"IS NOT",right:r}}(n),r=t):($u=r,r=u)));return r}())===u&&(t=function(){var r,t,e,n;r=$u,(t=function(){var r,t,e,n,o;r=$u,t=$u,(e=Pi())!==u&&(n=xc())!==u&&(o=ki())!==u?t=e=[e,n,o]:($u=t,t=u);t!==u&&(r,t=(a=t)[0]+" "+a[2]);var a;(r=t)===u&&(r=ki());return r}())!==u&&xc()!==u?((e=$s())===u&&(e=ps()),e!==u&&xc()!==u?((n=ys())===u&&(n=null),n!==u?(r,o=t,a=e,(s=n)&&(a.escape=s),r=t={op:o,right:a}):($u=r,r=u)):($u=r,r=u)):($u=r,r=u);var o,a,s;return r}())===u&&(t=function(){var r,t,e,n;r=$u,(t=function(){var r,t,e,n,o,a,s;r=$u,t=$u,(e=Pi())!==u&&(n=xc())!==u&&(o=Mi())!==u&&(a=xc())!==u&&(s=oi())!==u?t=e=[e,n,o,a,s]:($u=t,t=u);t!==u&&(r,t="NOT SIMILAR TO");(r=t)===u&&(r=$u,(t=Mi())!==u&&(e=xc())!==u&&(n=oi())!==u?(r,r=t="SIMILAR TO"):($u=r,r=u));return r}())!==u&&xc()!==u?((e=$s())===u&&(e=ps()),e!==u&&xc()!==u?((n=ys())===u&&(n=null),n!==u?(r,o=t,a=e,(s=n)&&(a.escape=s),r=t={op:o,right:a}):($u=r,r=u)):($u=r,r=u)):($u=r,r=u);var o,a,s;return r}())===u&&(t=function(){var t,e,n;t=$u,"@>"===r.substr($u,2)?(e="@>",$u+=2):(e=u,0===qu&&zu(tt));e===u&&("<@"===r.substr($u,2)?(e="<@",$u+=2):(e=u,0===qu&&zu(et)),e===u&&(e=gc())===u&&(e=function(){var t;"#>>"===r.substr($u,3)?(t="#>>",$u+=3):(t=u,0===qu&&zu(Nu));return t}())===u&&(e=function(){var t;"#>"===r.substr($u,2)?(t="#>",$u+=2):(t=u,0===qu&&zu(Iu));return t}())===u&&(63===r.charCodeAt($u)?(e="?",$u++):(e=u,0===qu&&zu(nt)),e===u&&("?|"===r.substr($u,2)?(e="?|",$u+=2):(e=u,0===qu&&zu(ot)),e===u&&("?&"===r.substr($u,2)?(e="?&",$u+=2):(e=u,0===qu&&zu(ut)),e===u&&("#-"===r.substr($u,2)?(e="#-",$u+=2):(e=u,0===qu&&zu(at)))))));e!==u&&xc()!==u&&(n=Oa())!==u?(t,e={op:e,right:(o=n)&&o.expr||o},t=e):($u=t,t=u);var o;return t}()),t}function ds(){var t;return">="===r.substr($u,2)?(t=">=",$u+=2):(t=u,0===qu&&zu(qr)),t===u&&(62===r.charCodeAt($u)?(t=">",$u++):(t=u,0===qu&&zu(Kr)),t===u&&("<="===r.substr($u,2)?(t="<=",$u+=2):(t=u,0===qu&&zu(Qr)),t===u&&("<>"===r.substr($u,2)?(t="<>",$u+=2):(t=u,0===qu&&zu(Jr)),t===u&&(60===r.charCodeAt($u)?(t="<",$u++):(t=u,0===qu&&zu(Zr)),t===u&&(61===r.charCodeAt($u)?(t="=",$u++):(t=u,0===qu&&zu(Vr)),t===u&&("!="===r.substr($u,2)?(t="!=",$u+=2):(t=u,0===qu&&zu(zr)))))))),t}function ys(){var t,e,n;return t=$u,"escape"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(rt)),e!==u&&xc()!==u&&(n=Ws())!==u?(t,t=e={type:"ESCAPE",value:n}):($u=t,t=u),t}function Es(){var r,t,e,n,o,a;return r=$u,t=$u,(e=Pi())!==u&&(n=xc())!==u&&(o=xi())!==u?t=e=[e,n,o]:($u=t,t=u),t!==u&&(r,t=(a=t)[0]+" "+a[2]),(r=t)===u&&(r=xi()),r}function hs(){var r,t,e,n,o,a,s,i;if(r=$u,(t=Ls())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=Cs())!==u&&(s=xc())!==u&&(i=Ls())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=Cs())!==u&&(s=xc())!==u&&(i=Ls())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,r=t=Wr(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function Cs(){var t;return 43===r.charCodeAt($u)?(t="+",$u++):(t=u,0===qu&&zu(st)),t===u&&(45===r.charCodeAt($u)?(t="-",$u++):(t=u,0===qu&&zu(it))),t}function Ls(){var r,t,e,n,o,a,s,i;if(r=$u,(t=ms())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=ws())!==u&&(s=xc())!==u&&(i=ms())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=ws())!==u&&(s=xc())!==u&&(i=ms())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,r=t=ol(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function ws(){var t;return 42===r.charCodeAt($u)?(t="*",$u++):(t=u,0===qu&&zu(ct)),t===u&&(47===r.charCodeAt($u)?(t="/",$u++):(t=u,0===qu&&zu(lt)),t===u&&(37===r.charCodeAt($u)?(t="%",$u++):(t=u,0===qu&&zu(ft)))),t}function ms(){var t,e,n,o;return(t=function(){var t,e,n,o,a,s,i,c;t=$u,(e=$s())===u&&(e=Us())===u&&(e=Hs())===u&&(e=os())===u&&(e=ns())===u&&(e=As())===u&&(e=xs());e!==u&&pc()!==u&&(n=Jc())!==u?(t,t=e={type:"cast",keyword:"cast",expr:e,symbol:"::",target:n}):($u=t,t=u);t===u&&(t=$u,(e=$i())===u&&(e=Vi()),e!==u&&xc()!==u&&(n=Tc())!==u&&xc()!==u&&(o=ss())!==u&&xc()!==u&&hi()!==u&&xc()!==u&&(a=Jc())!==u&&xc()!==u&&(s=Sc())!==u?(t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:e}}(e,o,a),t=e):($u=t,t=u),t===u&&(t=$u,(e=$i())===u&&(e=Vi()),e!==u&&xc()!==u&&(n=Tc())!==u&&xc()!==u&&(o=ss())!==u&&xc()!==u&&hi()!==u&&xc()!==u&&(a=Ki())!==u&&xc()!==u&&(s=Tc())!==u&&xc()!==u&&(i=Js())!==u&&xc()!==u&&Sc()!==u&&xc()!==u&&(c=Sc())!==u?(t,e=function(r,t,e){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:{dataType:"DECIMAL("+e+")"}}}(e,o,i),t=e):($u=t,t=u),t===u&&(t=$u,(e=$i())===u&&(e=Vi()),e!==u&&xc()!==u&&(n=Tc())!==u&&xc()!==u&&(o=ss())!==u&&xc()!==u&&hi()!==u&&xc()!==u&&(a=Ki())!==u&&xc()!==u&&(s=Tc())!==u&&xc()!==u&&(i=Js())!==u&&xc()!==u&&mc()!==u&&xc()!==u&&(c=Js())!==u&&xc()!==u&&Sc()!==u&&xc()!==u&&Sc()!==u?(t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:{dataType:"DECIMAL("+e+", "+n+")"}}}(e,o,i,c),t=e):($u=t,t=u),t===u&&(t=$u,(e=$i())===u&&(e=Vi()),e!==u&&xc()!==u&&(n=Tc())!==u&&xc()!==u&&(o=ss())!==u&&xc()!==u&&hi()!==u&&xc()!==u&&(a=function(){var t;(t=function(){var t,e,n,o;t=$u,"signed"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(Eo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SIGNED"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=Qi());return t}())!==u&&xc()!==u?((s=Zi())===u&&(s=null),s!==u&&xc()!==u&&(i=Sc())!==u?(t,e=function(r,t,e,n){return{type:"cast",keyword:r.toLowerCase(),expr:t,symbol:"as",target:{dataType:e+(n?" "+n:"")}}}(e,o,a,s),t=e):($u=t,t=u)):($u=t,t=u)))));return t}())===u&&(t=$s())===u&&(t=Us())===u&&(t=Hs())===u&&(t=os())===u&&(t=ns())===u&&(t=As())===u&&(t=xs())===u&&(t=$u,Tc()!==u&&(e=xc())!==u&&(n=cs())!==u&&xc()!==u&&Sc()!==u?(t,(o=n).parentheses=!0,t=o):($u=t,t=u),t===u&&(t=Kc())===u&&(t=$u,xc()!==u?(36===r.charCodeAt($u)?(e="$",$u++):(e=u,0===qu&&zu(pt)),e!==u&&(n=Qs())!==u?(t,t={type:"origin",value:"$"+n.value}):($u=t,t=u)):($u=t,t=u))),t}function As(){var r,t,e,n,o,a,s,i,c,l,f,p;if(r=$u,t=$u,(e=Ts())!==u&&(n=xc())!==u&&(o=wc())!==u?t=e=[e,n,o]:($u=t,t=u),t===u&&(t=null),t!==u&&(e=xc())!==u&&(n=Ac())!==u?(r,r=t=function(r){const t=r&&r[0]||null;return ll.add(`select::${t}::(.*)`),{type:"column_ref",table:t,column:"*"}}(t)):($u=r,r=u),r===u){if(r=$u,t=$u,(e=Ts())!==u&&(n=xc())!==u&&(o=wc())!==u?t=e=[e,n,o]:($u=t,t=u),t===u&&(t=null),t!==u)if((e=xc())!==u)if((n=Is())!==u)if((o=xc())!==u){if(a=[],s=$u,(i=Oc())===u&&(i=_c()),i!==u&&(c=xc())!==u?((l=Ws())===u&&(l=Qs()),l!==u?s=i=[i,c,l]:($u=s,s=u)):($u=s,s=u),s!==u)for(;s!==u;)a.push(s),s=$u,(i=Oc())===u&&(i=_c()),i!==u&&(c=xc())!==u?((l=Ws())===u&&(l=Qs()),l!==u?s=i=[i,c,l]:($u=s,s=u)):($u=s,s=u);else a=u;a!==u?(r,r=t=function(r,t,e){const n=r&&r[0]||null;return ll.add(`select::${n}::${t}`),{type:"column_ref",table:n,column:t,arrows:e.map(r=>r[0]),properties:e.map(r=>r[2])}}(t,n,a)):($u=r,r=u)}else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;else $u=r,r=u;r===u&&(r=$u,(t=Ts())!==u&&(e=xc())!==u&&(n=wc())!==u&&(o=xc())!==u&&(a=Is())!==u?(r,f=t,p=a,ll.add(`select::${f}::${p}`),r=t={type:"column_ref",table:f,column:p}):($u=r,r=u),r===u&&(r=$u,(t=Is())!==u&&(r,t=function(r){return ll.add("select::null::"+r),{type:"column_ref",table:null,column:r}}(t)),r=t))}return r}function Ts(){var r,t;return r=$u,(t=_s())!==u?($u,(bt(t)?u:void 0)!==u?(r,r=t=t):($u=r,r=u)):($u=r,r=u),r===u&&(r=$u,(t=Ss())!==u&&(r,t=t),r=t),r}function Ss(){var t;return(t=function(){var t,e,n,o;t=$u,34===r.charCodeAt($u)?(e='"',$u++):(e=u,0===qu&&zu(vt));if(e!==u){if(n=[],dt.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(yt)),o!==u)for(;o!==u;)n.push(o),dt.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(yt));else n=u;n!==u?(34===r.charCodeAt($u)?(o='"',$u++):(o=u,0===qu&&zu(vt)),o!==u?(t,e=Et(n),t=e):($u=t,t=u)):($u=t,t=u)}else $u=t,t=u;return t}())===u&&(t=function(){var t,e,n,o;t=$u,39===r.charCodeAt($u)?(e="'",$u++):(e=u,0===qu&&zu(Cr));if(e!==u){if(n=[],ht.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(Ct)),o!==u)for(;o!==u;)n.push(o),ht.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(Ct));else n=u;n!==u?(39===r.charCodeAt($u)?(o="'",$u++):(o=u,0===qu&&zu(Cr)),o!==u?(t,e=Et(n),t=e):($u=t,t=u)):($u=t,t=u)}else $u=t,t=u;return t}())===u&&(t=function(){var t,e,n,o;t=$u,96===r.charCodeAt($u)?(e="`",$u++):(e=u,0===qu&&zu(Lt));if(e!==u){if(n=[],wt.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(mt)),o!==u)for(;o!==u;)n.push(o),wt.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(mt));else n=u;n!==u?(96===r.charCodeAt($u)?(o="`",$u++):(o=u,0===qu&&zu(Lt)),o!==u?(t,e=Et(n),t=e):($u=t,t=u)):($u=t,t=u)}else $u=t,t=u;return t}()),t}function Rs(){var r,t;return r=$u,(t=Ns())!==u&&(r,t=t),(r=t)===u&&(r=Ss()),r}function Is(){var r,t;return r=$u,(t=Ns())!==u?($u,(bt(t)?u:void 0)!==u?(r,r=t=t):($u=r,r=u)):($u=r,r=u),r===u&&(r=Ss()),r}function Ns(){var r,t,e,n;if(r=$u,(t=Os())!==u){for(e=[],n=js();n!==u;)e.push(n),n=js();e!==u?(r,r=t=t+e.join("")):($u=r,r=u)}else $u=r,r=u;return r}function _s(){var r,t,e,n;if(r=$u,(t=Os())!==u){for(e=[],n=gs();n!==u;)e.push(n),n=gs();e!==u?(r,r=t=t+e.join("")):($u=r,r=u)}else $u=r,r=u;return r}function Os(){var t;return At.test(r.charAt($u))?(t=r.charAt($u),$u++):(t=u,0===qu&&zu(Tt)),t}function gs(){var t;return St.test(r.charAt($u))?(t=r.charAt($u),$u++):(t=u,0===qu&&zu(Rt)),t}function js(){var t;return It.test(r.charAt($u))?(t=r.charAt($u),$u++):(t=u,0===qu&&zu(Nt)),t}function xs(){var t,e,n,o;return t=$u,e=$u,58===r.charCodeAt($u)?(n=":",$u++):(n=u,0===qu&&zu(_t)),n!==u&&(o=_s())!==u?e=n=[n,o]:($u=e,e=u),e!==u&&(t,e={type:"param",value:e[1]}),t=e}function Us(){var t;return(t=function(){var t,e,n;t=$u,(e=function(){var t,e,n,o;t=$u,"count"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Yn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="COUNT"):($u=t,t=u)):($u=t,t=u);return t}())!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(n=function(){var t,e,n,o,a,s,i,c,l,f;t=$u,(e=function(){var t,e;t=$u,42===r.charCodeAt($u)?(e="*",$u++):(e=u,0===qu&&zu(ct));e!==u&&(t,e={type:"star",value:"*"});return t=e}())!==u&&(t,e={expr:e});if((t=e)===u){if(t=$u,(e=gi())===u&&(e=null),e!==u)if(xc()!==u)if((n=Tc())!==u)if(xc()!==u)if((o=ss())!==u)if(xc()!==u)if(Sc()!==u){for(a=[],s=$u,(i=xc())!==u?((c=Gi())===u&&(c=Fi()),c!==u&&(l=xc())!==u&&(f=ss())!==u?s=i=[i,c,l,f]:($u=s,s=u)):($u=s,s=u);s!==u;)a.push(s),s=$u,(i=xc())!==u?((c=Gi())===u&&(c=Fi()),c!==u&&(l=xc())!==u&&(f=ss())!==u?s=i=[i,c,l,f]:($u=s,s=u)):($u=s,s=u);a!==u&&(s=xc())!==u?((i=Wa())===u&&(i=null),i!==u?(t,e=function(r,t,e,n){const o=e.length;let u=t;u.parentheses=!0;for(let r=0;r<o;++r)u=tl(e[r][1],u,e[r][3]);return{distinct:r,expr:u,orderby:n}}(e,o,a,i),t=e):($u=t,t=u)):($u=t,t=u)}else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;t===u&&(t=$u,(e=gi())===u&&(e=null),e!==u&&xc()!==u&&(n=Ba())!==u&&xc()!==u?((o=Wa())===u&&(o=null),o!==u?(t,t=e={distinct:e,expr:n,orderby:o}):($u=t,t=u)):($u=t,t=u))}return t}())!==u&&xc()!==u&&Sc()!==u?(t,t=e={type:"aggr_func",name:e,args:n}):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c;t=$u,(e=function(){var t;(t=function(){var t,e,n,o;t=$u,"sum"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Wn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SUM"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"max"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu($n));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="MAX"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"min"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Vn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="MIN"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"avg"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Xn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="AVG"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"collect"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(qn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="COLLECT"):($u=t,t=u)):($u=t,t=u);return t}());return t}())!==u&&xc()!==u&&Tc()!==u&&xc()!==u?((n=gi())===u&&(n=null),n!==u&&(o=xc())!==u&&(a=hs())!==u&&(s=xc())!==u&&(i=Sc())!==u?(t,t=e={type:"aggr_func",name:e,args:{expr:a,distinct:n}}):($u=t,t=u)):($u=t,t=u);t===u&&(t=$u,(e=function(){var t;(t=function(){var t,e,n,o;t=$u,"rank"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Kn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="RANK"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"dense_rank"===r.substr($u,10).toLowerCase()?(e=r.substr($u,10),$u+=10):(e=u,0===qu&&zu(Qn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="DENSE_RANK"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"row_number"===r.substr($u,10).toLowerCase()?(e=r.substr($u,10),$u+=10):(e=u,0===qu&&zu(Zn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="ROW_NUMBER"):($u=t,t=u)):($u=t,t=u);return t}());return t}())!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(n=Sc())!==u?(t,e=function(r){return{type:"aggr_func",name:r}}(e),t=e):($u=t,t=u),t===u&&(t=$u,(e=function(){var t,e,n,o;t=$u,"listagg"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Jn));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="LISTAGG"):($u=t,t=u)):($u=t,t=u);return t}())!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(n=hs())!==u?(o=$u,(a=xc())!==u&&(s=mc())!==u&&(i=xc())!==u&&(c=Ws())!==u?o=a=[a,s,i,c]:($u=o,o=u),o===u&&(o=null),o!==u&&(a=xc())!==u&&(s=Sc())!==u?(t,e=function(r,t,e){return{type:"aggr_func",name:r,args:{expr:t,separator:e}}}(e,n,o),t=e):($u=t,t=u)):($u=t,t=u)));return t}()),t}function ks(){var r,t,e;return r=$u,Li()!==u&&xc()!==u&&ai()!==u&&xc()!==u&&(t=lc())!==u&&xc()!==u&&Tc()!==u&&xc()!==u?((e=es())===u&&(e=null),e!==u&&xc()!==u&&Sc()!==u?(r,r={type:"on update",keyword:t,parentheses:!0,expr:e}):($u=r,r=u)):($u=r,r=u),r===u&&(r=$u,Li()!==u&&xc()!==u&&ai()!==u&&xc()!==u&&(t=lc())!==u?(r,r=function(r){return{type:"on update",keyword:r}}(t)):($u=r,r=u)),r}function Ms(){var t,e,n,o;return t=$u,"over"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Ot)),e!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&vi()!==u&&xc()!==u&&Ii()!==u&&xc()!==u&&(n=_a())!==u&&xc()!==u?((o=Wa())===u&&(o=null),o!==u&&xc()!==u&&Sc()!==u?(t,t=e={partitionby:n,orderby:o}):($u=t,t=u)):($u=t,t=u),t===u&&(t=ks()),t}function Ds(){var t,e,n;return t=$u,"position"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(gt)),e!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(n=function(){var r,t,e,n,o,a,s,i;return r=$u,(t=Ws())!==u&&xc()!==u&&xi()!==u&&xc()!==u&&(e=ss())!==u?(n=$u,(o=xc())!==u&&(a=yi())!==u&&(s=xc())!==u&&(i=Qs())!==u?n=o=[o,a,s,i]:($u=n,n=u),n===u&&(n=null),n!==u?(r,r=t=function(r,t,e){let n=[r,{type:"origin",value:"in"},t];return e&&(n.push({type:"origin",value:"from"}),n.push(e[3])),{type:"expr_list",value:n}}(t,e,n)):($u=r,r=u)):($u=r,r=u),r}())!==u&&xc()!==u&&Sc()!==u?(t,t=e={type:"function",name:"POSITION",separator:" ",args:n}):($u=t,t=u),t}function Ps(){var t,e,n;return t=$u,(e=function(){var t;return"both"===r.substr($u,4).toLowerCase()?(t=r.substr($u,4),$u+=4):(t=u,0===qu&&zu(jt)),t===u&&("leading"===r.substr($u,7).toLowerCase()?(t=r.substr($u,7),$u+=7):(t=u,0===qu&&zu(xt)),t===u&&("trailing"===r.substr($u,8).toLowerCase()?(t=r.substr($u,8),$u+=8):(t=u,0===qu&&zu(Ut)))),t}())===u&&(e=null),e!==u&&xc()!==u?((n=Ws())===u&&(n=null),n!==u&&xc()!==u&&yi()!==u?(t,t=e=function(r,t,e){let n=[];return r&&n.push({type:"origin",value:r}),t&&n.push(t),n.push({type:"origin",value:"from"}),{type:"expr_list",value:n}}(e,n)):($u=t,t=u)):($u=t,t=u),t}function Gs(){var t,e,n;return t=$u,"overlay"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Pt)),e!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(n=function(){var t,e,n,o,a,s,i,c,l,f;return t=$u,(e=ss())!==u&&xc()!==u?("placing"===r.substr($u,7).toLowerCase()?(n=r.substr($u,7),$u+=7):(n=u,0===qu&&zu(Mt)),n!==u&&xc()!==u&&(o=ss())!==u&&xc()!==u&&yi()!==u&&xc()!==u&&(a=Qs())!==u?(s=$u,(i=xc())!==u?("for"===r.substr($u,3).toLowerCase()?(c=r.substr($u,3),$u+=3):(c=u,0===qu&&zu(Dt)),c!==u&&(l=xc())!==u&&(f=Qs())!==u?s=i=[i,c,l,f]:($u=s,s=u)):($u=s,s=u),s===u&&(s=null),s!==u?(t,t=e=function(r,t,e,n){let o=[r,{type:"origin",value:"placing"},t,{type:"origin",value:"from"},e];return n&&(o.push({type:"origin",value:"for"}),o.push(n[3])),{type:"expr_list",value:o}}(e,o,a,s)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t}())!==u&&xc()!==u&&Sc()!==u?(t,t=e={type:"function",name:"OVERLAY",separator:" ",args:n}):($u=t,t=u),t}function Fs(){var t,e,n;return t=$u,"substring"===r.substr($u,9).toLowerCase()?(e=r.substr($u,9),$u+=9):(e=u,0===qu&&zu(Gt)),e!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(n=function(){var t,e,n,o,a,s,i,c;return t=$u,(e=ss())!==u&&xc()!==u&&yi()!==u&&xc()!==u&&(n=Qs())!==u?(o=$u,(a=xc())!==u?("for"===r.substr($u,3).toLowerCase()?(s=r.substr($u,3),$u+=3):(s=u,0===qu&&zu(Dt)),s!==u&&(i=xc())!==u&&(c=Qs())!==u?o=a=[a,s,i,c]:($u=o,o=u)):($u=o,o=u),o===u&&(o=null),o!==u?(t,t=e=function(r,t,e){let n=[r,{type:"origin",value:"from"},t];return e&&(n.push({type:"origin",value:"for"}),n.push(e[3])),{type:"expr_list",value:n}}(e,n,o)):($u=t,t=u)):($u=t,t=u),t}())!==u&&xc()!==u&&Sc()!==u?(t,t=e={type:"function",name:"SUBSTRING",separator:" ",args:n}):($u=t,t=u),t}function Hs(){var t,e,n,o,a;return(t=Ds())===u&&(t=function(){var t,e,n,o;return t=$u,"trim"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(kt)),e!==u&&xc()!==u&&Tc()!==u&&xc()!==u?((n=Ps())===u&&(n=null),n!==u&&xc()!==u&&(o=ss())!==u&&xc()!==u&&Sc()!==u?(t,t=e=function(r,t){let e=r||{type:"expr_list",value:[]};return e.value.push(t),{type:"function",name:"TRIM",args:e}}(n,o)):($u=t,t=u)):($u=t,t=u),t}())===u&&(t=Fs())===u&&(t=Gs())===u&&(t=$u,(e=function(){var t;(t=Ys())===u&&(t=function(){var t,e,n,o;t=$u,"current_user"===r.substr($u,12).toLowerCase()?(e=r.substr($u,12),$u+=12):(e=u,0===qu&&zu(Wo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="CURRENT_USER"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"user"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Po));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="USER"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"session_user"===r.substr($u,12).toLowerCase()?(e=r.substr($u,12),$u+=12):(e=u,0===qu&&zu(Xo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SESSION_USER"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"system_user"===r.substr($u,11).toLowerCase()?(e=r.substr($u,11),$u+=11):(e=u,0===qu&&zu(qo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SYSTEM_USER"):($u=t,t=u)):($u=t,t=u);return t}());return t}())!==u&&xc()!==u&&(n=Tc())!==u&&xc()!==u?((o=es())===u&&(o=null),o!==u&&xc()!==u&&Sc()!==u&&xc()!==u?((a=Ms())===u&&(a=null),a!==u?(t,t=e={type:"function",name:e,args:o||{type:"expr_list",value:[]},over:a}):($u=t,t=u)):($u=t,t=u)):($u=t,t=u),t===u&&(t=function(){var r,t,e,n,o;r=$u,(t=Hi())!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(e=Bs())!==u&&xc()!==u&&yi()!==u&&xc()!==u?((n=sc())===u&&(n=cc())===u&&(n=ac())===u&&(n=oc()),n!==u&&xc()!==u&&(o=ss())!==u&&xc()!==u&&Sc()!==u?(r,a=e,s=n,i=o,t={type:t.toLowerCase(),args:{field:a,cast_type:s,source:i}},r=t):($u=r,r=u)):($u=r,r=u);var a,s,i;r===u&&(r=$u,(t=Hi())!==u&&xc()!==u&&Tc()!==u&&xc()!==u&&(e=Bs())!==u&&xc()!==u&&yi()!==u&&xc()!==u&&(n=ss())!==u&&xc()!==u&&(o=Sc())!==u?(r,t=function(r,t,e){return{type:r.toLowerCase(),args:{field:t,source:e}}}(t,e,n),r=t):($u=r,r=u));return r}())===u&&(t=$u,(e=Ys())!==u&&xc()!==u?((n=ks())===u&&(n=null),n!==u?(t,t=e={type:"function",name:e,over:n}):($u=t,t=u)):($u=t,t=u),t===u&&(t=$u,(e=Wc())!==u&&xc()!==u&&(n=Tc())!==u&&xc()!==u?((o=cs())===u&&(o=null),o!==u&&xc()!==u&&Sc()!==u&&xc()!==u?((a=Ms())===u&&(a=null),a!==u?(t,t=e=function(r,t,e){return t&&"expr_list"!==t.type&&(t={type:"expr_list",value:[t]}),{type:"function",name:r,args:t||{type:"expr_list",value:[]},over:e}}(e,o,a)):($u=t,t=u)):($u=t,t=u)):($u=t,t=u)))),t}function Bs(){var t,e;return t=$u,"century"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Ft)),e===u&&("day"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Ht)),e===u&&("date"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Bt)),e===u&&("decade"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(Yt)),e===u&&("dow"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu($t)),e===u&&("doy"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Vt)),e===u&&("epoch"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Wt)),e===u&&("hour"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Xt)),e===u&&("isodow"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(qt)),e===u&&("isoyear"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Kt)),e===u&&("microseconds"===r.substr($u,12).toLowerCase()?(e=r.substr($u,12),$u+=12):(e=u,0===qu&&zu(Qt)),e===u&&("millennium"===r.substr($u,10).toLowerCase()?(e=r.substr($u,10),$u+=10):(e=u,0===qu&&zu(Jt)),e===u&&("milliseconds"===r.substr($u,12).toLowerCase()?(e=r.substr($u,12),$u+=12):(e=u,0===qu&&zu(Zt)),e===u&&("minute"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(zt)),e===u&&("month"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(re)),e===u&&("quarter"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(te)),e===u&&("second"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(ee)),e===u&&("timezone"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(ne)),e===u&&("timezone_hour"===r.substr($u,13).toLowerCase()?(e=r.substr($u,13),$u+=13):(e=u,0===qu&&zu(oe)),e===u&&("timezone_minute"===r.substr($u,15).toLowerCase()?(e=r.substr($u,15),$u+=15):(e=u,0===qu&&zu(ue)),e===u&&("week"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(ae)),e===u&&("year"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(se))))))))))))))))))))))),e!==u&&(t,e=e),t=e}function Ys(){var t;return(t=function(){var t,e,n,o;t=$u,"current_date"===r.substr($u,12).toLowerCase()?(e=r.substr($u,12),$u+=12):(e=u,0===qu&&zu(Bo));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="CURRENT_DATE"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n,o;t=$u,"current_time"===r.substr($u,12).toLowerCase()?(e=r.substr($u,12),$u+=12):(e=u,0===qu&&zu($o));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="CURRENT_TIME"):($u=t,t=u)):($u=t,t=u);return t}())===u&&(t=lc()),t}function $s(){var t;return(t=Ws())===u&&(t=Qs())===u&&(t=function(){var t,e;t=$u,(e=function(){var t,e,n,o;t=$u,"true"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Ge));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}())!==u&&(t,e={type:"bool",value:!0});(t=e)===u&&(t=$u,(e=function(){var t,e,n,o;t=$u,"false"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(He));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}())!==u&&(t,e={type:"bool",value:!1}),t=e);return t}())===u&&(t=Vs())===u&&(t=function(){var t,e,n,o,a,s;t=$u,(e=ac())===u&&(e=oc())===u&&(e=sc())===u&&(e=uc());if(e!==u)if(xc()!==u){if(n=$u,39===r.charCodeAt($u)?(o="'",$u++):(o=u,0===qu&&zu(Cr)),o!==u){for(a=[],s=qs();s!==u;)a.push(s),s=qs();a!==u?(39===r.charCodeAt($u)?(s="'",$u++):(s=u,0===qu&&zu(Cr)),s!==u?n=o=[o,a,s]:($u=n,n=u)):($u=n,n=u)}else $u=n,n=u;n!==u?(t,i=n,e={type:e.toLowerCase(),value:i[1].join("")},t=e):($u=t,t=u)}else $u=t,t=u;else $u=t,t=u;var i;if(t===u)if(t=$u,(e=ac())===u&&(e=oc())===u&&(e=sc())===u&&(e=uc()),e!==u)if(xc()!==u){if(n=$u,34===r.charCodeAt($u)?(o='"',$u++):(o=u,0===qu&&zu(vt)),o!==u){for(a=[],s=Xs();s!==u;)a.push(s),s=Xs();a!==u?(34===r.charCodeAt($u)?(s='"',$u++):(s=u,0===qu&&zu(vt)),s!==u?n=o=[o,a,s]:($u=n,n=u)):($u=n,n=u)}else $u=n,n=u;n!==u?(t,e=function(r,t){return{type:r.toLowerCase(),value:t[1].join("")}}(e,n),t=e):($u=t,t=u)}else $u=t,t=u;else $u=t,t=u;return t}()),t}function Vs(){var t,e;return t=$u,(e=function(){var t,e,n,o;t=$u,"null"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(De));e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u);return t}())!==u&&(t,e={type:"null",value:null}),t=e}function Ws(){var t,e,n,o,a;if(t=$u,e=$u,39===r.charCodeAt($u)?(n="'",$u++):(n=u,0===qu&&zu(Cr)),n!==u){for(o=[],a=qs();a!==u;)o.push(a),a=qs();o!==u?(39===r.charCodeAt($u)?(a="'",$u++):(a=u,0===qu&&zu(Cr)),a!==u?e=n=[n,o,a]:($u=e,e=u)):($u=e,e=u)}else $u=e,e=u;if(e!==u&&(t,e={type:"single_quote_string",value:e[1].join("")}),(t=e)===u){if(t=$u,e=$u,34===r.charCodeAt($u)?(n='"',$u++):(n=u,0===qu&&zu(vt)),n!==u){for(o=[],a=Xs();a!==u;)o.push(a),a=Xs();o!==u?(34===r.charCodeAt($u)?(a='"',$u++):(a=u,0===qu&&zu(vt)),a!==u?e=n=[n,o,a]:($u=e,e=u)):($u=e,e=u)}else $u=e,e=u;e!==u?(n=$u,qu++,o=wc(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e=function(r){return{type:"double_quote_string",value:r[1].join("")}}(e)):($u=t,t=u)):($u=t,t=u)}return t}function Xs(){var t;return ie.test(r.charAt($u))?(t=r.charAt($u),$u++):(t=u,0===qu&&zu(ce)),t===u&&(t=Ks()),t}function qs(){var t;return le.test(r.charAt($u))?(t=r.charAt($u),$u++):(t=u,0===qu&&zu(fe)),t===u&&(t=Ks()),t}function Ks(){var t,e,n,o,a,s,i,c,l,f;return t=$u,"\\'"===r.substr($u,2)?(e="\\'",$u+=2):(e=u,0===qu&&zu(pe)),e!==u&&(t,e="\\'"),(t=e)===u&&(t=$u,'\\"'===r.substr($u,2)?(e='\\"',$u+=2):(e=u,0===qu&&zu(be)),e!==u&&(t,e='\\"'),(t=e)===u&&(t=$u,"\\\\"===r.substr($u,2)?(e="\\\\",$u+=2):(e=u,0===qu&&zu(ve)),e!==u&&(t,e="\\\\"),(t=e)===u&&(t=$u,"\\/"===r.substr($u,2)?(e="\\/",$u+=2):(e=u,0===qu&&zu(de)),e!==u&&(t,e="\\/"),(t=e)===u&&(t=$u,"\\b"===r.substr($u,2)?(e="\\b",$u+=2):(e=u,0===qu&&zu(ye)),e!==u&&(t,e="\b"),(t=e)===u&&(t=$u,"\\f"===r.substr($u,2)?(e="\\f",$u+=2):(e=u,0===qu&&zu(Ee)),e!==u&&(t,e="\f"),(t=e)===u&&(t=$u,"\\n"===r.substr($u,2)?(e="\\n",$u+=2):(e=u,0===qu&&zu(he)),e!==u&&(t,e="\n"),(t=e)===u&&(t=$u,"\\r"===r.substr($u,2)?(e="\\r",$u+=2):(e=u,0===qu&&zu(Ce)),e!==u&&(t,e="\r"),(t=e)===u&&(t=$u,"\\t"===r.substr($u,2)?(e="\\t",$u+=2):(e=u,0===qu&&zu(Le)),e!==u&&(t,e="\t"),(t=e)===u&&(t=$u,"\\u"===r.substr($u,2)?(e="\\u",$u+=2):(e=u,0===qu&&zu(we)),e!==u&&(n=ei())!==u&&(o=ei())!==u&&(a=ei())!==u&&(s=ei())!==u?(t,i=n,c=o,l=a,f=s,t=e=String.fromCharCode(parseInt("0x"+i+c+l+f))):($u=t,t=u),t===u&&(t=$u,92===r.charCodeAt($u)?(e="\\",$u++):(e=u,0===qu&&zu(me)),e!==u&&(t,e="\\"),(t=e)===u&&(t=$u,"''"===r.substr($u,2)?(e="''",$u+=2):(e=u,0===qu&&zu(Ae)),e!==u&&(t,e="''"),(t=e)===u&&(t=$u,'""'===r.substr($u,2)?(e='""',$u+=2):(e=u,0===qu&&zu(Te)),e!==u&&(t,e='""'),(t=e)===u&&(t=$u,"``"===r.substr($u,2)?(e="``",$u+=2):(e=u,0===qu&&zu(Se)),e!==u&&(t,e="``"),t=e))))))))))))),t}function Qs(){var r,t,e;return r=$u,(t=function(){var r,t,e,n;r=$u,(t=Js())!==u&&(e=Zs())!==u&&(n=zs())!==u?(r,r=t={type:"bigint",value:t+e+n}):($u=r,r=u);r===u&&(r=$u,(t=Js())!==u&&(e=Zs())!==u?(r,t=function(r,t){const e=r+t;return el(r)?{type:"bigint",value:e}:parseFloat(e)}(t,e),r=t):($u=r,r=u),r===u&&(r=$u,(t=Js())!==u&&(e=zs())!==u?(r,t=function(r,t){return{type:"bigint",value:r+t}}(t,e),r=t):($u=r,r=u),r===u&&(r=$u,(t=Js())!==u&&(r,t=function(r){return el(r)?{type:"bigint",value:r}:parseFloat(r)}(t)),r=t)));return r}())!==u&&(r,t=(e=t)&&"bigint"===e.type?e:{type:"number",value:e}),r=t}function Js(){var t,e,n;return(t=ri())===u&&(t=ti())===u&&(t=$u,45===r.charCodeAt($u)?(e="-",$u++):(e=u,0===qu&&zu(it)),e===u&&(43===r.charCodeAt($u)?(e="+",$u++):(e=u,0===qu&&zu(st))),e!==u&&(n=ri())!==u?(t,t=e=e+n):($u=t,t=u),t===u&&(t=$u,45===r.charCodeAt($u)?(e="-",$u++):(e=u,0===qu&&zu(it)),e===u&&(43===r.charCodeAt($u)?(e="+",$u++):(e=u,0===qu&&zu(st))),e!==u&&(n=ti())!==u?(t,t=e=function(r,t){return r+t}(e,n)):($u=t,t=u))),t}function Zs(){var t,e,n;return t=$u,46===r.charCodeAt($u)?(e=".",$u++):(e=u,0===qu&&zu(Ne)),e!==u&&(n=ri())!==u?(t,t=e="."+n):($u=t,t=u),t}function zs(){var t,e,n;return t=$u,(e=function(){var t,e,n;t=$u,xe.test(r.charAt($u))?(e=r.charAt($u),$u++):(e=u,0===qu&&zu(Ue));e!==u?(ke.test(r.charAt($u))?(n=r.charAt($u),$u++):(n=u,0===qu&&zu(Me)),n===u&&(n=null),n!==u?(t,t=e=e+(null!==(o=n)?o:"")):($u=t,t=u)):($u=t,t=u);var o;return t}())!==u&&(n=ri())!==u?(t,t=e=e+n):($u=t,t=u),t}function ri(){var r,t,e;if(r=$u,t=[],(e=ti())!==u)for(;e!==u;)t.push(e),e=ti();else t=u;return t!==u&&(r,t=t.join("")),r=t}function ti(){var t;return _e.test(r.charAt($u))?(t=r.charAt($u),$u++):(t=u,0===qu&&zu(Oe)),t}function ei(){var t;return ge.test(r.charAt($u))?(t=r.charAt($u),$u++):(t=u,0===qu&&zu(je)),t}function ni(){var t,e,n,o;return t=$u,"default"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(A)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function oi(){var t,e,n,o;return t=$u,"to"===r.substr($u,2).toLowerCase()?(e=r.substr($u,2),$u+=2):(e=u,0===qu&&zu(Fe)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function ui(){var t,e,n,o;return t=$u,"drop"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Be)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="DROP"):($u=t,t=u)):($u=t,t=u),t}function ai(){var t,e,n,o;return t=$u,"update"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(We)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function si(){var t,e,n,o;return t=$u,"create"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(Xe)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function ii(){var t,e,n,o;return t=$u,"temporary"===r.substr($u,9).toLowerCase()?(e=r.substr($u,9),$u+=9):(e=u,0===qu&&zu(qe)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function ci(){var t,e,n,o;return t=$u,"delete"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(Ke)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function li(){var t,e,n,o;return t=$u,"insert"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(Qe)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function fi(){var t,e,n,o;return t=$u,"replace"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Ze)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function pi(){var t,e,n,o;return t=$u,"rename"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(rn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function bi(){var t,e,n,o;return t=$u,"ignore"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(tn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function vi(){var t,e,n,o;return t=$u,"partition"===r.substr($u,9).toLowerCase()?(e=r.substr($u,9),$u+=9):(e=u,0===qu&&zu(en)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="PARTITION"):($u=t,t=u)):($u=t,t=u),t}function di(){var t,e,n,o;return t=$u,"into"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(nn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function yi(){var t,e,n,o;return t=$u,"from"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(on)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function Ei(){var t,e,n,o;return t=$u,"set"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(cr)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SET"):($u=t,t=u)):($u=t,t=u),t}function hi(){var t,e,n,o;return t=$u,"as"===r.substr($u,2).toLowerCase()?(e=r.substr($u,2),$u+=2):(e=u,0===qu&&zu(un)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function Ci(){var t,e,n,o;return t=$u,"table"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(an)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="TABLE"):($u=t,t=u)):($u=t,t=u),t}function Li(){var t,e,n,o;return t=$u,"on"===r.substr($u,2).toLowerCase()?(e=r.substr($u,2),$u+=2):(e=u,0===qu&&zu(K)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function wi(){var t,e,n,o;return t=$u,"join"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(yn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function mi(){var t,e,n,o;return t=$u,"cross"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(En)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function Ai(){var t,e,n,o;return t=$u,"outer"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Cn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function Ti(){var t,e,n,o;return t=$u,"values"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(An)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function Si(){var t,e,n,o;return t=$u,"using"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Tn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function Ri(){var t,e,n,o;return t=$u,"with"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Gr)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function Ii(){var t,e,n,o;return t=$u,"by"===r.substr($u,2).toLowerCase()?(e=r.substr($u,2),$u+=2):(e=u,0===qu&&zu(In)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function Ni(){var t,e,n,o;return t=$u,"asc"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(jn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="ASC"):($u=t,t=u)):($u=t,t=u),t}function _i(){var t,e,n,o;return t=$u,"desc"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(xn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="DESC"):($u=t,t=u)):($u=t,t=u),t}function Oi(){var t,e,n,o;return t=$u,"all"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Un)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="ALL"):($u=t,t=u)):($u=t,t=u),t}function gi(){var t,e,n,o;return t=$u,"distinct"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(kn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="DISTINCT"):($u=t,t=u)):($u=t,t=u),t}function ji(){var t,e,n,o;return t=$u,"between"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Mn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="BETWEEN"):($u=t,t=u)):($u=t,t=u),t}function xi(){var t,e,n,o;return t=$u,"in"===r.substr($u,2).toLowerCase()?(e=r.substr($u,2),$u+=2):(e=u,0===qu&&zu(Ar)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="IN"):($u=t,t=u)):($u=t,t=u),t}function Ui(){var t,e,n,o;return t=$u,"is"===r.substr($u,2).toLowerCase()?(e=r.substr($u,2),$u+=2):(e=u,0===qu&&zu(Dn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="IS"):($u=t,t=u)):($u=t,t=u),t}function ki(){var t,e,n,o;return t=$u,"like"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Pn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="LIKE"):($u=t,t=u)):($u=t,t=u),t}function Mi(){var t,e,n,o;return t=$u,"similar"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Gn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SIMILAR"):($u=t,t=u)):($u=t,t=u),t}function Di(){var t,e,n,o;return t=$u,"exists"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(Fn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="EXISTS"):($u=t,t=u)):($u=t,t=u),t}function Pi(){var t,e,n,o;return t=$u,"not"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(rr)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="NOT"):($u=t,t=u)):($u=t,t=u),t}function Gi(){var t,e,n,o;return t=$u,"and"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Hn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="AND"):($u=t,t=u)):($u=t,t=u),t}function Fi(){var t,e,n,o;return t=$u,"or"===r.substr($u,2).toLowerCase()?(e=r.substr($u,2),$u+=2):(e=u,0===qu&&zu(Bn)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="OR"):($u=t,t=u)):($u=t,t=u),t}function Hi(){var t,e,n,o;return t=$u,"extract"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(ro)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="EXTRACT"):($u=t,t=u)):($u=t,t=u),t}function Bi(){var t,e,n,o;return t=$u,"case"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(eo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function Yi(){var t,e,n,o;return t=$u,"when"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(no)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?t=e=[e,n]:($u=t,t=u)):($u=t,t=u),t}function $i(){var t,e,n,o;return t=$u,"cast"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(so)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="CAST"):($u=t,t=u)):($u=t,t=u),t}function Vi(){var t,e,n,o;return t=$u,"try_cast"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(io)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="TRY_CAST"):($u=t,t=u)):($u=t,t=u),t}function Wi(){var t,e,n,o;return t=$u,"char"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(fo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="CHAR"):($u=t,t=u)):($u=t,t=u),t}function Xi(){var t,e,n,o;return t=$u,"varchar"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(po)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="VARCHAR"):($u=t,t=u)):($u=t,t=u),t}function qi(){var t,e,n,o;return t=$u,"numeric"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(vo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="NUMERIC"):($u=t,t=u)):($u=t,t=u),t}function Ki(){var t,e,n,o;return t=$u,"decimal"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(yo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="DECIMAL"):($u=t,t=u)):($u=t,t=u),t}function Qi(){var t,e,n,o;return t=$u,"unsigned"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(ho)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="UNSIGNED"):($u=t,t=u)):($u=t,t=u),t}function Ji(){var t,e,n,o;return t=$u,"int"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Co)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="INT"):($u=t,t=u)):($u=t,t=u),t}function Zi(){var t,e,n,o;return t=$u,"integer"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(wo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="INTEGER"):($u=t,t=u)):($u=t,t=u),t}function zi(){var t,e,n,o;return t=$u,"smallint"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(So)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="SMALLINT"):($u=t,t=u)):($u=t,t=u),t}function rc(){var t,e,n,o;return t=$u,"tinyint"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(Ro)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="TINYINT"):($u=t,t=u)):($u=t,t=u),t}function tc(){var t,e,n,o;return t=$u,"bigint"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(go)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="BIGINT"):($u=t,t=u)):($u=t,t=u),t}function ec(){var t,e,n,o;return t=$u,"float"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(jo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="FLOAT"):($u=t,t=u)):($u=t,t=u),t}function nc(){var t,e,n,o;return t=$u,"double"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(xo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="DOUBLE"):($u=t,t=u)):($u=t,t=u),t}function oc(){var t,e,n,o;return t=$u,"date"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Bt)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="DATE"):($u=t,t=u)):($u=t,t=u),t}function uc(){var t,e,n,o;return t=$u,"datetime"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(Uo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="DATETIME"):($u=t,t=u)):($u=t,t=u),t}function ac(){var t,e,n,o;return t=$u,"time"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(ko)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="TIME"):($u=t,t=u)):($u=t,t=u),t}function sc(){var t,e,n,o;return t=$u,"timestamp"===r.substr($u,9).toLowerCase()?(e=r.substr($u,9),$u+=9):(e=u,0===qu&&zu(Mo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="TIMESTAMP"):($u=t,t=u)):($u=t,t=u),t}function ic(){var t,e,n,o;return t=$u,"truncate"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(Do)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="TRUNCATE"):($u=t,t=u)):($u=t,t=u),t}function cc(){var t,e,n,o;return t=$u,"interval"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(Yo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="INTERVAL"):($u=t,t=u)):($u=t,t=u),t}function lc(){var t,e,n,o;return t=$u,"current_timestamp"===r.substr($u,17).toLowerCase()?(e=r.substr($u,17),$u+=17):(e=u,0===qu&&zu(Vo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="CURRENT_TIMESTAMP"):($u=t,t=u)):($u=t,t=u),t}function fc(){var t;return(t=function(){var t;return"@@"===r.substr($u,2)?(t="@@",$u+=2):(t=u,0===qu&&zu(tu)),t}())===u&&(t=function(){var t;return 64===r.charCodeAt($u)?(t="@",$u++):(t=u,0===qu&&zu(ru)),t}())===u&&(t=function(){var t;return 36===r.charCodeAt($u)?(t="$",$u++):(t=u,0===qu&&zu(pt)),t}()),t}function pc(){var t;return"::"===r.substr($u,2)?(t="::",$u+=2):(t=u,0===qu&&zu(ou)),t}function bc(){var t;return 61===r.charCodeAt($u)?(t="=",$u++):(t=u,0===qu&&zu(Vr)),t}function vc(){var t,e,n,o;return t=$u,"add"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(au)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="ADD"):($u=t,t=u)):($u=t,t=u),t}function dc(){var t,e,n,o;return t=$u,"column"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(su)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="COLUMN"):($u=t,t=u)):($u=t,t=u),t}function yc(){var t,e,n,o;return t=$u,"index"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(iu)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="INDEX"):($u=t,t=u)):($u=t,t=u),t}function Ec(){var t,e,n,o;return t=$u,"key"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(h)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="KEY"):($u=t,t=u)):($u=t,t=u),t}function hc(){var t,e,n,o;return t=$u,"unique"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(E)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="UNIQUE"):($u=t,t=u)):($u=t,t=u),t}function Cc(){var t,e,n,o;return t=$u,"comment"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(fu)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="COMMENT"):($u=t,t=u)):($u=t,t=u),t}function Lc(){var t,e,n,o;return t=$u,"constraint"===r.substr($u,10).toLowerCase()?(e=r.substr($u,10),$u+=10):(e=u,0===qu&&zu(pu)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="CONSTRAINT"):($u=t,t=u)):($u=t,t=u),t}function wc(){var t;return 46===r.charCodeAt($u)?(t=".",$u++):(t=u,0===qu&&zu(Ne)),t}function mc(){var t;return 44===r.charCodeAt($u)?(t=",",$u++):(t=u,0===qu&&zu(wu)),t}function Ac(){var t;return 42===r.charCodeAt($u)?(t="*",$u++):(t=u,0===qu&&zu(ct)),t}function Tc(){var t;return 40===r.charCodeAt($u)?(t="(",$u++):(t=u,0===qu&&zu(xr)),t}function Sc(){var t;return 41===r.charCodeAt($u)?(t=")",$u++):(t=u,0===qu&&zu(Ur)),t}function Rc(){var t;return 60===r.charCodeAt($u)?(t="<",$u++):(t=u,0===qu&&zu(Zr)),t}function Ic(){var t;return 62===r.charCodeAt($u)?(t=">",$u++):(t=u,0===qu&&zu(Kr)),t}function Nc(){var t;return 59===r.charCodeAt($u)?(t=";",$u++):(t=u,0===qu&&zu(Tu)),t}function _c(){var t;return"->"===r.substr($u,2)?(t="->",$u+=2):(t=u,0===qu&&zu(Su)),t}function Oc(){var t;return"->>"===r.substr($u,3)?(t="->>",$u+=3):(t=u,0===qu&&zu(Ru)),t}function gc(){var t;return"||"===r.substr($u,2)?(t="||",$u+=2):(t=u,0===qu&&zu(_u)),t}function jc(){var t;return(t=gc())===u&&(t=function(){var t;return"&&"===r.substr($u,2)?(t="&&",$u+=2):(t=u,0===qu&&zu(Ou)),t}()),t}function xc(){var r,t;for(r=[],(t=Pc())===u&&(t=kc());t!==u;)r.push(t),(t=Pc())===u&&(t=kc());return r}function Uc(){var r,t;if(r=[],(t=Pc())===u&&(t=kc()),t!==u)for(;t!==u;)r.push(t),(t=Pc())===u&&(t=kc());else r=u;return r}function kc(){var t;return(t=function(){var t,e,n,o,a,s;t=$u,"/*"===r.substr($u,2)?(e="/*",$u+=2):(e=u,0===qu&&zu(gu));if(e!==u){for(n=[],o=$u,a=$u,qu++,"*/"===r.substr($u,2)?(s="*/",$u+=2):(s=u,0===qu&&zu(ju)),qu--,s===u?a=void 0:($u=a,a=u),a!==u&&(s=Dc())!==u?o=a=[a,s]:($u=o,o=u);o!==u;)n.push(o),o=$u,a=$u,qu++,"*/"===r.substr($u,2)?(s="*/",$u+=2):(s=u,0===qu&&zu(ju)),qu--,s===u?a=void 0:($u=a,a=u),a!==u&&(s=Dc())!==u?o=a=[a,s]:($u=o,o=u);n!==u?("*/"===r.substr($u,2)?(o="*/",$u+=2):(o=u,0===qu&&zu(ju)),o!==u?t=e=[e,n,o]:($u=t,t=u)):($u=t,t=u)}else $u=t,t=u;return t}())===u&&(t=function(){var t,e,n,o,a,s;t=$u,"--"===r.substr($u,2)?(e="--",$u+=2):(e=u,0===qu&&zu(xu));if(e!==u){for(n=[],o=$u,a=$u,qu++,s=Gc(),qu--,s===u?a=void 0:($u=a,a=u),a!==u&&(s=Dc())!==u?o=a=[a,s]:($u=o,o=u);o!==u;)n.push(o),o=$u,a=$u,qu++,s=Gc(),qu--,s===u?a=void 0:($u=a,a=u),a!==u&&(s=Dc())!==u?o=a=[a,s]:($u=o,o=u);n!==u?t=e=[e,n]:($u=t,t=u)}else $u=t,t=u;return t}()),t}function Mc(){var r,t,e,n,o,a,s;return r=$u,(t=Cc())!==u&&xc()!==u?((e=bc())===u&&(e=null),e!==u&&xc()!==u&&(n=Ws())!==u?(r,a=e,s=n,r=t={type:(o=t).toLowerCase(),keyword:o.toLowerCase(),symbol:a,value:s}):($u=r,r=u)):($u=r,r=u),r}function Dc(){var t;return r.length>$u?(t=r.charAt($u),$u++):(t=u,0===qu&&zu(Uu)),t}function Pc(){var t;return Hu.test(r.charAt($u))?(t=r.charAt($u),$u++):(t=u,0===qu&&zu(Bu)),t}function Gc(){var t,e;if((t=function(){var t,e;t=$u,qu++,r.length>$u?(e=r.charAt($u),$u++):(e=u,0===qu&&zu(Uu));qu--,e===u?t=void 0:($u=t,t=u);return t}())===u)if(t=[],Re.test(r.charAt($u))?(e=r.charAt($u),$u++):(e=u,0===qu&&zu(Ie)),e!==u)for(;e!==u;)t.push(e),Re.test(r.charAt($u))?(e=r.charAt($u),$u++):(e=u,0===qu&&zu(Ie));else t=u;return t}function Fc(){var t,e;return t=$u,$u,il=[],(!0?void 0:u)!==u&&xc()!==u?((e=Hc())===u&&(e=function(){var t,e;t=$u,function(){var t;return"return"===r.substr($u,6).toLowerCase()?(t=r.substr($u,6),$u+=6):(t=u,0===qu&&zu(eu)),t}()!==u&&xc()!==u&&(e=Bc())!==u?(t,t={type:"return",expr:e}):($u=t,t=u);return t}()),e!==u?(t,t={type:"proc",stmt:e,vars:il}):($u=t,t=u)):($u=t,t=u),t}function Hc(){var t,e,n,o;return t=$u,(e=Kc())===u&&(e=Qc()),e!==u&&xc()!==u?((n=function(){var t;return":="===r.substr($u,2)?(t=":=",$u+=2):(t=u,0===qu&&zu(nu)),t}())===u&&(n=bc()),n!==u&&xc()!==u&&(o=Bc())!==u?(t,t=e={type:"assign",left:e,symbol:n,right:o}):($u=t,t=u)):($u=t,t=u),t}function Bc(){var t;return(t=Aa())===u&&(t=function(){var r,t,e,n,o;r=$u,(t=Kc())!==u&&xc()!==u&&(e=Fa())!==u&&xc()!==u&&(n=Kc())!==u&&xc()!==u&&(o=Ya())!==u?(r,r=t={type:"join",ltable:t,rtable:n,op:e,on:o}):($u=r,r=u);return r}())===u&&(t=Yc())===u&&(t=function(){var t,e;t=$u,function(){var t;return 91===r.charCodeAt($u)?(t="[",$u++):(t=u,0===qu&&zu(mu)),t}()!==u&&xc()!==u&&(e=qc())!==u&&xc()!==u&&function(){var t;return 93===r.charCodeAt($u)?(t="]",$u++):(t=u,0===qu&&zu(Au)),t}()!==u?(t,t={type:"array",value:e}):($u=t,t=u);return t}()),t}function Yc(){var r,t,e,n,o,a,s,i;if(r=$u,(t=$c())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=Cs())!==u&&(s=xc())!==u&&(i=$c())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=Cs())!==u&&(s=xc())!==u&&(i=$c())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,r=t=Wr(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function $c(){var r,t,e,n,o,a,s,i;if(r=$u,(t=Vc())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=ws())!==u&&(s=xc())!==u&&(i=Vc())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=ws())!==u&&(s=xc())!==u&&(i=Vc())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,r=t=Wr(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function Vc(){var r,t,e;return(r=$s())===u&&(r=Kc())===u&&(r=Xc())===u&&(r=xs())===u&&(r=$u,Tc()!==u&&xc()!==u&&(t=Yc())!==u&&xc()!==u&&Sc()!==u?(r,(e=t).parentheses=!0,r=e):($u=r,r=u)),r}function Wc(){var r,t,e,n,o,a,s;return r=$u,(t=_s())!==u?(e=$u,(n=xc())!==u&&(o=wc())!==u&&(a=xc())!==u&&(s=_s())!==u?e=n=[n,o,a,s]:($u=e,e=u),e===u&&(e=null),e!==u?(r,r=t=function(r,t){let e=r;return null!==t&&(e=`${r}.${t[3]}`),e}(t,e)):($u=r,r=u)):($u=r,r=u),r}function Xc(){var r,t,e;return r=$u,(t=Wc())!==u&&xc()!==u&&Tc()!==u&&xc()!==u?((e=qc())===u&&(e=null),e!==u&&xc()!==u&&Sc()!==u?(r,r=t={type:"function",name:t,args:{type:"expr_list",value:e}}):($u=r,r=u)):($u=r,r=u),r===u&&(r=$u,(t=Wc())!==u&&(r,t=function(r){return{type:"function",name:r,args:null}}(t)),r=t),r}function qc(){var r,t,e,n,o,a,s,i;if(r=$u,(t=Vc())!==u){for(e=[],n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Vc())!==u?n=o=[o,a,s,i]:($u=n,n=u);n!==u;)e.push(n),n=$u,(o=xc())!==u&&(a=mc())!==u&&(s=xc())!==u&&(i=Vc())!==u?n=o=[o,a,s,i]:($u=n,n=u);e!==u?(r,r=t=nl(t,e)):($u=r,r=u)}else $u=r,r=u;return r}function Kc(){var r,t,e,n,o;return r=$u,(t=fc())!==u&&(e=Qc())!==u?(r,n=t,o=e,r=t={type:"var",...o,prefix:n}):($u=r,r=u),r}function Qc(){var t,e,n,o,a;return t=$u,(e=_s())!==u&&(n=function(){var t,e,n,o,a;t=$u,e=[],n=$u,46===r.charCodeAt($u)?(o=".",$u++):(o=u,0===qu&&zu(Ne));o!==u&&(a=_s())!==u?n=o=[o,a]:($u=n,n=u);for(;n!==u;)e.push(n),n=$u,46===r.charCodeAt($u)?(o=".",$u++):(o=u,0===qu&&zu(Ne)),o!==u&&(a=_s())!==u?n=o=[o,a]:($u=n,n=u);e!==u&&(t,e=function(r){const t=[];for(let e=0;e<r.length;e++)t.push(r[e][1]);return t}(e));return t=e}())!==u?(t,o=e,a=n,il.push(o),t=e={type:"var",name:o,members:a,prefix:null}):($u=t,t=u),t===u&&(t=$u,(e=Qs())!==u&&(t,e={type:"var",name:e.value,members:[],quoted:null,prefix:null}),t=e),t}function Jc(){var t;return(t=function(){var t,e,n,o;t=$u,(e=Wi())===u&&(e=Xi());if(e!==u)if(xc()!==u)if(Tc()!==u)if(xc()!==u){if(n=[],_e.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(Oe)),o!==u)for(;o!==u;)n.push(o),_e.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(Oe));else n=u;n!==u&&(o=xc())!==u&&Sc()!==u?(t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):($u=t,t=u)}else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;t===u&&(t=$u,(e=Wi())!==u&&(t,e=function(r){return{dataType:r}}(e)),(t=e)===u&&(t=$u,(e=Xi())!==u&&(t,e=Yu(e)),(t=e)===u&&(t=$u,(e=function(){var t,e,n,o;return t=$u,"string"===r.substr($u,6).toLowerCase()?(e=r.substr($u,6),$u+=6):(e=u,0===qu&&zu(bo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="STRING"):($u=t,t=u)):($u=t,t=u),t}())!==u&&(t,e=function(r){return{dataType:r}}(e)),t=e)));return t}())===u&&(t=function(){var t,e,n,o,a,s,i,c,l,f,p,b;t=$u,(e=qi())===u&&(e=Ki())===u&&(e=Ji())===u&&(e=Zi())===u&&(e=zi())===u&&(e=rc())===u&&(e=tc())===u&&(e=ec())===u&&(e=nc());if(e!==u)if((n=xc())!==u)if((o=Tc())!==u)if((a=xc())!==u){if(s=[],_e.test(r.charAt($u))?(i=r.charAt($u),$u++):(i=u,0===qu&&zu(Oe)),i!==u)for(;i!==u;)s.push(i),_e.test(r.charAt($u))?(i=r.charAt($u),$u++):(i=u,0===qu&&zu(Oe));else s=u;if(s!==u)if((i=xc())!==u){if(c=$u,(l=mc())!==u)if((f=xc())!==u){if(p=[],_e.test(r.charAt($u))?(b=r.charAt($u),$u++):(b=u,0===qu&&zu(Oe)),b!==u)for(;b!==u;)p.push(b),_e.test(r.charAt($u))?(b=r.charAt($u),$u++):(b=u,0===qu&&zu(Oe));else p=u;p!==u?c=l=[l,f,p]:($u=c,c=u)}else $u=c,c=u;else $u=c,c=u;c===u&&(c=null),c!==u&&(l=xc())!==u&&(f=Sc())!==u&&(p=xc())!==u?((b=Zc())===u&&(b=null),b!==u?(t,v=c,d=b,e={dataType:e,length:parseInt(s.join(""),10),scale:v&&parseInt(v[2].join(""),10),parentheses:!0,suffix:d},t=e):($u=t,t=u)):($u=t,t=u)}else $u=t,t=u;else $u=t,t=u}else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;var v,d;if(t===u){if(t=$u,(e=qi())===u&&(e=Ki())===u&&(e=Ji())===u&&(e=Zi())===u&&(e=zi())===u&&(e=rc())===u&&(e=tc())===u&&(e=ec())===u&&(e=nc()),e!==u){if(n=[],_e.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(Oe)),o!==u)for(;o!==u;)n.push(o),_e.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(Oe));else n=u;n!==u&&(o=xc())!==u?((a=Zc())===u&&(a=null),a!==u?(t,e=function(r,t,e){return{dataType:r,length:parseInt(t.join(""),10),suffix:e}}(e,n,a),t=e):($u=t,t=u)):($u=t,t=u)}else $u=t,t=u;t===u&&(t=$u,(e=qi())===u&&(e=Ki())===u&&(e=Ji())===u&&(e=Zi())===u&&(e=zi())===u&&(e=rc())===u&&(e=tc())===u&&(e=ec())===u&&(e=nc()),e!==u&&(n=xc())!==u?((o=Zc())===u&&(o=null),o!==u&&(a=xc())!==u?(t,e=function(r,t){return{dataType:r,suffix:t}}(e,o),t=e):($u=t,t=u)):($u=t,t=u))}return t}())===u&&(t=function(){var t,e,n,o;t=$u,(e=oc())===u&&(e=uc())===u&&(e=ac())===u&&(e=sc());if(e!==u)if(xc()!==u)if(Tc()!==u)if(xc()!==u){if(n=[],_e.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(Oe)),o!==u)for(;o!==u;)n.push(o),_e.test(r.charAt($u))?(o=r.charAt($u),$u++):(o=u,0===qu&&zu(Oe));else n=u;n!==u&&(o=xc())!==u&&Sc()!==u?(t,e={dataType:e,length:parseInt(n.join(""),10),parentheses:!0},t=e):($u=t,t=u)}else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;else $u=t,t=u;t===u&&(t=$u,(e=oc())===u&&(e=uc())===u&&(e=ac())===u&&(e=sc()),e!==u&&(t,e=Yu(e)),t=e);return t}())===u&&(t=function(){var t,e;t=$u,(e=function(){var t,e,n,o;return t=$u,"json"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(mo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="JSON"):($u=t,t=u)):($u=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=$u,"jsonb"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Ao)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="JSONB"):($u=t,t=u)):($u=t,t=u),t}());e!==u&&(t,e=Yu(e));return t=e}())===u&&(t=function(){var t,e;t=$u,(e=function(){var t,e,n,o;return t=$u,"geometry"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(To)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="GEOMETRY"):($u=t,t=u)):($u=t,t=u),t}())!==u&&(t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e;t=$u,(e=function(){var t,e,n,o;return t=$u,"tinytext"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(Io)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="TINYTEXT"):($u=t,t=u)):($u=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=$u,"text"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(No)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="TEXT"):($u=t,t=u)):($u=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=$u,"mediumtext"===r.substr($u,10).toLowerCase()?(e=r.substr($u,10),$u+=10):(e=u,0===qu&&zu(_o)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="MEDIUMTEXT"):($u=t,t=u)):($u=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=$u,"longtext"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(Oo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="LONGTEXT"):($u=t,t=u)):($u=t,t=u),t}());e!==u&&(t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e;t=$u,(e=function(){var t,e,n,o;return t=$u,"uuid"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(Go)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="UUID"):($u=t,t=u)):($u=t,t=u),t}())!==u&&(t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e;t=$u,(e=function(){var t,e,n,o;return t=$u,"bool"===r.substr($u,4).toLowerCase()?(e=r.substr($u,4),$u+=4):(e=u,0===qu&&zu(co)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="BOOL"):($u=t,t=u)):($u=t,t=u),t}())===u&&(e=function(){var t,e,n,o;return t=$u,"boolean"===r.substr($u,7).toLowerCase()?(e=r.substr($u,7),$u+=7):(e=u,0===qu&&zu(lo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="BOOLEAN"):($u=t,t=u)):($u=t,t=u),t}());e!==u&&(t,e={dataType:e});return t=e}())===u&&(t=function(){var t,e,n;t=$u,(e=function(){var t,e,n,o;return t=$u,"array"===r.substr($u,5).toLowerCase()?(e=r.substr($u,5),$u+=5):(e=u,0===qu&&zu(Fo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="ARRAY"):($u=t,t=u)):($u=t,t=u),t}())!==u&&Rc()!==u&&(n=Jc())!==u&&Ic()!==u?(t,t=e={dataType:e,subType:n}):($u=t,t=u);return t}())===u&&(t=function(){var t,e,n;t=$u,(e=function(){var t,e,n,o;return t=$u,"map"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(Ho)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="MAP"):($u=t,t=u)):($u=t,t=u),t}())!==u&&Rc()!==u&&Jc()!==u&&mc()!==u&&(n=Jc())!==u&&Ic()!==u?(t,t=e={dataType:e,subType:n}):($u=t,t=u);return t}())===u&&(t=function(){var t,e;t=$u,(e=function(){var t,e,n,o;return t=$u,"row"===r.substr($u,3).toLowerCase()?(e=r.substr($u,3),$u+=3):(e=u,0===qu&&zu(ar)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="ROW"):($u=t,t=u)):($u=t,t=u),t}())!==u&&(t,e={dataType:e});return t=e}()),t}function Zc(){var t,e,n;return t=$u,(e=Qi())===u&&(e=null),e!==u&&xc()!==u?((n=function(){var t,e,n,o;return t=$u,"zerofill"===r.substr($u,8).toLowerCase()?(e=r.substr($u,8),$u+=8):(e=u,0===qu&&zu(Lo)),e!==u?(n=$u,qu++,o=Os(),qu--,o===u?n=void 0:($u=n,n=u),n!==u?(t,t=e="ZEROFILL"):($u=t,t=u)):($u=t,t=u),t}())===u&&(n=null),n!==u?(t,t=e=function(r,t){const e=[];return r&&e.push(r),t&&e.push(t),e}(e,n)):($u=t,t=u)):($u=t,t=u),t}const zc={ABS:!0,ALL:!0,ALLOCATE:!0,ALLOW:!0,ALTER:!0,AND:!0,ANY:!0,ARE:!0,ARRAY:!0,ARRAY_MAX_CARDINALITY:!0,AS:!0,ASENSITIVE:!0,ASYMMETRIC:!0,AT:!0,ATOMIC:!0,AUTHORIZATION:!0,AVG:!0,BEGIN:!0,BEGIN_FRAME:!0,BEGIN_PARTITION:!0,BETWEEN:!0,BIGINT:!0,BINARY:!0,BIT:!0,BLOB:!0,BOOLEAN:!0,BOTH:!0,BY:!0,CALL:!0,CALLED:!0,CARDINALITY:!0,CASCADED:!0,CASE:!0,CAST:!0,CEIL:!0,CEILING:!0,CHAR:!0,CHARACTER:!0,CHARACTER_LENGTH:!0,CHAR_LENGTH:!0,CHECK:!0,CLASSIFIER:!0,CLOB:!0,CLOSE:!0,COALESCE:!0,COLLATE:!0,COLLECT:!0,COLUMN:!0,COMMIT:!0,CONDITION:!0,CONNECT:!0,CONSTRAINT:!0,CONTAINS:!0,CONVERT:!0,CORR:!0,CORRESPONDING:!0,COUNT:!0,COVAR_POP:!0,COVAR_SAMP:!0,CREATE:!0,CROSS:!0,CUBE:!0,CUME_DIST:!0,CURRENT:!0,CURRENT_CATALOG:!0,CURRENT_DATE:!0,CURRENT_DEFAULT_TRANSFORM_GROUP:!0,CURRENT_PATH:!0,CURRENT_ROLE:!0,CURRENT_ROW:!0,CURRENT_SCHEMA:!0,CURRENT_TIME:!0,CURRENT_TIMESTAMP:!0,CURRENT_TRANSFORM_GROUP_FOR_TYPE:!0,CURRENT_USER:!0,CURSOR:!0,CYCLE:!0,DATE:!0,DAY:!0,DEALLOCATE:!0,DEC:!0,DECIMAL:!0,DECLARE:!0,DEFAULT:!0,DEFINE:!0,DELETE:!0,DENSE_RANK:!0,DEREF:!0,DESCRIBE:!0,DETERMINISTIC:!0,DISALLOW:!0,DISCONNECT:!0,DISTINCT:!0,DOUBLE:!0,DROP:!0,DYNAMIC:!0,EACH:!0,ELEMENT:!0,ELSE:!0,EMPTY:!0,END:!0,"END-EXEC":!0,END_FRAME:!0,END_PARTITION:!0,EQUALS:!0,ESCAPE:!0,EVERY:!0,EXCEPT:!0,EXEC:!0,EXECUTE:!0,EXISTS:!0,EXP:!0,EXPLAIN:!0,EXTEND:!0,EXTERNAL:!0,EXTRACT:!0,FALSE:!0,FETCH:!0,FILTER:!0,FIRST_VALUE:!0,FLOAT:!0,FLOOR:!0,FOR:!0,FOREIGN:!0,FRAME_ROW:!0,FREE:!0,FROM:!0,FULL:!0,FUNCTION:!0,FUSION:!0,GET:!0,GLOBAL:!0,GRANT:!0,GROUP:!0,GROUPING:!0,GROUPS:!0,HAVING:!0,HOLD:!0,HOUR:!0,IDENTITY:!0,IMPORT:!0,IN:!0,INDICATOR:!0,INITIAL:!0,INNER:!0,INOUT:!0,INSENSITIVE:!0,INSERT:!0,INT:!0,INTEGER:!0,INTERSECT:!0,INTERSECTION:!0,INTERVAL:!0,INTO:!0,IS:!0,JOIN:!0,JSON_ARRAY:!0,JSON_ARRAYAGG:!0,JSON_EXISTS:!0,JSON_OBJECT:!0,JSON_OBJECTAGG:!0,JSON_QUERY:!0,JSON_VALUE:!0,LAG:!0,LANGUAGE:!0,LARGE:!0,LAST_VALUE:!0,LATERAL:!0,LEAD:!0,LEADING:!0,LEFT:!0,LIKE:!0,LIKE_REGEX:!0,LIMIT:!0,LN:!0,LOCAL:!0,LOCALTIME:!0,LOCALTIMESTAMP:!0,LOWER:!0,MATCH:!0,MATCHES:!0,MATCH_NUMBER:!0,MATCH_RECOGNIZE:!0,MAX:!0,MEASURES:!0,MEMBER:!0,MERGE:!0,METHOD:!0,MIN:!0,MINUS:!0,MINUTE:!0,MOD:!0,MODIFIES:!0,MODULE:!0,MONTH:!0,MULTISET:!0,NATIONAL:!0,NATURAL:!0,NCHAR:!0,NCLOB:!0,NEW:!0,NEXT:!0,NO:!0,NONE:!0,NORMALIZE:!0,NOT:!0,NTH_VALUE:!0,NTILE:!0,NULL:!0,NULLIF:!0,NUMERIC:!0,OCCURRENCES_REGEX:!0,OCTET_LENGTH:!0,OF:!0,OFFSET:!0,OLD:!0,OMIT:!0,ON:!0,ONE:!0,ONLY:!0,OPEN:!0,OR:!0,ORDER:!0,OUT:!0,OUTER:!0,OVER:!0,OVERLAPS:!0,OVERLAY:!0,PARAMETER:!0,PARTITION:!0,PATTERN:!0,PER:!0,PERCENT:!0,PERCENTILE_CONT:!0,PERCENTILE_DISC:!0,PERCENT_RANK:!0,PERIOD:!0,PERMUTE:!0,PORTION:!0,POSITION:!0,POSITION_REGEX:!0,POWER:!0,PRECEDES:!0,PRECISION:!0,PREPARE:!0,PREV:!0,PRIMARY:!0,PROCEDURE:!0,RANGE:!0,RANK:!0,READS:!0,REAL:!0,RECURSIVE:!0,REF:!0,REFERENCES:!0,REFERENCING:!0,REGR_AVGX:!0,REGR_AVGY:!0,REGR_COUNT:!0,REGR_INTERCEPT:!0,REGR_R2:!0,REGR_SLOPE:!0,REGR_SXX:!0,REGR_SXY:!0,REGR_SYY:!0,RELEASE:!0,RESET:!0,RESULT:!0,RETURN:!0,RETURNS:!0,REVOKE:!0,RIGHT:!0,ROLLBACK:!0,ROLLUP:!0,ROW:!0,ROWS:!0,ROW_NUMBER:!0,RUNNING:!0,SAVEPOINT:!0,SCOPE:!0,SCROLL:!0,SEARCH:!0,SECOND:!0,SEEK:!0,SELECT:!0,SENSITIVE:!0,SESSION_USER:!0,SET:!0,SHOW:!0,SIMILAR:!0,SIMILAR:!0,SKIP:!0,SMALLINT:!0,SOME:!0,SPECIFIC:!0,SPECIFICTYPE:!0,SQL:!0,SQLEXCEPTION:!0,SQLSTATE:!0,SQLWARNING:!0,SQRT:!0,START:!0,STATIC:!0,STDDEV_POP:!0,STDDEV_SAMP:!0,STREAM:!0,SUBMULTISET:!0,SUBSET:!0,SUBSTRING:!0,SUBSTRING_REGEX:!0,SUCCEEDS:!0,SUM:!0,SYMMETRIC:!0,SYSTEM:!0,SYSTEM_TIME:!0,SYSTEM_USER:!0,TABLE:!0,TABLESAMPLE:!0,THEN:!0,TO:!0,TIME:!0,TIMESTAMP:!0,TIMEZONE_HOUR:!0,TIMEZONE_MINUTE:!0,TINYINT:!0,TO:!0,TRAILING:!0,TRANSLATE:!0,TRANSLATE_REGEX:!0,TRANSLATION:!0,TREAT:!0,TRIGGER:!0,TRIM:!0,TRIM_ARRAY:!0,TRUE:!0,TRUNCATE:!0,UESCAPE:!0,UNION:!0,UNIQUE:!0,UNKNOWN:!0,UNNEST:!0,UPDATE:!0,UPPER:!0,UPSERT:!0,USER:!0,USING:!0,VALUE:!0,VALUES:!0,VALUE_OF:!0,VARBINARY:!0,VARCHAR:!0,VARYING:!0,VAR_POP:!0,VAR_SAMP:!0,VERSIONING:!0,WHEN:!0,WHENEVER:!0,WHERE:!0,WIDTH_BUCKET:!0,WINDOW:!0,WITH:!0,WITHIN:!0,WITHOUT:!0,YEAR:!0};function rl(r,t){return{type:"unary_expr",operator:r,expr:t}}function tl(r,t,e){return{type:"binary_expr",operator:r,left:t,right:e}}function el(r){const t=n(Number.MAX_SAFE_INTEGER);return!(n(r)<t)}function nl(r,t,e=3){const n=[r];for(let r=0;r<t.length;r++)delete t[r][e].tableList,delete t[r][e].columnList,n.push(t[r][e]);return n}function ol(r,t){let e=r;for(let r=0;r<t.length;r++)e=tl(t[r][1],e,t[r][3]);return e}function ul(r){const t=fl[r];return t||(r||null)}function al(r){const t=new Set;for(let e of r.keys()){const r=e.split("::");if(!r){t.add(e);break}r&&r[1]&&(r[1]=ul(r[1])),t.add(r.join("::"))}return Array.from(t)}function sl(r){return"string"==typeof r?{type:"same",value:r}:r}let il=[];const cl=new Set,ll=new Set,fl={};if((e=s())!==u&&$u===r.length)return e;throw e!==u&&$u<r.length&&zu({type:"end"}),ra(Xu,Wu<r.length?r.charAt(Wu):null,Wu<r.length?Zu(Wu,Wu+1):Zu(Wu,Wu))}}},function(r,t,e){r.exports=e(3)},function(r,t){r.exports=require("big-integer")},function(r,t,e){"use strict";e.r(t),e.d(t,"Parser",(function(){return Lt})),e.d(t,"util",(function(){return n}));var n={};function o(r){return function(r){if(Array.isArray(r))return u(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return u(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return u(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function a(r){if(!r)return[];var t=r.keyword,e=r.type;return[t.toUpperCase(),br(e)]}function s(r){if(r){var t=r.type,e=r.expr,n=r.symbol,u=t.toUpperCase(),s=[];switch(s.push(u),u){case"KEY_BLOCK_SIZE":n&&s.push(n),s.push(dr(e));break;case"BTREE":case"HASH":s.length=0,s.push.apply(s,o(a(r)));break;case"WITH PARSER":s.push(e);break;case"VISIBLE":case"INVISIBLE":break;case"COMMENT":s.shift(),s.push(Lr(r));break;case"DATA_COMPRESSION":s.push(n,br(e.value),Er(e.on));break;default:s.push(n,dr(e))}return s.filter(vr).join(" ")}}function i(r){return r?r.map(s):[]}function c(r){var t=r.constraint_type,e=r.index_type,n=r.index_options,u=void 0===n?[]:n,s=r.definition,c=r.on,l=r.with,f=[];if(f.push.apply(f,o(a(e))),s&&s.length){var p="CHECK"===br(t)?"(".concat(ut(s[0]),")"):"(".concat(s.map((function(r){return ut(r)})).join(", "),")");f.push(p)}return f.push(i(u).join(" ")),l&&f.push("WITH (".concat(i(l).join(", "),")")),c&&f.push("ON [".concat(c,"]")),f}function l(r){return function(r){if(Array.isArray(r))return f(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return f(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return f(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function p(r){if(r){var t=r.constraint,e=r.constraint_type,n=r.enforced,o=r.index,u=r.keyword,a=r.reference_definition,s=[],i=sr().database;s.push(br(u)),s.push(fr(t));var f=br(e);return"sqlite"===i&&"UNIQUE KEY"===f&&(f="UNIQUE"),s.push(f),s.push("sqlite"!==i&&fr(o)),s.push.apply(s,l(c(r))),s.push.apply(s,l(X(a))),s.push(br(n)),s.filter(vr).join(" ")}}function b(r){if("string"==typeof r)return r;var t=r.window_specification;return"(".concat(function(r){var t=r.name,e=r.partitionby,n=r.orderby,o=r.window_frame_clause;return[t,st(e,"partition by"),st(n,"order by"),br(o)].filter(vr).join(" ")}(t),")")}function v(r){var t=r.name,e=r.as_window_specification;return"".concat(t," AS ").concat(b(e))}function d(r){var t=r.args,e=r.name,n=r.consider_nulls,o=void 0===n?"":n,u=t?ut(t).join(", "):"",a=function(r){switch(br(r)){case"NTH_VALUE":case"LEAD":case"LAG":return!1;default:return!0}}(e);return[e,"(",u,!a&&")",o&&" ",o,a&&")"].filter(vr).join("")}function y(r){if(r){var t=r.as_window_specification,e=r.expr,n=r.keyword,o=r.type,u=r.parentheses,a=br(o);if("WINDOW"===a)return"OVER ".concat(b(t));if("ON UPDATE"===a){var s="".concat(br(o)," ").concat(br(n)),i=ut(e)||[];return u&&(s="".concat(s,"(").concat(i.join(", "),")")),s}throw new Error("unknown over type")}}function E(r){var t=r.arrows,e=void 0===t?[]:t,n=r.collate,o=r.target,u=r.expr,a=r.keyword,s=r.symbol,i=r.as,c=r.properties,l=void 0===c?[]:c,f=o.length,p=o.dataType,b=o.parentheses,v=o.quoted,d=o.scale,y=o.suffix,E="";null!=f&&(E=d?"".concat(f,", ").concat(d):f),b&&(E="(".concat(E,")")),y&&y.length&&(E+=" ".concat(y.join(" ")));var h=ut(u),C="::",L="";return"as"===s&&(h="".concat(br(a),"(").concat(h),L=")",C=" ".concat(s.toUpperCase()," ")),L+=e.map((function(r,t){return er(r,dr,l[t])})).join(" "),i&&(L+=" AS ".concat(fr(i))),n&&(L+=" ".concat(pr(n).join(" "))),[h,C,v,p,v,function(r){if(!r||!r.array)return"";switch(r.array){case"one":return"[]";case"two":return"[][]"}}(o),E,L].filter(vr).join("")}function h(r){var t=r.args,e=r.name,n=r.args_parentheses,o=r.parentheses,u=r.over,a=r.collate,s=r.suffix,i=pr(a).join(" "),c=y(u),l=ut(s);if(!t)return[e,c].filter(vr).join(" ");var f=r.separator||", ";"TRIM"===br(e)&&(f=" ");var p=[e];return p.push(!1===n?" ":"("),p.push(ut(t).join(f)),!1!==n&&p.push(")"),p=[p.join(""),l].filter(vr).join(" "),[o?"(".concat(p,")"):p,i,c].filter(vr).join(" ")}function C(r){var t=r.operator||r.op,e=ut(r.right),n=!1;if(Array.isArray(e)){switch(t){case"=":t="IN";break;case"!=":t="NOT IN";break;case"BETWEEN":case"NOT BETWEEN":n=!0,e="".concat(e[0]," AND ").concat(e[1])}n||(e="(".concat(e.join(", "),")"))}var o=r.right.escape||{},u=[ut(r.left),t,e,br(o.type),ut(o.value)].filter(vr).join(" ");return r.parentheses?"(".concat(u,")"):u}function L(r){return function(r){if(Array.isArray(r))return w(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return w(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return w(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function m(r){return r?[br(r.prefix),ut(r.value),br(r.suffix)]:[]}function A(r){return r?r.fetch?(e=(t=r).fetch,n=t.offset,[].concat(L(m(n)),L(m(e))).filter(vr).join(" ")):function(r){var t=r.seperator,e=r.value;return 1===e.length&&"offset"===t?nr("OFFSET",ut(e[0])):nr("LIMIT",e.map(ut).join("".concat("offset"===t?" ":"").concat(br(t)," ")))}(r):"";var t,e,n}function T(r){if(r&&0!==r.length){var t=r[0].recursive?"RECURSIVE ":"",e=r.map((function(r){var t=r.name,e=r.stmt,n=r.columns,o=Array.isArray(n)?"(".concat(n.map(V).join(", "),")"):"";return"".concat("default"===t.type?fr(t.value):dr(t)).concat(o," AS (").concat(ut(e),")")})).join(", ");return"WITH ".concat(t).concat(e)}}function S(r){if(r&&r.position){var t=r.keyword,e=r.expr,n=[],o=br(t);switch(o){case"VAR":n.push(e.map(ot).join(", "));break;default:n.push(o,"string"==typeof e?fr(e):ut(e))}return n.filter(vr).join(" ")}}function R(r){var t=r.as_struct_val,e=r.columns,n=r.distinct,o=r.for,u=r.from,a=r.for_sys_time_as_of,s=void 0===a?{}:a,i=r.locking_read,c=r.groupby,l=r.having,f=r.into,p=void 0===f?{}:f,b=r.limit,v=r.options,d=r.orderby,y=r.parentheses_symbol,E=r.qualify,h=r.top,C=r.window,L=r.with,w=r.where,m=[T(L),"SELECT",br(t)];m.push(cr(h)),Array.isArray(v)&&m.push(v.join(" ")),m.push(function(r){if(r){if("string"==typeof r)return r;var t=r.type,e=r.columns,n=[br(t)];return e&&n.push("(".concat(e.map(V).join(", "),")")),n.filter(vr).join(" ")}}(n),Z(e,u));var R=p.position,I="";R&&(I=er("INTO",S,p)),"column"===R&&m.push(I),m.push(er("FROM",H,u)),"from"===R&&m.push(I);var N=s||{},_=N.keyword,O=N.expr;m.push(er(_,ut,O)),m.push(er("WHERE",ut,w)),m.push(nr("GROUP BY",at(c).join(", "))),m.push(er("HAVING",ut,l)),m.push(er("QUALIFY",ut,E)),m.push(er("WINDOW",ut,C)),m.push(st(d,"order by")),m.push(A(b)),m.push(br(i)),"end"===R&&m.push(I),m.push(function(r){if(r){var t=r.expr,e=r.keyword,n=[br(r.type),br(e)];return t?"".concat(n.join(" "),"(").concat(ut(t),")"):n.join(" ")}}(o));var g=m.filter(vr).join(" ");return y?"(".concat(g,")"):g}function I(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(!r)return;if("string"==typeof r)return N(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return N(r,t)}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function N(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function _(r){if(!r||0===r.length)return"";var t,e=[],n=I(r);try{for(n.s();!(t=n.n()).done;){var o=t.value,u=o.table,a=o.column,s=o.value,i=[[u,a].filter(vr).map((function(r){return fr(r)})).join(".")],c="";s&&(c=ut(s),i.push("=",c)),e.push(i.filter(vr).join(" "))}}catch(r){n.e(r)}finally{n.f()}return e.join(", ")}function O(r){if("select"===r.type)return R(r);var t=r.map(ut);return"(".concat(t.join("), ("),")")}function g(r){if(!r)return"";var t=["PARTITION","("];if(Array.isArray(r))t.push(r.map(fr).join(", "));else{var e=r.value;t.push(e.map(ut).join(", "))}return t.push(")"),t.filter(vr).join("")}function j(r){if(!r)return"";switch(r.type){case"column":return"(".concat(r.expr.map(V).join(", "),")")}}function x(r){var t=r.expr,e=r.keyword,n=t.type,o=[br(e)];switch(n){case"origin":o.push(dr(t));break;case"update":o.push("UPDATE",er("SET",_,t.set),er("WHERE",ut,t.where))}return o.filter(vr).join(" ")}function U(r){if(!r)return"";var t=r.action;return[j(r.target),x(t)].filter(vr).join(" ")}function k(r){var t=r.table,e=r.type,n=r.prefix,o=void 0===n?"into":n,u=r.columns,a=r.conflict,s=r.values,i=r.where,c=r.on_duplicate_update,l=r.partition,f=r.returning,p=r.set,b=c||{},v=b.keyword,d=b.set,y=[br(e),br(o),H(t),g(l)];return Array.isArray(u)&&y.push("(".concat(u.map(fr).join(", "),")")),y.push(er(Array.isArray(s)?"VALUES":"",O,s)),y.push(er("ON CONFLICT",U,a)),y.push(er("SET",_,p)),y.push(er("WHERE",ut,i)),y.push(mr(f)),y.push(er(v,_,d)),y.filter(vr).join(" ")}function M(r){var t=r.expr,e=r.unit;return["INTERVAL",ut(t),br(e)].filter(vr).join(" ")}function D(r){var t=r.type,e=r.as,n=r.expr,o=r.with_offset;return["".concat(br(t),"(").concat(n&&ut(n)||"",")"),er("AS",fr,e),er(br(o&&o.keyword),fr,o&&o.as)].filter(vr).join(" ")}function P(r){if(r)switch(r.type){case"pivot":case"unpivot":return function(r){var t=r.as,e=r.column,n=r.expr,o=r.in_expr,u=r.type,a=[ut(n),"FOR",V(e),C(o)],s=["".concat(br(u),"(").concat(a.join(" "),")")];return t&&s.push("AS",fr(t)),s.join(" ")}(r);default:return""}}function G(r){if(r){var t=r.keyword,e=r.expr,n=r.index,o=r.index_columns,u=r.parentheses,a=r.prefix,s=[];switch(t.toLowerCase()){case"forceseek":s.push(br(t),"(".concat(fr(n)),"(".concat(o.map(ut).filter(vr).join(", "),"))"));break;case"spatial_window_max_cells":s.push(br(t),"=",ut(e));break;case"index":s.push(br(a),br(t),u?"(".concat(e.map(fr).join(", "),")"):"= ".concat(fr(e)));break;default:s.push(ut(e))}return s.filter(vr).join(" ")}}function F(r){if("UNNEST"===br(r.type))return D(r);var t=r.table,e=r.db,n=r.as,o=r.expr,u=r.operator,a=r.prefix,s=r.schema,i=r.server,c=r.tablesample,l=r.table_hint,f=fr(i),p=fr(e),b=fr(s),v=t&&fr(t);if(o)switch(o.type){case"values":var d=o.parentheses,y=o.values,E=o.prefix,h=[d&&"(","",d&&")"],C=O(y);E&&(C=C.split("(").slice(1).map((function(r){return"".concat(br(E),"(").concat(r)})).join("")),h[1]="VALUES ".concat(C),v=h.filter(vr).join("");break;case"tumble":v=function(r){if(!r)return"";var t=r.data,e=r.timecol,n=r.size;return["TABLE(TUMBLE(TABLE",[fr(t.db),fr(t.table)].filter(vr).join("."),"DESCRIPTOR(".concat(V(e),")"),"".concat(M(n),"))")].filter(vr).join(" ")}(o);break;default:v=ut(o)}var L=[f,p,b,v=[br(a),v].filter(vr).join(" ")].filter(vr).join(".");r.parentheses&&(L="(".concat(L,")"));var w=[L];if(c){var m=["TABLESAMPLE",ut(c.expr),dr(c.repeatable)].filter(vr).join(" ");w.push(m)}return w.push(er("AS",fr,n),P(u)),l&&w.push(br(l.keyword),"(".concat(l.expr.map(G).filter(vr).join(", "),")")),w.filter(vr).join(" ")}function H(r){if(!r)return"";if(!Array.isArray(r)){var t=r.expr,e=r.parentheses,n=H(t);return e?"(".concat(n,")"):n}var o=r[0],u=[];if("dual"===o.type)return"DUAL";u.push(F(o));for(var a=1;a<r.length;++a){var s=r[a],i=s.on,c=s.using,l=s.join,f=[];f.push(l?" ".concat(br(l)):","),f.push(F(s)),f.push(er("ON",ut,i)),c&&f.push("USING (".concat(c.map(fr).join(", "),")")),u.push(f.filter(vr).join(" "))}return u.filter(vr).join("")}function B(r){var t=r.keyword,e=r.symbol,n=r.value,o=[t.toUpperCase()];e&&o.push(e);var u=n;switch(t){case"partition by":case"default collate":u=ut(n);break;case"options":u="(".concat(n.map((function(r){return[r.keyword,r.symbol,ut(r.value)].join(" ")})).join(", "),")");break;case"cluster by":u=n.map(ut).join(", ")}return o.push(u),o.join(" ")}function Y(r){return function(r){if(Array.isArray(r))return $(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||function(r,t){if(!r)return;if("string"==typeof r)return $(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return $(r,t)}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function V(r){var t=r.array_index,e=r.arrows,n=void 0===e?[]:e,o=r.as,u=r.collate,a=r.column,s=r.db,i=r.isDual,c=r.schema,l=r.table,f=r.parentheses,p=r.properties,b=r.suffix,v=r.order_by,d=r.subFields,y=void 0===d?[]:d,E="*"===a?"*":function(r,t){if("string"==typeof r)return fr(r,t);var e=r.expr,n=r.offset,o=r.suffix,u=n&&n.map((function(r){return["[",r.name,"".concat(r.name?"(":""),dr(r.value),"".concat(r.name?")":""),"]"].filter(vr).join("")})).join("");return[ut(e),u,o].filter(vr).join("")}(a,i),h=[c,s,l].filter(vr).map((function(r){return"".concat(fr(r))})).join(".");h&&(E="".concat(h,".").concat(E)),t&&(E="".concat(E,"[").concat(dr(t.index),"]"),t.property&&(E="".concat(E,".").concat(dr(t.property))));var C=[E=[E].concat(Y(y)).join("."),er("AS",ut,o),n.map((function(r,t){return er(r,dr,p[t])})).join(" ")];u&&C.push(pr(u).join(" ")),C.push(br(b)),C.push(br(v));var L=C.filter(vr).join(" ");return f?"(".concat(L,")"):L}function W(r){var t=r||{},e=t.dataType,n=t.length,o=t.suffix,u=t.scale,a=t.expr,s=e;return null!=n&&(s+="(".concat([n,u].filter((function(r){return null!=r})).join(", "),")")),o&&o.length&&(s+=" ".concat(o.join(" "))),a&&(s+=ut(a)),s}function X(r){var t=[];if(!r)return t;var e=r.definition,n=r.keyword,o=r.match,u=r.table,a=r.on_action;return t.push(br(n)),t.push(H(u)),t.push(e&&"(".concat(e.map((function(r){return ut(r)})).join(", "),")")),t.push(br(o)),a.map((function(r){return t.push(br(r.type),ut(r.value))})),t.filter(vr)}function q(r){var t=r.column,e=r.collate,n=r.nulls,o=r.opclass,u=r.order_by;return[ut("string"==typeof t?{type:"column_ref",table:r.table,column:t}:r),er(e&&e.type,fr,e&&e.value),o,br(u),br(n)].filter(vr).join(" ")}function K(r){var t=[],e=V(r.column),n=W(r.definition);t.push(e),t.push(n);var o=function(r){var t=[],e=r.nullable,n=r.character_set,o=r.check,u=r.comment,a=r.collate,s=r.storage,i=r.default_val,c=r.auto_increment,l=r.unique,f=r.primary_key,b=r.column_format,v=r.reference_definition;if(t.push(br(e&&e.value)),i){var d=i.type,y=i.value;t.push(d.toUpperCase(),ut(y))}var E=sr().database;return t.push(p(o)),t.push(Tr(c),br(f),br(l),Lr(u)),t.push.apply(t,Y(pr(n))),"sqlite"!==E&&t.push.apply(t,Y(pr(a))),t.push.apply(t,Y(pr(b))),t.push.apply(t,Y(pr(s))),t.push.apply(t,Y(X(v))),t.filter(vr).join(" ")}(r);t.push(o);var u=function(r){if(r)return[br(r.value),"(".concat(ut(r.expr),")"),br(r.storage_type)].filter(vr).join(" ")}(r.generated);return t.push(u),t.filter(vr).join(" ")}function Q(r){return r?["AS",/^(`?)[a-z_][0-9a-z_]*(`?)$/i.test(r)?fr(r):lr(r)].join(" "):""}function J(r,t){var e=r.expr,n=r.type;if("cast"===n)return E(r);t&&(e.isDual=t);var o=ut(e),u=r.expr_list;if(u){var a=[o],s=u.map((function(r){return J(r,t)})).join(", ");return a.push([br(n),n&&"(",s,n&&")"].filter(vr).join("")),a.filter(vr).join(" ")}return e.parentheses&&Reflect.has(e,"array_index")&&(o="(".concat(o,")")),e.array_index&&"column_ref"!==e.type&&(o="".concat(o,"[").concat(dr(e.array_index.index),"]")),[o,Q(r.as)].filter(vr).join(" ")}function Z(r,t){if(!r||"*"===r)return r;var e=function(r){var t=Array.isArray(r)&&r[0];return!(!t||"dual"!==t.type)}(t);return r.map((function(r){return J(r,e)})).join(", ")}function z(r){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}e.r(n),e.d(n,"arrayStructTypeToSQL",(function(){return Cr})),e.d(n,"autoIncrementToSQL",(function(){return Tr})),e.d(n,"columnOrderListToSQL",(function(){return Sr})),e.d(n,"commonKeywordArgsToSQL",(function(){return Ar})),e.d(n,"commonOptionConnector",(function(){return er})),e.d(n,"connector",(function(){return nr})),e.d(n,"commonTypeValue",(function(){return pr})),e.d(n,"commentToSQL",(function(){return Lr})),e.d(n,"createBinaryExpr",(function(){return ur})),e.d(n,"createValueExpr",(function(){return or})),e.d(n,"dataTypeToSQL",(function(){return hr})),e.d(n,"DEFAULT_OPT",(function(){return rr})),e.d(n,"escape",(function(){return ar})),e.d(n,"literalToSQL",(function(){return dr})),e.d(n,"columnIdentifierToSql",(function(){return lr})),e.d(n,"getParserOpt",(function(){return sr})),e.d(n,"identifierToSql",(function(){return fr})),e.d(n,"onPartitionsToSQL",(function(){return Er})),e.d(n,"replaceParams",(function(){return yr})),e.d(n,"returningToSQL",(function(){return mr})),e.d(n,"hasVal",(function(){return vr})),e.d(n,"setParserOpt",(function(){return ir})),e.d(n,"toUpper",(function(){return br})),e.d(n,"topToSQL",(function(){return cr})),e.d(n,"triggerEventToSQL",(function(){return wr}));var rr={database:"flinksql",type:"table",parseOptions:{}},tr=rr;function er(r,t,e){if(e)return r?"".concat(r.toUpperCase()," ").concat(t(e)):t(e)}function nr(r,t){if(t)return"".concat(r.toUpperCase()," ").concat(t)}function or(r){var t=z(r);if(Array.isArray(r))return{type:"expr_list",value:r.map(or)};if(null===r)return{type:"null",value:null};switch(t){case"boolean":return{type:"bool",value:r};case"string":return{type:"string",value:r};case"number":return{type:"number",value:r};default:throw new Error('Cannot convert value "'.concat(t,'" to SQL'))}}function ur(r,t,e){var n={operator:r,type:"binary_expr"};return n.left=t.type?t:or(t),"BETWEEN"===r||"NOT BETWEEN"===r?(n.right={type:"expr_list",value:[or(e[0]),or(e[1])]},n):(n.right=e.type?e:or(e),n)}function ar(r){return r}function sr(){return tr}function ir(r){tr=r}function cr(r){if(r){var t=r.value,e=r.percent,n=r.parentheses?"(".concat(t,")"):t,o="TOP ".concat(n);return e?"".concat(o," ").concat(e.toUpperCase()):o}}function lr(r){var t=sr().database;if(r)switch(t&&t.toLowerCase()){case"postgresql":case"db2":case"snowflake":case"noql":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"mysql":case"mariadb":case"bigquery":default:return"`".concat(r,"`")}}function fr(r,t){var e=sr().database;if(!0===t)return"'".concat(r,"'");if(r){if("*"===r)return r;switch(e&&e.toLowerCase()){case"mysql":case"mariadb":case"sqlite":return"`".concat(r,"`");case"postgresql":case"snowflake":case"noql":return'"'.concat(r,'"');case"transactsql":return"[".concat(r,"]");case"bigquery":case"db2":return r;default:return"`".concat(r,"`")}}}function pr(r){var t=[];if(!r)return t;var e=r.type,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(o.toUpperCase()),t}function br(r){if(r)return r.toUpperCase()}function vr(r){return r}function dr(r){if(r){var t=r.prefix,e=r.type,n=r.parentheses,o=r.suffix,u=r.value,a="string"==typeof r?r:u;switch(e){case"backticks_quote_string":a="`".concat(u,"`");break;case"string":a="'".concat(u,"'");break;case"regex_string":a='r"'.concat(u,'"');break;case"hex_string":a="X'".concat(u,"'");break;case"full_hex_string":a="0x".concat(u);break;case"natural_string":a="N'".concat(u,"'");break;case"bit_string":a="b'".concat(u,"'");break;case"double_quote_string":a='"'.concat(u,'"');break;case"single_quote_string":a="'".concat(u,"'");break;case"boolean":case"bool":a=u?"TRUE":"FALSE";break;case"null":a="NULL";break;case"star":a="*";break;case"param":a="".concat(t||":").concat(u),t=null;break;case"origin":a=u.toUpperCase();break;case"date":case"datetime":case"time":case"timestamp":a="".concat(e.toUpperCase()," '").concat(u,"'");break;case"var_string":a="N'".concat(u,"'")}var s=[];return t&&s.push(br(t)),s.push(a),o&&s.push("object"===z(o)&&o.collate?pr(o.collate).join(" "):br(o)),a=s.join(" "),n?"(".concat(a,")"):a}}function yr(r,t){return function r(t,e){return Object.keys(t).filter((function(r){var e=t[r];return Array.isArray(e)||"object"===z(e)&&null!==e})).forEach((function(n){var o=t[n];if("object"!==z(o)||"param"!==o.type)return r(o,e);if(void 0===e[o.value])throw new Error("no value for parameter :".concat(o.value," found"));return t[n]=or(e[o.value]),null})),t}(JSON.parse(JSON.stringify(r)),t)}function Er(r){var t=r.type,e=r.partitions;return[br(t),"(".concat(e.map((function(r){if("range"!==r.type)return dr(r);var t=r.start,e=r.end,n=r.symbol;return"".concat(dr(t)," ").concat(br(n)," ").concat(dr(e))})).join(", "),")")].join(" ")}function hr(r){var t=r.dataType,e=r.length,n=r.parentheses,o=r.scale,u=r.suffix,a="";return null!=e&&(a=o?"".concat(e,", ").concat(o):e),n&&(a="(".concat(a,")")),u&&u.length&&(a+=" ".concat(u.join(" "))),"".concat(t).concat(a)}function Cr(r){if(r){var t=r.dataType,e=r.definition,n=r.anglebracket,o=br(t);if("ARRAY"!==o&&"STRUCT"!==o)return o;var u=e&&e.map((function(r){return[r.field_name,Cr(r.field_type)].filter(vr).join(" ")})).join(", ");return n?"".concat(o,"<").concat(u,">"):"".concat(o," ").concat(u)}}function Lr(r){if(r){var t=[],e=r.keyword,n=r.symbol,o=r.value;return t.push(e.toUpperCase()),n&&t.push(n),t.push(dr(o)),t.join(" ")}}function wr(r){return r.map((function(r){var t=r.keyword,e=r.args,n=[br(t)];if(e){var o=e.keyword,u=e.columns;n.push(br(o),u.map(V).join(", "))}return n.join(" ")})).join(" OR ")}function mr(r){return r?["RETURNING",r.columns.map(J).filter(vr).join(", ")].join(" "):""}function Ar(r){return r?[br(r.keyword),br(r.args)]:[]}function Tr(r){if(r){if("string"==typeof r){var t=sr().database;switch(t&&t.toLowerCase()){case"sqlite":return"AUTOINCREMENT";default:return"AUTO_INCREMENT"}}var e=r.keyword,n=r.seed,o=r.increment,u=r.parentheses,a=br(e);return u&&(a+="(".concat(dr(n),", ").concat(dr(o),")")),a}}function Sr(r){if(r)return r.map(q).filter(vr).join(", ")}function Rr(r){var t=r.name,e=r.value;return["@".concat(t),"=",ut(e)].filter(vr).join(" ")}function Ir(r){var t=r.left,e=r.right,n=r.symbol,o=r.keyword;t.keyword=o;var u=ut(t),a=ut(e);return"".concat(u," ").concat(n," ").concat(a)}function Nr(r){var t,e,n,o,u=r.keyword,a=r.suffix,s="";switch(br(u)){case"BINLOG":e=(t=r).in,n=t.from,o=t.limit,s=[er("IN",dr,e&&e.right),er("FROM",H,n),A(o)].filter(vr).join(" ");break;case"CHARACTER":case"COLLATION":s=function(r){var t=r.expr;if(t)return"LIKE"===br(t.op)?er("LIKE",dr,t.right):er("WHERE",ut,t)}(r);break;case"COLUMNS":case"INDEXES":s=er("FROM",H,r.from);break;case"GRANTS":s=function(r){var t=r.for;if(t){var e=t.user,n=t.host,o=t.role_list,u="'".concat(e,"'");return n&&(u+="@'".concat(n,"'")),["FOR",u,o&&"USING",o&&o.map((function(r){return"'".concat(r,"'")})).join(", ")].filter(vr).join(" ")}}(r);break;case"CREATE":s=er("",F,r[a]);break;case"VAR":s=ot(r.var),u=""}return["SHOW",br(u),br(a),s].filter(vr).join(" ")}var _r={alter:function(r){var t=r.keyword;switch(void 0===t?"table":t){case"aggregate":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name,u=r.type,a=t.expr,s=t.orderby;return[br(u),br(n),[[fr(o.schema),fr(o.name)].filter(vr).join("."),"(".concat(a.map(Zr).join(", ")).concat(s?[" ORDER","BY",s.map(Zr).join(", ")].join(" "):"",")")].filter(vr).join(""),Jr(e)].filter(vr).join(" ")}(r);case"table":return function(r){var t=r.type,e=r.table,n=r.expr,o=void 0===n?[]:n,u=br(t),a=H(e),s=o.map(ut);return[u,"TABLE",a,s.join(", ")].filter(vr).join(" ")}(r);case"schema":return function(r){var t=r.expr,e=r.keyword,n=r.schema;return[br(r.type),br(e),fr(n),Jr(t)].filter(vr).join(" ")}(r);case"domain":case"type":return function(r){var t=r.expr,e=r.keyword,n=r.name;return[br(r.type),br(e),[fr(n.schema),fr(n.name)].filter(vr).join("."),Jr(t)].filter(vr).join(" ")}(r);case"function":return function(r){var t=r.args,e=r.expr,n=r.keyword,o=r.name;return[br(r.type),br(n),[[fr(o.schema),fr(o.name)].filter(vr).join("."),t&&"(".concat(t.expr?t.expr.map(Zr).join(", "):"",")")].filter(vr).join(""),Jr(e)].filter(vr).join(" ")}(r);case"view":return function(r){var t=r.type,e=r.columns,n=r.attributes,o=r.select,u=r.view,a=r.with,s=br(t),i=F(u),c=[s,"VIEW",i];e&&c.push("(".concat(e.map(V).join(", "),")"));n&&c.push("WITH ".concat(n.map(br).join(", ")));c.push("AS",R(o)),a&&c.push(br(a));return c.filter(vr).join(" ")}(r)}},analyze:function(r){var t=r.type,e=r.table;return[br(t),F(e)].join(" ")},attach:function(r){var t=r.type,e=r.database,n=r.expr,o=r.as,u=r.schema;return[br(t),br(e),ut(n),br(o),fr(u)].filter(vr).join(" ")},create:function(r){var t=r.keyword,e="";switch(t.toLowerCase()){case"aggregate":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,a=r.options,s=[br(t),br(e),br(n)],i=[fr(o.schema),o.name].filter(vr).join("."),c="".concat(u.expr.map(Zr).join(", ")).concat(u.orderby?[" ORDER","BY",u.orderby.map(Zr).join(", ")].join(" "):"");return s.push("".concat(i,"(").concat(c,")"),"(".concat(a.map(Qr).join(", "),")")),s.filter(vr).join(" ")}(r);break;case"table":e=function(r){var t=r.type,e=r.keyword,n=r.table,o=r.like,u=r.as,a=r.temporary,s=r.if_not_exists,i=r.create_definitions,c=r.table_options,l=r.ignore_replace,f=r.or_replace,p=r.query_expr,b=[br(t),br(f),br(a),br(e),br(s),H(n)];if(o){var v=o.type,d=H(o.table);return b.push(br(v),d),b.filter(vr).join(" ")}i&&b.push("(".concat(i.map(Xr).join(", "),")"));c&&b.push(c.map(B).join(" "));b.push(br(l),br(u)),p&&b.push(Or(p));return b.filter(vr).join(" ")}(r);break;case"trigger":e="constraint"===r.resource?function(r){var t=r.constraint,e=r.constraint_kw,n=r.deferrable,o=r.events,u=r.execute,a=r.for_each,s=r.from,i=r.location,c=r.keyword,l=r.or,f=r.type,p=r.table,b=r.when,v=[br(f),br(l),br(e),br(c),fr(t),br(i)],d=wr(o);v.push(d,"ON",F(p)),s&&v.push("FROM",F(s));v.push.apply(v,$r(Ar(n)).concat($r(Ar(a)))),b&&v.push(br(b.type),ut(b.cond));return v.push(br(u.keyword),h(u.expr)),v.filter(vr).join(" ")}(r):function(r){var t=r.definer,e=r.for_each,n=r.keyword,o=r.execute,u=r.type,a=r.table,s=r.if_not_exists,i=r.temporary,c=r.trigger,l=r.events,f=r.order,p=r.time,b=r.when,v=[br(u),br(i),t,br(n),br(s),F(c),br(p),l.map((function(r){var t=[br(r.keyword)],e=r.args;return e&&t.push(br(e.keyword),e.columns.map(V).join(", ")),t.join(" ")})),"ON",F(a),br(e&&e.keyword),br(e&&e.args),f&&"".concat(br(f.keyword)," ").concat(fr(f.trigger)),er("WHEN",ut,b),br(o.prefix)];switch(o.type){case"set":v.push(er("SET",_,o.expr));break;case"multiple":v.push(gr(o.expr.ast))}return v.push(br(o.suffix)),v.filter(vr).join(" ")}(r);break;case"extension":e=function(r){var t=r.extension,e=r.from,n=r.if_not_exists,o=r.keyword,u=r.schema,a=r.type,s=r.with,i=r.version;return[br(a),br(o),br(n),dr(t),br(s),er("SCHEMA",dr,u),er("VERSION",dr,i),er("FROM",dr,e)].filter(vr).join(" ")}(r);break;case"function":e=function(r){var t=r.type,e=r.replace,n=r.keyword,o=r.name,u=r.args,a=r.returns,s=r.options,i=r.last,c=[br(t),br(e),br(n)],l=[fr(o.schema),o.name].filter(vr).join("."),f=u.map(Zr).filter(vr).join(", ");return c.push("".concat(l,"(").concat(f,")"),function(r){var t=r.type,e=r.keyword,n=r.expr;return[br(t),br(e),Array.isArray(n)?"(".concat(n.map(K).join(", "),")"):qr(n)].filter(vr).join(" ")}(a),s.map(Kr).join(" "),i),c.filter(vr).join(" ")}(r);break;case"index":e=function(r){var t=r.concurrently,e=r.filestream_on,n=r.keyword,o=r.include,u=r.index_columns,s=r.index_type,c=r.index_using,l=r.index,f=r.on,p=r.index_options,b=r.algorithm_option,v=r.lock_option,d=r.on_kw,y=r.table,E=r.tablespace,h=r.type,C=r.where,L=r.with,w=r.with_before_where,m=L&&"WITH (".concat(i(L).join(", "),")"),A=o&&"".concat(br(o.keyword)," (").concat(o.columns.map((function(r){return fr(r)})).join(", "),")"),T=[br(h),br(s),br(n),br(t),fr(l),br(d),F(y)].concat($r(a(c)),["(".concat(Sr(u),")"),A,i(p).join(" "),Jr(b),Jr(v),er("TABLESPACE",dr,E)]);w?T.push(m,er("WHERE",ut,C)):T.push(er("WHERE",ut,C),m);return T.push(er("ON",ut,f),er("FILESTREAM_ON",dr,e)),T.filter(vr).join(" ")}(r);break;case"sequence":e=function(r){var t=r.type,e=r.keyword,n=r.sequence,o=r.temporary,u=r.if_not_exists,a=r.create_definitions,s=[br(t),br(o),br(e),br(u),H(n)];a&&s.push(a.map(Xr).join(" "));return s.filter(vr).join(" ")}(r);break;case"database":e=function(r){var t=r.type,e=r.keyword,n=r.database,o=r.if_not_exists,u=r.create_definitions,a=[br(t),br(e),br(o),lr(n)];u&&a.push(u.map(B).join(" "));return a.filter(vr).join(" ")}(r);break;case"view":e=function(r){var t=r.algorithm,e=r.columns,n=r.definer,o=r.if_not_exists,u=r.keyword,a=r.recursive,s=r.replace,i=r.select,c=r.sql_security,l=r.temporary,f=r.type,p=r.view,b=r.with,v=r.with_options,d=p.db,y=p.view,E=[fr(d),fr(y)].filter(vr).join(".");return[br(f),br(s),br(l),br(a),t&&"ALGORITHM = ".concat(br(t)),n,c&&"SQL SECURITY ".concat(br(c)),br(u),br(o),E,e&&"(".concat(e.map(lr).join(", "),")"),v&&["WITH","(".concat(v.map((function(r){return pr(r).join(" ")})).join(", "),")")].join(" "),"AS",Or(i),br(b)].filter(vr).join(" ")}(r);break;case"domain":e=function(r){var t=r.as,e=r.domain,n=r.type,o=r.keyword,u=r.target,a=r.create_definitions,s=[br(n),br(o),[fr(e.schema),fr(e.name)].filter(vr).join("."),br(t),hr(u)];if(a&&a.length>0){var i,c=[],l=function(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Vr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}(a);try{for(l.s();!(i=l.n()).done;){var f=i.value,b=f.type;switch(b){case"collate":c.push(pr(f).join(" "));break;case"default":c.push(br(b),ut(f.value));break;case"constraint":c.push(p(f))}}}catch(r){l.e(r)}finally{l.f()}s.push(c.filter(vr).join(" "))}return s.filter(vr).join(" ")}(r);break;case"type":e=function(r){var t=r.as,e=r.create_definitions,n=r.keyword,o=r.name,u=r.resource,a=[br(r.type),br(n),[fr(o.schema),fr(o.name)].filter(vr).join("."),br(t),br(u)];if(e){var s=[];switch(u){case"enum":s.push(ut(e))}a.push(s.filter(vr).join(" "))}return a.filter(vr).join(" ")}(r);break;case"user":e=function(r){var t=r.attribute,e=r.comment,n=r.default_role,o=r.if_not_exists,u=r.keyword,a=r.lock_option,s=r.password_options,i=r.require,c=r.resource_options,l=r.type,f=r.user.map((function(r){var t=r.user,e=r.auth_option,n=[Br(t)];return e&&n.push(br(e.keyword),e.auth_plugin,dr(e.value)),n.filter(vr).join(" ")})).join(", "),p=[br(l),br(u),br(o),f];n&&p.push(br(n.keyword),n.value.map(Br).join(", "));p.push(er(i&&i.keyword,ut,i&&i.value)),c&&p.push(br(c.keyword),c.value.map((function(r){return ut(r)})).join(" "));s&&s.forEach((function(r){return p.push(er(r.keyword,ut,r.value))}));return p.push(dr(a),Lr(e),dr(t)),p.filter(vr).join(" ")}(r);break;default:throw new Error("unknown create resource ".concat(t))}return e},select:R,deallocate:function(r){var t=r.type,e=r.keyword,n=r.expr;return[br(t),br(e),ut(n)].filter(vr).join(" ")},delete:function(r){var t=r.columns,e=r.from,n=r.table,o=r.where,u=r.orderby,a=r.with,s=r.limit,i=[T(a),"DELETE"],c=Z(t,e);return i.push(c),Array.isArray(n)&&(1===n.length&&!0===n[0].addition||i.push(H(n))),i.push(er("FROM",H,e)),i.push(er("WHERE",ut,o)),i.push(st(u,"order by")),i.push(A(s)),i.filter(vr).join(" ")},exec:function(r){var t=r.keyword,e=r.module,n=r.parameters;return[br(t),F(e),(n||[]).map(Rr).filter(vr).join(", ")].filter(vr).join(" ")},execute:function(r){var t=r.type,e=r.name,n=r.args,o=[br(t)],u=[e];n&&u.push("(".concat(ut(n).join(", "),")"));return o.push(u.join("")),o.filter(vr).join(" ")},for:function(r){var t=r.type,e=r.label,n=r.target,o=r.query,u=r.stmts;return[e,br(t),n,"IN",gr([o]),"LOOP",gr(u),"END LOOP",e].filter(vr).join(" ")},update:function(r){var t=r.from,e=r.table,n=r.set,o=r.where,u=r.orderby,a=r.with,s=r.limit,i=r.returning;return[T(a),"UPDATE",H(e),er("SET",_,n),er("FROM",H,t),er("WHERE",ut,o),st(u,"order by"),A(s),mr(i)].filter(vr).join(" ")},if:function(r){var t=r.boolean_expr,e=r.else_expr,n=r.elseif_expr,o=r.if_expr,u=r.prefix,a=r.go,s=r.semicolons,i=r.suffix,c=[br(r.type),ut(t),dr(u),"".concat(kr(o.ast||o)).concat(s[0]),br(a)];n&&c.push(n.map((function(r){return[br(r.type),ut(r.boolean_expr),"THEN",kr(r.then.ast||r.then),r.semicolon].filter(vr).join(" ")})).join(" "));e&&c.push("ELSE","".concat(kr(e.ast||e)).concat(s[1]));return c.push(dr(i)),c.filter(vr).join(" ")},insert:k,drop:Fr,truncate:Fr,replace:k,declare:function(r){var t=r.type,e=r.declare,n=r.symbol,o=[br(t)],u=e.map((function(r){var t=r.at,e=r.name,n=r.as,o=r.constant,u=r.datatype,a=r.not_null,s=r.prefix,i=r.definition,c=r.keyword,l=[[t,e].filter(vr).join(""),br(n),br(o)];switch(c){case"variable":l.push.apply(l,[W(u)].concat(Dr(pr(r.collate)),[br(a)])),i&&l.push(br(i.keyword),ut(i.value));break;case"cursor":l.push(br(s));break;case"table":l.push(br(s),"(".concat(i.map(Xr).join(", "),")"))}return l.filter(vr).join(" ")})).join("".concat(n," "));return o.push(u),o.join(" ")},use:function(r){var t=r.type,e=r.db,n=br(t),o=fr(e);return"".concat(n," ").concat(o)},rename:function(r){var t=r.type,e=r.table,n=[],o="".concat(t&&t.toUpperCase()," TABLE");if(e){var u,a=Mr(e);try{for(a.s();!(u=a.n()).done;){var s=u.value.map(F);n.push(s.join(" TO "))}}catch(r){a.e(r)}finally{a.f()}}return"".concat(o," ").concat(n.join(", "))},call:function(r){var t=ut(r.expr);return"".concat("CALL"," ").concat(t)},desc:function(r){var t=r.type,e=r.table,n=br(t);return"".concat(n," ").concat(fr(e))},set:function(r){var t=r.expr,e=ut(t);return"".concat("SET"," ").concat(e)},lock:Hr,unlock:Hr,show:Nr,grant:Yr,revoke:Yr,proc:function(r){var t=r.stmt;switch(t.type){case"assign":return Ir(t);case"return":return function(r){var t=r.type,e=r.expr;return[br(t),ut(e)].join(" ")}(t)}},raise:function(r){var t=r.type,e=r.level,n=r.raise,o=r.using,u=[br(t),br(e)];n&&u.push([dr(n.keyword),"format"===n.type&&n.expr.length>0&&","].filter(vr).join(""),n.expr.map((function(r){return ut(r)})).join(", "));o&&u.push(br(o.type),br(o.option),o.symbol,o.expr.map((function(r){return ut(r)})).join(", "));return u.filter(vr).join(" ")},transaction:function(r){return ut(r.expr)}};function Or(r){if(!r)return"";for(var t=_r[r.type],e=r,n=e._parentheses,o=e._orderby,u=e._limit,a=[n&&"(",t(r)];r._next;){var s=_r[r._next.type],i=br(r.set_op);a.push(i,s(r._next)),r=r._next}return a.push(n&&")",st(o,"order by"),A(u)),a.filter(vr).join(" ")}function gr(r){for(var t=[],e=0,n=r.length;e<n;++e){var o=r[e]&&r[e].ast?r[e].ast:r[e],u=Or(o);e===n-1&&"transaction"===o.type&&(u="".concat(u," ;")),t.push(u)}return t.join(" ; ")}var jr=["analyze","attach","select","deallocate","delete","exec","update","insert","drop","rename","truncate","call","desc","use","alter","set","create","lock","unlock","declare","show","replace","if","grant","revoke","proc","raise","execute","transaction"];function xr(r){var t=r&&r.ast?r.ast:r;if(!jr.includes(t.type))throw new Error("".concat(t.type," statements not supported at the moment"))}function Ur(r){return Array.isArray(r)?(r.forEach(xr),gr(r)):(xr(r),Or(r))}function kr(r){return"go"===r.go?function r(t){if(!t||0===t.length)return"";var e=[Ur(t.ast)];return t.go_next&&e.push(t.go.toUpperCase(),r(t.go_next)),e.filter((function(r){return r})).join(" ")}(r):Ur(r)}function Mr(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=Pr(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function Dr(r){return function(r){if(Array.isArray(r))return Gr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Pr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pr(r,t){if(r){if("string"==typeof r)return Gr(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Gr(r,t):void 0}}function Gr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function Fr(r){var t=r.type,e=r.keyword,n=r.name,o=r.prefix,u=[br(t),br(e),br(o)];switch(e){case"table":u.push(H(n));break;case"trigger":u.push([n[0].schema?"".concat(fr(n[0].schema),"."):"",fr(n[0].trigger)].filter(vr).join(""));break;case"database":case"schema":case"procedure":u.push(fr(n));break;case"view":u.push(H(n),r.options&&r.options.map(ut).filter(vr).join(" "));break;case"index":u.push.apply(u,[V(n)].concat(Dr(r.table?["ON",F(r.table)]:[]),[r.options&&r.options.map(ut).filter(vr).join(" ")]))}return u.filter(vr).join(" ")}function Hr(r){var t=r.type,e=r.keyword,n=r.tables,o=[t.toUpperCase(),br(e)];if("UNLOCK"===t.toUpperCase())return o.join(" ");var u,a=[],s=Mr(n);try{var i=function(){var r=u.value,t=r.table,e=r.lock_type,n=[F(t)];if(e){n.push(["prefix","type","suffix"].map((function(r){return br(e[r])})).filter(vr).join(" "))}a.push(n.join(" "))};for(s.s();!(u=s.n()).done;)i()}catch(r){s.e(r)}finally{s.f()}return o.push.apply(o,[a.join(", ")].concat(Dr(function(r){var t=r.lock_mode,e=r.nowait,n=[];if(t){var o=t.mode;n.push(o.toUpperCase())}return e&&n.push(e.toUpperCase()),n}(r)))),o.filter(vr).join(" ")}function Br(r){var t=r.name,e=r.host,n=[dr(t)];return e&&n.push("@",dr(e)),n.join("")}function Yr(r){var t=r.type,e=r.grant_option_for,n=r.keyword,o=r.objects,u=r.on,a=r.to_from,s=r.user_or_roles,i=r.with,c=[br(t),dr(e)],l=o.map((function(r){var t=r.priv,e=r.columns,n=[ut(t)];return e&&n.push("(".concat(e.map(V).join(", "),")")),n.join(" ")})).join(", ");if(c.push(l),u)switch(c.push("ON"),n){case"priv":c.push(dr(u.object_type),u.priv_level.map((function(r){return[fr(r.prefix),fr(r.name)].filter(vr).join(".")})).join(", "));break;case"proxy":c.push(Br(u))}return c.push(br(a),s.map(Br).join(", ")),c.push(dr(i)),c.filter(vr).join(" ")}function $r(r){return function(r){if(Array.isArray(r))return Wr(r)}(r)||function(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}(r)||Vr(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Vr(r,t){if(r){if("string"==typeof r)return Wr(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);return"Object"===e&&r.constructor&&(e=r.constructor.name),"Map"===e||"Set"===e?Array.from(r):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?Wr(r,t):void 0}}function Wr(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function Xr(r){if(!r)return[];var t,e,n,u,a=r.resource;switch(a){case"column":return K(r);case"index":return e=[],n=(t=r).keyword,u=t.index,e.push(br(n)),e.push(u),e.push.apply(e,o(c(t))),e.filter(vr).join(" ");case"constraint":return p(r);case"sequence":return[br(r.prefix),ut(r.value)].filter(vr).join(" ");default:throw new Error("unknown resource = ".concat(a," type"))}}function qr(r){return r.dataType?hr(r):[fr(r.db),fr(r.schema),fr(r.table)].filter(vr).join(".")}function Kr(r){var t=r.type;switch(t){case"as":return[br(t),r.symbol,Or(r.declare),br(r.begin),gr(r.expr),br(r.end),r.symbol].filter(vr).join(" ");case"set":return[br(t),r.parameter,br(r.value&&r.value.prefix),r.value&&r.value.expr.map(ut).join(", ")].filter(vr).join(" ");default:return ut(r)}}function Qr(r){var t=r.type,e=r.symbol,n=r.value,o=[br(t),e];switch(br(t)){case"SFUNC":o.push([fr(n.schema),n.name].filter(vr).join("."));break;case"STYPE":case"MSTYPE":o.push(hr(n));break;default:o.push(ut(n))}return o.filter(vr).join(" ")}function Jr(r){if(!r)return"";var t=r.action,e=r.create_definitions,n=r.first_after,o=r.if_not_exists,u=r.keyword,a=r.old_column,s=r.prefix,i=r.resource,l=r.symbol,f="",p=[];switch(i){case"column":p=[K(r)];break;case"index":p=c(r),f=r[i];break;case"table":case"schema":f=fr(r[i]);break;case"aggregate":case"function":case"domain":case"type":f=fr(r[i]);break;case"algorithm":case"lock":case"table-option":f=[l,br(r[i])].filter(vr).join(" ");break;case"constraint":f=fr(r[i]),p=[Xr(e)];break;case"key":f=fr(r[i]);break;default:f=[l,r[i]].filter((function(r){return null!==r})).join(" ")}return[br(t),br(u),br(o),a&&V(a),br(s),f&&f.trim(),p.filter(vr).join(" "),n&&"".concat(br(n.keyword)," ").concat(V(n.column))].filter(vr).join(" ")}function Zr(r){var t=r.default&&[br(r.default.keyword),ut(r.default.value)].join(" ");return[br(r.mode),r.name,hr(r.type),t].filter(vr).join(" ")}function zr(r){return(zr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function rt(r){var t=r.expr_list;switch(br(r.type)){case"STRUCT":return"(".concat(Z(t),")");case"ARRAY":return function(r){var t=r.array_path,e=r.brackets,n=r.expr_list,o=r.parentheses;if(!n)return"[".concat(Z(t),"]");var u=Array.isArray(n)?n.map((function(r){return"(".concat(Z(r),")")})).filter(vr).join(", "):ut(n);return e?"[".concat(u,"]"):o?"(".concat(u,")"):u}(r);default:return""}}function tt(r){var t=r.definition,e=[br(r.keyword)];return t&&"object"===zr(t)&&(e.length=0,e.push(Cr(t))),e.push(rt(r)),e.filter(vr).join("")}function et(r){return(et="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var nt={alter:Jr,aggr_func:function(r){var t=r.args,e=r.filter,n=r.over,o=r.within_group_orderby,u=ut(t.expr),a=r.name,s=y(n);t.distinct&&(u=["DISTINCT",u].join(" ")),t.orderby&&(u="".concat(u," ").concat(st(t.orderby,"order by"))),t.separator&&(u=[u,br(t.separator.keyword),dr(t.separator.value)].filter(vr).join(" "));var i=o?"WITHIN GROUP (".concat(st(o,"order by"),")"):"",c=e?"FILTER (WHERE ".concat(ut(e.where),")"):"";return["".concat(a,"(").concat(u,")"),i,s,c].filter(vr).join(" ")},any_value:function(r){var t=r.args,e=r.type,n=r.over,o=t.expr,u=t.having,a="".concat(br(e),"(").concat(ut(o));return u&&(a="".concat(a," HAVING ").concat(br(u.prefix)," ").concat(ut(u.expr))),[a="".concat(a,")"),y(n)].filter(vr).join(" ")},window_func:function(r){var t=r.over;return[d(r),y(t)].filter(vr).join(" ")},array:tt,assign:Ir,binary_expr:C,case:function(r){var t=["CASE"],e=r.args,n=r.expr,o=r.parentheses;n&&t.push(ut(n));for(var u=0,a=e.length;u<a;++u)t.push(e[u].type.toUpperCase()),e[u].cond&&(t.push(ut(e[u].cond)),t.push("THEN")),t.push(ut(e[u].result));return t.push("END"),o?"(".concat(t.join(" "),")"):t.join(" ")},cast:E,column_ref:V,column_definition:K,datatype:hr,extract:function(r){var t=r.args,e=r.type,n=t.field,o=t.cast_type,u=t.source,a=["".concat(br(e),"(").concat(br(n)),"FROM",br(o),ut(u)];return"".concat(a.filter(vr).join(" "),")")},flatten:function(r){var t=r.args,e=r.type,n=["input","path","outer","recursive","mode"].map((function(r){return function(r){if(!r)return"";var t=r.type,e=r.symbol,n=r.value;return[br(t),e,ut(n)].filter(vr).join(" ")}(t[r])})).filter(vr).join(", ");return"".concat(br(e),"(").concat(n,")")},fulltext_search:function(r){var t=r.against,e=r.as,n=r.columns,o=r.match,u=r.mode;return[[br(o),"(".concat(n.map((function(r){return V(r)})).join(", "),")")].join(" "),[br(t),["(",ut(r.expr),u&&" ".concat(dr(u)),")"].filter(vr).join("")].join(" "),Q(e)].filter(vr).join(" ")},function:h,insert:Or,interval:M,json:function(r){var t=r.keyword,e=r.expr_list;return[br(t),e.map((function(r){return ut(r)})).join(", ")].join(" ")},show:Nr,struct:tt,tablefunc:function(r){var t=r.as,e=r.name,n=r.args;return["".concat(e,"(").concat(ut(n).join(", "),")"),"AS",h(t)].join(" ")},tables:H,unnest:D,window:function(r){return r.expr.map(v).join(", ")}};function ot(r){var t=r.prefix,e=void 0===t?"@":t,n=r.name,o=r.members,u=r.keyword,a=r.quoted,s=r.suffix,i=[];u&&i.push(u);var c=o&&o.length>0?"".concat(n,".").concat(o.join(".")):n,l="".concat(e||"").concat(c);return s&&(l+=s),i.push(l),[a,i.join(" "),a].filter(vr).join("")}function ut(r){if(r){var t=r;if(r.ast){var e=t.ast;Reflect.deleteProperty(t,e);for(var n=0,o=Object.keys(e);n<o.length;n++){var u=o[n];t[u]=e[u]}}return nt[t.type]?nt[t.type](t):dr(t)}}function at(r){return r?r.map(ut):[]}function st(r,t){if(!Array.isArray(r))return"";var e=[],n=br(t);switch(n){case"ORDER BY":e=r.map((function(r){return[ut(r.expr),r.type||"ASC",br(r.nulls)].filter(vr).join(" ")}));break;case"PARTITION BY":default:e=r.map((function(r){return ut(r.expr)}))}return nr(n,e.join(", "))}nt.var=ot,nt.expr_list=function(r){var t=at(r.value);return r.parentheses?"(".concat(t.join(", "),")"):t},nt.select=function(r){var t="object"===et(r._next)?Or(r):R(r);return r.parentheses?"(".concat(t,")"):t},nt.unary_expr=function(r){var t=r.operator,e=r.parentheses,n=r.expr,o="-"===t||"+"===t||"~"===t||"!"===t?"":" ",u="".concat(t).concat(o).concat(ut(n));return e?"(".concat(u,")"):u};var it=e(0);function ct(r){return(ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}var lt,ft,pt,bt,vt=(lt={},ft="flinksql",pt=it.parse,bt=function(r,t){if("object"!=ct(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=ct(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(ft,"string"),(ft="symbol"==ct(bt)?bt:String(bt))in lt?Object.defineProperty(lt,ft,{value:pt,enumerable:!0,configurable:!0,writable:!0}):lt[ft]=pt,lt);function dt(r){return(dt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}function yt(r,t){var e="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!e){if(Array.isArray(r)||(e=function(r,t){if(!r)return;if("string"==typeof r)return Et(r,t);var e=Object.prototype.toString.call(r).slice(8,-1);"Object"===e&&r.constructor&&(e=r.constructor.name);if("Map"===e||"Set"===e)return Array.from(r);if("Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e))return Et(r,t)}(r))||t&&r&&"number"==typeof r.length){e&&(r=e);var n=0,o=function(){};return{s:o,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var u,a=!0,s=!1;return{s:function(){e=e.call(r)},n:function(){var r=e.next();return a=r.done,r},e:function(r){s=!0,u=r},f:function(){try{a||null==e.return||e.return()}finally{if(s)throw u}}}}function Et(r,t){(null==t||t>r.length)&&(t=r.length);for(var e=0,n=new Array(t);e<t;e++)n[e]=r[e];return n}function ht(r,t){for(var e=0;e<t.length;e++){var n=t[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ct(n.key),n)}}function Ct(r){var t=function(r,t){if("object"!=dt(r)||!r)return r;var e=r[Symbol.toPrimitive];if(void 0!==e){var n=e.call(r,t||"default");if("object"!=dt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(r)}(r,"string");return"symbol"==dt(t)?t:String(t)}var Lt=function(){function r(){!function(r,t){if(!(r instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r)}var t,e,n;return t=r,(e=[{key:"astify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr,e=this.parse(r,t);return e&&e.ast}},{key:"sqlify",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr;return ir(t),kr(r)}},{key:"exprToSQL",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr;return ir(t),ut(r)}},{key:"parse",value:function(r){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:rr,e=t.database,n=void 0===e?"flinksql":e;ir(t);var o=n.toLowerCase();if(vt[o])return vt[o](r.trim(),t.parseOptions||rr.parseOptions);throw new Error("".concat(n," is not supported currently"))}},{key:"whiteListCheck",value:function(r,t){var e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:rr;if(t&&0!==t.length){var n=e.type,o=void 0===n?"table":n;if(!this["".concat(o,"List")]||"function"!=typeof this["".concat(o,"List")])throw new Error("".concat(o," is not valid check mode"));var u,a=this["".concat(o,"List")].bind(this),s=a(r,e),i=!0,c="",l=yt(s);try{for(l.s();!(u=l.n()).done;){var f,p=u.value,b=!1,v=yt(t);try{for(v.s();!(f=v.n()).done;){var d=f.value,y=new RegExp(d,"i");if(y.test(p)){b=!0;break}}}catch(r){v.e(r)}finally{v.f()}if(!b){c=p,i=!1;break}}}catch(r){l.e(r)}finally{l.f()}if(!i)throw new Error("authority = '".concat(c,"' is required in ").concat(o," whiteList to execute SQL = '").concat(r,"'"))}}},{key:"tableList",value:function(r,t){var e=this.parse(r,t);return e&&e.tableList}},{key:"columnList",value:function(r,t){var e=this.parse(r,t);return e&&e.columnList}}])&&ht(t.prototype,e),n&&ht(t,n),Object.defineProperty(t,"prototype",{writable:!1}),r}();function wt(r){return(wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(r){return typeof r}:function(r){return r&&"function"==typeof Symbol&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r})(r)}"object"===("undefined"==typeof self?"undefined":wt(self))&&self&&(self.NodeSQLParser={Parser:Lt,util:n}),"undefined"==typeof global&&"object"===("undefined"==typeof window?"undefined":wt(window))&&window&&(window.global=window),"object"===("undefined"==typeof global?"undefined":wt(global))&&global&&global.window&&(global.window.NodeSQLParser={Parser:Lt,util:n})}]));
//# sourceMappingURL=flinksql.js.map