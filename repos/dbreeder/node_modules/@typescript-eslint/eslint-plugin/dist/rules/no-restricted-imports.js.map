{"version": 3, "file": "no-restricted-imports.js", "sourceRoot": "", "sources": ["../../src/rules/no-restricted-imports.ts"], "names": [], "mappings": ";;;;;AACA,oDAA0D;AAY1D,oDAA4B;AAM5B,kCAAqC;AACrC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,uBAAuB,CAAC,CAAC;AAK5D,iFAAiF;AACjF,gFAAgF;AAChF,kDAAkD;AAClD,MAAM,SAAS,GAAG,CAAI,MAAe,EAAE,QAAW,EAAK,EAAE;IACvD,IAAI,CAAC;QACH,OAAO,MAAM,EAAE,CAAC;IAClB,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,QAAQ,CAAC;IAClB,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,MAwChC,CAAC;AAEF,MAAM,4BAA4B,GAA0C;IAC1E,gBAAgB,EAAE;QAChB,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,sDAAsD;KACpE;CACF,CAAC;AAEF,MAAM,uBAAuB,GAA2B;IACtD,IAAI,EAAE,OAAO;IACb,KAAK,EAAE;QACL,KAAK,EAAE;YACL,EAAE,IAAI,EAAE,QAAQ,EAAE;YAClB;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,GAAG,SAAS,CACV,GAAG,EAAE,CACH,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;yBACzD,UAAU,EACf,SAAS,CACV;oBACD,GAAG,4BAA4B;iBAChC;gBACD,QAAQ,EAAE,SAAS,CACjB,GAAG,EAAE,CACH,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;qBACzD,QAAQ,EACb,SAAS,CACV;aACF;SACF;KACF;IACD,WAAW,EAAE,IAAI;CAClB,CAAC;AAEF,MAAM,8BAA8B,GAA2B;IAC7D,KAAK,EAAE;QACL;YACE,IAAI,EAAE,OAAO;YACb,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;aACf;YACD,WAAW,EAAE,IAAI;SAClB;QACD;YACE,IAAI,EAAE,OAAO;YACb,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,GAAG,SAAS,CACV,GAAG,EAAE,CACH,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK;yBAC5D,UAAU,EACf,SAAS,CACV;oBACD,GAAG,4BAA4B;iBAChC;gBACD,QAAQ,EAAE,SAAS,CACjB,GAAG,EAAE,CACH,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK;qBAC5D,QAAQ,EACb,EAAE,CACH;aACF;YACD,WAAW,EAAE,IAAI;SAClB;KACF;CACF,CAAC;AAEF,MAAM,MAAM,GAA2B;IACrC,KAAK,EAAE;QACL,uBAAuB;QACvB;YACE,IAAI,EAAE,OAAO;YACb,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,KAAK,EAAE,uBAAuB;wBAC9B,QAAQ,EAAE,8BAA8B;qBACzC;oBACD,oBAAoB,EAAE,KAAK;iBAC5B;aACF;YACD,eAAe,EAAE,KAAK;SACvB;KACF;CACF,CAAC;AAEF,SAAS,eAAe,CACtB,GAAY;IAEZ,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAC5D,CAAC;AAED,SAAS,kBAAkB,CACzB,GAAY;IAEZ,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,8BAA8B,CACrC,OAAgB;IAEhB,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,kBAAkB,CAAC,OAAgB;IAC1C,IAAI,8BAA8B,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5C,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChC,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAC1B,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,qBAAqB,CAC5B,OAAgB;IAEhB,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACnC,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC7B,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,gBAAgB,CACvB,SAAuB,EACvB,OAAgB;IAEhB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAChE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAC,8BAA8B,CAAC,OAAO,CAAC,EAAE,CAAC;QAC7C,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACrE,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,uBAAuB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,oDAAoD;YACjE,eAAe,EAAE,IAAI;SACtB;QACD,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAChC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO;QAC9B,MAAM;KACP;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAE5B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,CAAC;YACtC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACpD,MAAM,4BAA4B,GAAG,IAAI,GAAG,EAAU,CAAC;QACvD,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;YAC7C,IACE,OAAO,cAAc,KAAK,QAAQ;gBAClC,cAAc,CAAC,gBAAgB,EAC/B,CAAC;gBACD,4BAA4B,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QACD,SAAS,uBAAuB,CAAC,YAAoB;YACnD,OAAO,4BAA4B,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAC1D,MAAM,yBAAyB,GAAa,EAAE,CAAC;QAC/C,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE,CAAC;YACnD,IACE,OAAO,iBAAiB,KAAK,QAAQ;gBACrC,iBAAiB,CAAC,gBAAgB,EAClC,CAAC;gBACD,sDAAsD;gBACtD,yBAAyB,CAAC,IAAI,CAC5B,IAAA,gBAAM,EAAC;oBACL,kBAAkB,EAAE,IAAI;oBACxB,UAAU,EAAE,CAAC,iBAAiB,CAAC,aAAa;iBAC7C,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAChC,CAAC;YACJ,CAAC;QACH,CAAC;QACD,SAAS,0BAA0B,CAAC,YAAoB;YACtD,OAAO;YACL,kEAAkE;YAClE,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CACzE,CAAC;QACJ,CAAC;QAED,SAAS,eAAe,CAAC,IAAgC;YACvD,IACE,IAAI,CAAC,UAAU,KAAK,MAAM;gBAC1B,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;oBACzB,IAAI,CAAC,UAAU,CAAC,KAAK,CACnB,SAAS,CAAC,EAAE,CACV,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;wBACjD,SAAS,CAAC,UAAU,KAAK,MAAM,CAClC,CAAC,EACJ,CAAC;gBACD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;gBAC9C,IACE,CAAC,uBAAuB,CAAC,YAAY,CAAC;oBACtC,CAAC,0BAA0B,CAAC,YAAY,CAAC,EACzC,CAAC;oBACD,OAAO,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,OAAO;YACL,yBAAyB,CACvB,IAAwC;gBAExC,IACE,IAAI,CAAC,eAAe,CAAC,IAAI;oBACvB,sBAAc,CAAC,yBAAyB;oBAC1C,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBAC/D,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,KAAK,QAAQ,EACzD,CAAC;oBACD,MAAM,iBAAiB,GAAG;wBACxB,GAAG,IAAI;wBACP,IAAI,EAAE,sBAAc,CAAC,iBAAiB;wBACtC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU;wBACvC,UAAU,EAAE,EAAE;wBACd,UAAU,EAAE,EAAE;wBACd,UAAU,EAAE;4BACV;gCACE,GAAG,IAAI,CAAC,EAAE;gCACV,IAAI,EAAE,sBAAc,CAAC,sBAAsB;gCAC3C,KAAK,EAAE,IAAI,CAAC,EAAE;6BACf;yBACF;qBACmC,CAAC;oBACvC,OAAO,eAAe,CAAC,iBAAiB,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;YACD,iBAAiB,EAAE,eAAe;YAClC,gCAAgC,CAC9B,IAEC;gBAED,IACE,IAAI,CAAC,UAAU,KAAK,MAAM;oBAC1B,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;wBACzB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,UAAU,KAAK,MAAM,CAAC,CAAC,EACtE,CAAC;oBACD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC9C,IACE,CAAC,uBAAuB,CAAC,YAAY,CAAC;wBACtC,CAAC,0BAA0B,CAAC,YAAY,CAAC,EACzC,CAAC;wBACD,OAAO,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;oBAC5C,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAO,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;YACD,oBAAoB,EAAE,KAAK,CAAC,oBAAoB;SACjD,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}