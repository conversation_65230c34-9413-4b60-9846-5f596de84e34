{"version": 3, "file": "explicit-function-return-type.js", "sourceRoot": "", "sources": ["../../src/rules/explicit-function-return-type.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAAsE;AAEtE,kCAAqC;AACrC,6EAIyC;AAgBzC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,8DAA8D;SACjE;QACD,QAAQ,EAAE;YACR,iBAAiB,EAAE,kCAAkC;SACtD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,oDAAoD,EAAE;wBACpD,WAAW,EACT,sEAAsE;wBACxE,IAAI,EAAE,SAAS;qBAChB;oBACD,gBAAgB,EAAE;wBAChB,WAAW,EACT,yFAAyF;wBAC3F,IAAI,EAAE,SAAS;qBAChB;oBACD,yBAAyB,EAAE;wBACzB,WAAW,EACT,gFAAgF;wBAClF,IAAI,EAAE,SAAS;qBAChB;oBACD,6BAA6B,EAAE;wBAC7B,WAAW,EACT,6EAA6E;wBAC/E,IAAI,EAAE,SAAS;qBAChB;oBACD,yCAAyC,EAAE;wBACzC,WAAW,EACT,6EAA6E;wBAC/E,IAAI,EAAE,SAAS;qBAChB;oBACD,mCAAmC,EAAE;wBACnC,WAAW,EACT,sEAAsE;wBACxE,IAAI,EAAE,SAAS;qBAChB;oBACD,YAAY,EAAE;wBACZ,WAAW,EACT,gGAAgG;wBAClG,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;wBACD,IAAI,EAAE,OAAO;qBACd;oBACD,UAAU,EAAE;wBACV,WAAW,EACT,qEAAqE;wBACvE,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,KAAK;YACvB,6BAA6B,EAAE,IAAI;YACnC,yBAAyB,EAAE,IAAI;YAC/B,yCAAyC,EAAE,IAAI;YAC/C,oDAAoD,EAAE,KAAK;YAC3D,mCAAmC,EAAE,KAAK;YAC1C,YAAY,EAAE,EAAE;YAChB,UAAU,EAAE,KAAK;SAClB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,SAAS,iBAAiB,CACxB,IAG+B;YAE/B,IAAI,OAAO,CAAC,mCAAmC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC;gBAClC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;gBACpD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAC/C,CAAC;gBACD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,IAAI,QAAQ,CAAC;gBACb,IAAI,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC;oBAClB,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;gBAC1B,CAAC;qBAAM,CAAC;oBACN,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;wBACpB,KAAK,sBAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC;4BACvC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;gCACjD,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;4BAC5B,CAAC;4BACD,MAAM;wBACR,CAAC;wBACD,KAAK,sBAAc,CAAC,gBAAgB,CAAC;wBACrC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;wBACvC,KAAK,sBAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;4BAC7B,IACE,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gCAC7C,CAAC,MAAM,CAAC,QAAQ,EAChB,CAAC;gCACD,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;4BAC7B,CAAC;4BACD,MAAM;wBACR,CAAC;oBACH,CAAC;gBACH,CAAC;gBACD,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC5D,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YACD,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBAChD,IAAI,CAAC,EAAE;gBACP,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAC7C,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,MAAM,CACb,IAG+B;YAE/B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;QAC5D,CAAC;QAED,OAAO;YACL,6CAA6C,CAC3C,IAAoE;gBAEpE,IACE,OAAO,CAAC,oDAAoD;oBAC5D,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;oBACpD,IAAI,CAAC,UAAU;oBACf,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBACjD,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,MAAM,EAC7B,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5B,OAAO;gBACT,CAAC;gBAED,IACE,OAAO,CAAC,6BAA6B;oBACrC,CAAC,IAAA,6DAAmC,EAAC,IAAI,EAAE,OAAO,CAAC;wBACjD,IAAA,+CAAqB,EAAC,IAAI,CAAC,CAAC,EAC9B,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,IAAA,iDAAuB,EAAC,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE,CACvD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG;oBACH,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CACH,CAAC;YACJ,CAAC;YACD,mBAAmB,CAAC,IAAI;gBACtB,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5B,OAAO;gBACT,CAAC;gBACD,IAAI,OAAO,CAAC,6BAA6B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;oBAC7D,OAAO;gBACT,CAAC;gBAED,IAAA,iDAAuB,EAAC,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE,CACvD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG;oBACH,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CACH,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}