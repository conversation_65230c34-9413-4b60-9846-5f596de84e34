{"version": 3, "file": "prefer-nullish-coalescing.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-nullish-coalescing.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAC3E,wEAAsE;AACtE,sDAAwC;AACxC,+CAAiC;AAEjC,kCAWiB;AAyBjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,2BAA2B;IACjC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,0FAA0F;YAC5F,WAAW,EAAE,WAAW;YACxB,oBAAoB,EAAE,IAAI;SAC3B;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,mBAAmB,EACjB,4GAA4G;YAC9G,wBAAwB,EACtB,4GAA4G;YAC9G,cAAc,EAAE,4CAA4C;YAC5D,iBAAiB,EACf,kGAAkG;SACrG;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,sDAAsD,EAAE;wBACtD,IAAI,EAAE,SAAS;qBAChB;oBACD,sBAAsB,EAAE;wBACtB,IAAI,EAAE,SAAS;qBAChB;oBACD,6BAA6B,EAAE;wBAC7B,IAAI,EAAE,SAAS;qBAChB;oBACD,gBAAgB,EAAE;wBAChB,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,QAAQ;gCACd,UAAU,EAAE;oCACV,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oCAC3B,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oCAC5B,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oCAC3B,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;iCAC5B;6BACF;4BACD;gCACE,IAAI,EAAE,SAAS;gCACf,IAAI,EAAE,CAAC,IAAI,CAAC;6BACb;yBACF;qBACF;oBACD,kBAAkB,EAAE;wBAClB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,sDAAsD,EAAE,KAAK;YAC7D,sBAAsB,EAAE,KAAK;YAC7B,kBAAkB,EAAE,KAAK;YACzB,6BAA6B,EAAE,KAAK;YACpC,gBAAgB,EAAE;gBAChB,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,KAAK;aACd;SACF;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,sDAAsD,EACtD,sBAAsB,EACtB,6BAA6B,EAC7B,gBAAgB,EAChB,kBAAkB,GACnB,EACF;QAED,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAClD,MAAM,eAAe,GAAG,cAAc,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACpE,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,kBAAkB,GAAG,OAAO,CAAC,6BAA6B,CAC9D,eAAe,EACf,kBAAkB,CACnB,CAAC;QAEF,IACE,CAAC,kBAAkB;YACnB,sDAAsD,KAAK,IAAI,EAC/D,CAAC;YACD,OAAO,CAAC,MAAM,CAAC;gBACb,GAAG,EAAE;oBACH,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;oBAC7B,GAAG,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;iBAC5B;gBACD,SAAS,EAAE,mBAAmB;aAC/B,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,qBAAqB,CAAC,IAAoC;gBACxD,IAAI,kBAAkB,EAAE,CAAC;oBACvB,OAAO;gBACT,CAAC;gBAED,IAAI,QAAiD,CAAC;gBACtD,IAAI,yBAAyB,GAAoB,EAAE,CAAC;gBACpD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE,CAAC;oBACvD,yBAAyB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAC9D,IACE,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;wBAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;wBAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;wBAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK,EAC5B,CAAC;wBACD,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAChC,CAAC;gBACH,CAAC;qBAAM,IACL,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACnD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACvD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EACxD,CAAC;oBACD,yBAAyB,GAAG;wBAC1B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;wBACnB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK;wBACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;wBACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;qBACtB,CAAC;oBACF,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;wBAChC,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,EAClC,CAAC;4BACD,QAAQ,GAAG,KAAK,CAAC;wBACnB,CAAC;6BAAM,IACL,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC;4BACnC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;4BACvC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,EACpC,CAAC;4BACD,QAAQ,GAAG,IAAI,CAAC;wBAClB,CAAC;oBACH,CAAC;yBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;wBACvC,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,EAClC,CAAC;4BACD,QAAQ,GAAG,KAAK,CAAC;wBACnB,CAAC;6BAAM,IACL,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK;4BACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC;4BACnC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,CAAC;4BACvC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI;gCAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,EACpC,CAAC;4BACD,QAAQ,GAAG,IAAI,CAAC;wBAClB,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO;gBACT,CAAC;gBAED,IAAI,UAAqC,CAAC;gBAC1C,IAAI,iBAAiB,GAAG,KAAK,CAAC;gBAC9B,IAAI,YAAY,GAAG,KAAK,CAAC;gBAEzB,0EAA0E;gBAC1E,KAAK,MAAM,QAAQ,IAAI,yBAAyB,EAAE,CAAC;oBACjD,IAAI,IAAA,oBAAa,EAAC,QAAQ,CAAC,EAAE,CAAC;wBAC5B,YAAY,GAAG,IAAI,CAAC;oBACtB,CAAC;yBAAM,IAAI,IAAA,4BAAqB,EAAC,QAAQ,CAAC,EAAE,CAAC;wBAC3C,iBAAiB,GAAG,IAAI,CAAC;oBAC3B,CAAC;yBAAM,IACL,CAAC,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,IAAI,CAAC;wBACzC,IAAA,kBAAW,EAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,EACtC,CAAC;wBACD,UAAU,GAAG,QAAQ,CAAC;oBACxB,CAAC;yBAAM,IACL,CAAC,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,IAAI,CAAC;wBACzC,IAAA,kBAAW,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,EACrC,CAAC;wBACD,UAAU,GAAG,QAAQ,CAAC;oBACxB,CAAC;yBAAM,CAAC;wBACN,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,OAAO;gBACT,CAAC;gBAED,MAAM,SAAS,GAAG,CAAC,GAAY,EAAE;oBAC/B,2EAA2E;oBAC3E,IAAI,iBAAiB,KAAK,YAAY,EAAE,CAAC;wBACvC,OAAO,iBAAiB,CAAC;oBAC3B,CAAC;oBAED,iEAAiE;oBACjE,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;wBAC3C,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBACpE,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;oBAC/C,MAAM,KAAK,GAAG,IAAA,mBAAY,EAAC,IAAI,CAAC,CAAC;oBAEjC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;wBACtD,OAAO,KAAK,CAAC;oBACf,CAAC;oBAED,MAAM,WAAW,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAEtD,uEAAuE;oBACvE,IAAI,iBAAiB,IAAI,CAAC,WAAW,EAAE,CAAC;wBACtC,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,MAAM,gBAAgB,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBAEhE,qEAAqE;oBACrE,OAAO,YAAY,IAAI,CAAC,gBAAgB,CAAC;gBAC3C,CAAC,CAAC,EAAE,CAAC;gBAEL,IAAI,SAAS,EAAE,CAAC;oBACd,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,0BAA0B;wBACrC,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,gBAAgB;gCAC3B,GAAG,CAAC,KAAyB;oCAC3B,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GACjB,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,IAAI;wCACrC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC;wCACnC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;oCACxC,OAAO,KAAK,CAAC,WAAW,CACtB,IAAI,EACJ,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CACtB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EACb,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CACd,OAAO,UAAU,CAAC,IAAI,CAAC,KAAK,CAC3B,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EACd,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACf,EAAE,CACJ,CAAC;gCACJ,CAAC;6BACF;yBACF;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,oCAAoC,CAClC,IAAgC;gBAEhC,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC9D,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACpD,IAAI,CAAC,IAAA,oBAAa,EAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC;oBACrE,OAAO;gBACT,CAAC;gBAED,IAAI,sBAAsB,KAAK,IAAI,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/D,OAAO;gBACT,CAAC;gBAED,MAAM,cAAc,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAC;gBACtD,IAAI,6BAA6B,KAAK,IAAI,IAAI,cAAc,EAAE,CAAC;oBAC7D,OAAO;gBACT,CAAC;gBAED,MAAM,cAAc,GAAG;oBACrB,CAAC,gBAAgB,KAAK,IAAI,IAAI,gBAAiB,CAAC,MAAM,CAAC;wBACrD,EAAE,CAAC,SAAS,CAAC,MAAM;oBACrB,CAAC,gBAAgB,KAAK,IAAI,IAAI,gBAAiB,CAAC,OAAO,CAAC;wBACtD,EAAE,CAAC,SAAS,CAAC,cAAc;oBAC7B,CAAC,gBAAgB,KAAK,IAAI,IAAI,gBAAiB,CAAC,MAAM,CAAC;wBACrD,EAAE,CAAC,SAAS,CAAC,MAAM;oBACrB,CAAC,gBAAgB,KAAK,IAAI,IAAI,gBAAiB,CAAC,MAAM,CAAC;wBACrD,EAAE,CAAC,SAAS,CAAC,MAAM;iBACtB;qBACE,MAAM,CAAC,CAAC,IAAI,EAAkB,EAAE,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC;qBAC1D,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,QAAQ,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC;gBAClD,IACE,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,SAAS,CAAC,IAAI;oBAChC,IAAI,CAAC,KAAK,KAAK,EAAE,CAAC,SAAS,CAAC,SAAS;oBACpC,IAAmC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAClD,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE,cAAc,CAAC,CACzC,EACD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,MAAM,cAAc,GAAG,IAAA,iBAAU,EAC/B,UAAU,CAAC,aAAa,CACtB,IAAI,CAAC,IAAI,EACT,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;oBACzC,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,CAChC,EACD,wBAAiB,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,CACtD,CAAC;gBAEF,QAAQ,CAAC,CAAC,GAAG,CACX,KAAyB;oBAEzB,IAAI,IAAA,0BAAmB,EAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;wBACrC,kFAAkF;wBAClF,IACE,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;4BACnD,CAAC,IAAA,0BAAmB,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EACpC,CAAC;4BACD,MAAM,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;wBACrD,CAAC;6BAAM,CAAC;4BACN,MAAM,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;wBAC/C,CAAC;wBACD,MAAM,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;oBAC/C,CAAC;oBACD,MAAM,KAAK,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;gBAChD,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,cAAc;oBACpB,SAAS,EAAE,qBAAqB;oBAChC,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,gBAAgB;4BAC3B,GAAG;yBACJ;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,iBAAiB,CAAC,IAAmB;IAC5C,MAAM,OAAO,GAAG,IAAI,GAAG,CAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;IACtD,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;IAC1B,OAAO,OAAO,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAErB,IACE,CAAC,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB;YACpD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;YAChD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;YAC3C,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY;YAC5C,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;YACjD,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EACzB,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IACE;YACE,sBAAc,CAAC,uBAAuB;YACtC,sBAAc,CAAC,kBAAkB;SAClC,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EACxB,CAAC;YACD;;;;eAIG;YACH,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;IAC3B,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,wBAAwB,CAAC,IAAgC;IAChE,MAAM,IAAI,GAAG,IAAI,GAAG,EAA6B,CAAC;IAClD,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IACnD,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACtB,SAAS;QACX,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAElB,IAAI,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;YACtD,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC;YACd,CAAC;iBAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACrC,sEAAsE;gBACtE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAC1D,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}