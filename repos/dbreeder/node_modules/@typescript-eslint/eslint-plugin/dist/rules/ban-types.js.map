{"version": 3, "file": "ban-types.js", "sourceRoot": "", "sources": ["../../src/rules/ban-types.ts"], "names": [], "mappings": ";;;AACA,oDAA0D;AAC1D,wEAAsE;AAEtE,kCAAsD;AAsBtD,SAAS,YAAY,CAAC,GAAW;IAC/B,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAChC,CAAC;AAED,SAAS,aAAa,CACpB,IAAmB,EACnB,UAA+B;IAE/B,OAAO,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,gBAAgB,CACvB,UAAyE;IAEzE,IAAI,UAAU,IAAI,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;QAC9C,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;QACnC,OAAO,IAAI,UAAU,EAAE,CAAC;IAC1B,CAAC;IAED,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,OAAO,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;IAClC,CAAC;IAED,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,MAAM,YAAY,GAAU;IAC1B,MAAM,EAAE;QACN,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,QAAQ;KAClB;IACD,OAAO,EAAE;QACP,OAAO,EAAE,qBAAqB;QAC9B,OAAO,EAAE,SAAS;KACnB;IACD,MAAM,EAAE;QACN,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,QAAQ;KAClB;IACD,MAAM,EAAE;QACN,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,QAAQ;KAClB;IACD,MAAM,EAAE;QACN,OAAO,EAAE,oBAAoB;QAC7B,OAAO,EAAE,QAAQ;KAClB;IAED,QAAQ,EAAE;QACR,OAAO,EAAE;YACP,sDAAsD;YACtD,6FAA6F;YAC7F,oHAAoH;YACpH,iHAAiH;SAClH,CAAC,IAAI,CAAC,IAAI,CAAC;KACb;IAED,gBAAgB;IAChB,MAAM,EAAE;QACN,OAAO,EAAE;YACP,sGAAsG;YACtG,gFAAgF;YAChF,gFAAgF;YAChF,gHAAgH;SACjH,CAAC,IAAI,CAAC,IAAI,CAAC;QACZ,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,sBAAsB,CAAC;KACvD;IACD,IAAI,EAAE;QACJ,OAAO,EAAE;YACP,8CAA8C;YAC9C,gFAAgF;YAChF,gFAAgF;YAChF,iGAAiG;YACjG,gHAAgH;SACjH,CAAC,IAAI,CAAC,IAAI,CAAC;QACZ,OAAO,EAAE;YACP,QAAQ;YACR,SAAS;YACT,uBAAuB;YACvB,sBAAsB;SACvB;KACF;CACF,CAAC;AAEW,QAAA,aAAa,GAAG;IAC3B,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,OAAO,EAAE,sBAAc,CAAC,gBAAgB;IACxC,KAAK,EAAE,sBAAc,CAAC,cAAc;IACpC,IAAI,EAAE,sBAAc,CAAC,aAAa;IAClC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,MAAM,EAAE,sBAAc,CAAC,eAAe;IACtC,SAAS,EAAE,sBAAc,CAAC,kBAAkB;IAC5C,OAAO,EAAE,sBAAc,CAAC,gBAAgB;IACxC,IAAI,EAAE,sBAAc,CAAC,aAAa;CACnC,CAAC;AAEF,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,WAAW;IACjB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,wBAAwB;YACrC,WAAW,EAAE,aAAa;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,iBAAiB,EAAE,kDAAkD;YACrE,qBAAqB,EAAE,4CAA4C;SACpE;QACD,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL,SAAS,EAAE;wBACT,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,MAAM;gCACZ,WAAW,EAAE,wCAAwC;6BACtD;4BACD;gCACE,IAAI,EAAE,SAAS;gCACf,IAAI,EAAE,CAAC,KAAK,CAAC;gCACb,WAAW,EACT,6DAA6D;6BAChE;4BACD;gCACE,IAAI,EAAE,SAAS;gCACf,IAAI,EAAE,CAAC,IAAI,CAAC;gCACZ,WAAW,EAAE,wCAAwC;6BACtD;4BACD;gCACE,IAAI,EAAE,QAAQ;gCACd,WAAW,EAAE,qCAAqC;6BACnD;4BACD;gCACE,IAAI,EAAE,QAAQ;gCACd,WAAW,EAAE,aAAa;gCAC1B,UAAU,EAAE;oCACV,OAAO,EAAE;wCACP,IAAI,EAAE,QAAQ;wCACd,WAAW,EAAE,sBAAsB;qCACpC;oCACD,OAAO,EAAE;wCACP,IAAI,EAAE,QAAQ;wCACd,WAAW,EACT,+HAA+H;qCAClI;oCACD,OAAO,EAAE;wCACP,IAAI,EAAE,OAAO;wCACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wCACzB,WAAW,EAAE,kCAAkC;wCAC/C,eAAe,EAAE,KAAK;qCACvB;iCACF;gCACD,oBAAoB,EAAE,KAAK;6BAC5B;yBACF;qBACF;iBACF;gBACD,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,oBAAoB,EAAE;4BACpB,IAAI,EAAE,2BAA2B;yBAClC;qBACF;oBACD,cAAc,EAAE;wBACd,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE,CAAC,EAAE,CAAC;IACpB,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC;QACtD,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CACzB,EAAE,EACF,cAAc,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,EAClC,WAAW,CACZ,CAAC;QACF,MAAM,WAAW,GAAG,IAAI,GAAG,CACzB,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CACxE,CAAC;QAEF,SAAS,gBAAgB,CACvB,QAAuB,EACvB,IAAI,GAAG,aAAa,CAAC,QAAQ,EAAE,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;YAEtD,MAAM,UAAU,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAEzC,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,KAAK,KAAK,EAAE,CAAC;gBACrD,OAAO;YACT,CAAC;YAED,MAAM,aAAa,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;YACnD,MAAM,OAAO,GACX,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC,OAAO,CAAC;YACrE,MAAM,OAAO,GACX,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ;gBAC1C,CAAC,CAAC,UAAU,CAAC,OAAO;gBACpB,CAAC,CAAC,SAAS,CAAC;YAEhB,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,mBAAmB;gBAC9B,IAAI,EAAE;oBACJ,IAAI;oBACJ,aAAa;iBACd;gBACD,GAAG,EAAE,OAAO;oBACV,CAAC,CAAC,CAAC,KAAK,EAAoB,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC;oBACnE,CAAC,CAAC,IAAI;gBACR,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;oBACpC,SAAS,EAAE,uBAAuB;oBAClC,IAAI,EAAE;wBACJ,IAAI;wBACJ,WAAW;qBACZ;oBACD,GAAG,EAAE,CAAC,KAAK,EAAoB,EAAE,CAC/B,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC;iBAC3C,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAA,sBAAe,EACtC,qBAAa,EACb,CAAC,GAA0B,EAAE,OAAO,EAAE,EAAE;YACtC,IAAI,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7B,GAAG,CAAC,qBAAa,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAmB,EAAQ,EAAE,CAC1D,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACpC,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,EACD,EAAE,CACH,CAAC;QAEF,OAAO;YACL,GAAG,gBAAgB;YAEnB,aAAa,CAAC,IAAI;gBAChB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;oBACxB,OAAO;gBACT,CAAC;gBAED,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YACD,WAAW,CAAC,IAAI;gBACd,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACnC,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAEhC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YACD,mBAAmB,CAAC,IAAI;gBACtB,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;YACD,iBAAiB,CAAC,IAAI;gBACpB,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}