#!/bin/bash

# DBReeder Development Environment Setup Script

set -e

echo "🚀 Setting up DBReeder development environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ .env file created. Please review and update the configuration."
else
    echo "✅ .env file already exists."
fi

# Create logs directory
mkdir -p logs
echo "✅ Logs directory created."

# Start development databases
echo "🐳 Starting development databases..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for databases to be ready
echo "⏳ Waiting for databases to be ready..."
sleep 30

# Check database health
echo "🔍 Checking database health..."
docker-compose -f docker-compose.dev.yml ps

# Install Node.js dependencies if not already installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
else
    echo "✅ Node.js dependencies already installed."
fi

# Build the application
echo "🔨 Building the application..."
npm run build

echo ""
echo "🎉 Development environment setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Review and update the .env file with your configuration"
echo "2. Start the development server: npm run dev"
echo "3. Access the application at: http://localhost:3000"
echo ""
echo "🗄️  Database connections:"
echo "- App Database (PostgreSQL): localhost:5434"
echo "- Redis: localhost:6380"
echo "- Target MySQL: localhost:3307"
echo "- Target PostgreSQL: localhost:5435"
echo ""
echo "🛠️  Useful commands:"
echo "- Start dev server: npm run dev"
echo "- Run tests: npm test"
echo "- View logs: docker-compose -f docker-compose.dev.yml logs -f"
echo "- Stop databases: docker-compose -f docker-compose.dev.yml down"
echo ""
