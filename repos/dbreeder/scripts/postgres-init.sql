-- PostgreSQL Target Database Initialization Script
-- This creates sample data for testing DBReeder functionality

-- Create sample tables
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    category_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS categories (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    total_amount DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id),
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample data
INSERT INTO categories (name, description) VALUES
('Electronics', 'Electronic devices and accessories'),
('Books', 'Physical and digital books'),
('Clothing', 'Apparel and accessories'),
('Home & Garden', 'Home improvement and garden supplies')
ON CONFLICT (name) DO NOTHING;

INSERT INTO users (username, email, first_name, last_name) VALUES
('john_doe', '<EMAIL>', 'John', 'Doe'),
('jane_smith', '<EMAIL>', 'Jane', 'Smith'),
('bob_wilson', '<EMAIL>', 'Bob', 'Wilson'),
('alice_brown', '<EMAIL>', 'Alice', 'Brown')
ON CONFLICT (username) DO NOTHING;

INSERT INTO products (name, description, price, category_id) VALUES
('Laptop Computer', 'High-performance laptop for professionals', 1299.99, 1),
('Programming Book', 'Learn advanced programming techniques', 49.99, 2),
('T-Shirt', 'Comfortable cotton t-shirt', 19.99, 3),
('Garden Tools Set', 'Complete set of garden tools', 89.99, 4),
('Smartphone', 'Latest model smartphone', 799.99, 1),
('Fiction Novel', 'Bestselling fiction novel', 14.99, 2)
ON CONFLICT DO NOTHING;

-- Create indexes for performance testing
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_orders_user ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items(order_id);

-- Create a view for testing
CREATE OR REPLACE VIEW user_order_summary AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    COUNT(o.id) as total_orders,
    COALESCE(SUM(o.total_amount), 0) as total_spent
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
GROUP BY u.id, u.username, u.email;

-- Grant permissions for testing
GRANT SELECT ON ALL TABLES IN SCHEMA public TO target_dev_user;
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO target_dev_user;
GRANT SELECT ON user_order_summary TO target_dev_user;
