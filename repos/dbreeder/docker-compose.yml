version: '3.8'

services:
  # Application Database (PostgreSQL)
  postgres:
    image: postgres:15-alpine
    container_name: dbreeder-postgres
    environment:
      POSTGRES_DB: dbreeder
      POSTGRES_USER: dbreeder_user
      POSTGRES_PASSWORD: secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docs/database-schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
    networks:
      - dbreeder-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dbreeder_user -d dbreeder"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: dbreeder-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - dbreeder-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # DBReeder Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dbreeder-app
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: dbreeder
      DB_USER: dbreeder_user
      DB_PASSWORD: secure_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      JWT_EXPIRES_IN: 24h
      BCRYPT_ROUNDS: 12
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - dbreeder-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  # Example Target Database (MySQL)
  mysql-target:
    image: mysql:8.0
    container_name: dbreeder-mysql-target
    environment:
      MYSQL_ROOT_PASSWORD: mysql_root_password
      MYSQL_DATABASE: target_db
      MYSQL_USER: target_user
      MYSQL_PASSWORD: target_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - dbreeder-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Example Target Database (PostgreSQL)
  postgres-target:
    image: postgres:15-alpine
    container_name: dbreeder-postgres-target
    environment:
      POSTGRES_DB: target_db
      POSTGRES_USER: target_user
      POSTGRES_PASSWORD: target_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_target_data:/var/lib/postgresql/data
    networks:
      - dbreeder-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U target_user -d target_db"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  mysql_data:
    driver: local
  postgres_target_data:
    driver: local

networks:
  dbreeder-network:
    driver: bridge
