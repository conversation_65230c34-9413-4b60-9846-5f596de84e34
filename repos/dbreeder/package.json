{"name": "dbreeder", "version": "1.0.0", "description": "Secure Database Management System for Developers", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "docker:build": "docker build -t dbreeder .", "docker:run": "docker run -p 3000:3000 dbreeder", "docker:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:dev:down": "docker-compose -f docker-compose.dev.yml down", "docker:dev:logs": "docker-compose -f docker-compose.dev.yml logs -f", "docker:prod": "docker-compose up -d", "docker:prod:down": "docker-compose down", "setup:dev": "./scripts/setup-dev.sh"}, "keywords": ["database", "security", "sql", "audit", "proxy", "rbac"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "node-sql-parser": "^4.17.0", "pg": "^8.11.3", "mysql2": "^3.6.5", "winston": "^3.11.0", "joi": "^17.11.0", "uuid": "^9.0.1", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "compression": "^1.7.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0"}, "engines": {"node": ">=18.0.0"}}