// Database type definitions for DBReeder

export enum UserRole {
  SPECIAL_ADMIN = 'special_admin',
  ADMIN = 'admin',
  DBA = 'dba',
  SQL_APPROVER = 'sql_approver',
  SQL_USER = 'sql_user'
}

export enum ApprovalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EXPIRED = 'expired'
}

export enum SqlCommandType {
  DQL = 'DQL', // Data Query Language (SELECT)
  DML = 'DML', // Data Manipulation Language (INSERT, UPDATE, DELETE)
  DDL = 'DDL', // Data Definition Language (CREATE, ALTER, DROP)
  DCL = 'DCL', // Data Control Language (GRANT, REVOKE)
  TCL = 'TCL'  // Transaction Control Language (COMMIT, ROLLBACK)
}

export enum AuditEventType {
  LOGIN = 'login',
  LOGOUT = 'logout',
  SQL_EXECUTE = 'sql_execute',
  SQL_APPROVE = 'sql_approve',
  SQL_REJECT = 'sql_reject',
  USER_CREATE = 'user_create',
  USER_UPDATE = 'user_update',
  TEAM_CREATE = 'team_create',
  PERMISSION_CHANGE = 'permission_change'
}

export enum DatabaseType {
  POSTGRESQL = 'postgresql',
  MYSQL = 'mysql',
  SQLITE = 'sqlite'
}

// Base interface for all database entities
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Team entity
export interface Team extends BaseEntity {
  name: string;
  description?: string;
  isActive: boolean;
}

// User entity
export interface User extends BaseEntity {
  username: string;
  email: string;
  passwordHash: string;
  firstName?: string;
  lastName?: string;
  isActive: boolean;
  lastLogin?: Date;
  passwordChangedAt: Date;
}

// User-Team-Role relationship
export interface UserTeamRole {
  id: string;
  userId: string;
  teamId: string;
  role: UserRole;
  grantedBy?: string;
  grantedAt: Date;
  isActive: boolean;
}

// Database connection configuration
export interface DatabaseConnection extends BaseEntity {
  teamId: string;
  name: string;
  databaseType: DatabaseType;
  host: string;
  port: number;
  databaseName: string;
  username: string;
  passwordEncrypted: string;
  sslEnabled: boolean;
  connectionParams?: Record<string, any>;
  isActive: boolean;
}

// SQL approval request
export interface SqlApprovalRequest extends BaseEntity {
  requesterId: string;
  teamId: string;
  databaseConnectionId: string;
  sqlCommand: string;
  commandType: SqlCommandType;
  justification?: string;
  status: ApprovalStatus;
  approvedBy?: string;
  approvedAt?: Date;
  rejectionReason?: string;
  expiresAt: Date;
}

// Audit log entry
export interface AuditLog {
  id: string;
  userId?: string;
  teamId?: string;
  eventType: AuditEventType;
  resourceType?: string;
  resourceId?: string;
  sqlCommand?: string;
  commandType?: SqlCommandType;
  databaseConnectionId?: string;
  executionTimeMs?: number;
  rowsAffected?: number;
  success: boolean;
  errorMessage?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  additionalData?: Record<string, any>;
  createdAt: Date;
}

// User session
export interface UserSession {
  id: string;
  userId: string;
  tokenHash: string;
  refreshTokenHash?: string;
  ipAddress?: string;
  userAgent?: string;
  expiresAt: Date;
  createdAt: Date;
  lastUsedAt: Date;
  isActive: boolean;
}

// System configuration
export interface SystemConfig {
  key: string;
  value: any;
  description?: string;
  updatedBy?: string;
  updatedAt: Date;
}

// User permissions view
export interface UserPermission {
  userId: string;
  username: string;
  email: string;
  teamId: string;
  teamName: string;
  role: UserRole;
  grantedAt: Date;
  grantedBy?: string;
}

// DTOs for API requests/responses
export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface CreateTeamRequest {
  name: string;
  description?: string;
}

export interface CreateDatabaseConnectionRequest {
  teamId: string;
  name: string;
  databaseType: DatabaseType;
  host: string;
  port: number;
  databaseName: string;
  username: string;
  password: string;
  sslEnabled?: boolean;
  connectionParams?: Record<string, any>;
}

export interface SqlExecutionRequest {
  databaseConnectionId: string;
  sqlCommand: string;
  justification?: string;
}

export interface SqlExecutionResult {
  success: boolean;
  data?: any[];
  rowsAffected?: number;
  executionTimeMs: number;
  message?: string;
  error?: string;
}

export interface ApprovalRequest {
  requestId: string;
  approved: boolean;
  rejectionReason?: string;
}

// Authentication DTOs
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: Omit<User, 'passwordHash'>;
  token: string;
  refreshToken: string;
  permissions: UserPermission[];
}

export interface JwtPayload {
  userId: string;
  username: string;
  email: string;
  sessionId: string;
  iat: number;
  exp: number;
}

// Permission checking
export interface PermissionContext {
  userId: string;
  teamId: string;
  role: UserRole;
  resourceType?: string;
  resourceId?: string;
}

// Query filters and pagination
export interface PaginationOptions {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface AuditLogFilter {
  userId?: string;
  teamId?: string;
  eventType?: AuditEventType;
  startDate?: Date;
  endDate?: Date;
  success?: boolean;
}

export interface SqlApprovalRequestFilter {
  requesterId?: string;
  teamId?: string;
  status?: ApprovalStatus;
  commandType?: SqlCommandType;
  startDate?: Date;
  endDate?: Date;
}
